package online.yllh.smartinspect.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.io.Serializable

@Entity(tableName = "recycled_photos")
data class RecycledPhoto(
    @PrimaryKey(autoGenerate = true)
    val id: Int = 0,
    val uri: String,
    val cachePath: String,
    val shotTime: Long,
    val longitude: Double,
    val latitude: Double,
    val deleteTime: Long, // 删除时间戳
) : Serializable {
    
    /**
     * 计算剩余清理时间（天数）
     */
    fun getRemainingDays(): Int {
        val currentTime = System.currentTimeMillis()
        val daysPassed = (currentTime - deleteTime) / (24 * 60 * 60 * 1000)
        return maxOf(0, 30 - daysPassed.toInt())
    }
    
    /**
     * 是否已过期（超过30天）
     */
    fun isExpired(): Boolean {
        val currentTime = System.currentTimeMillis()
        val daysPassed = (currentTime - deleteTime) / (24 * 60 * 60 * 1000)
        return daysPassed >= 30
    }
}
