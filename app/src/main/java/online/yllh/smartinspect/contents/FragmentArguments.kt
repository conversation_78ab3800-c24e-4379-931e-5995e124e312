package online.yllh.smartinspect.contents

@Suppress("ConstPropertyName")
object FragmentArguments {
    const val SnapshotModel = "SnapshotModel"
    const val AlbumId = "AlbumId"
    const val AlbumName = "AlbumName"
    const val ImageUri = "ImageUri"
    const val MessageList = "MessageList"
    const val ImageId = "ImageId"
    const val ImageCachePath = "ImageCachePath"
    const val EditModel = "EditModel"
    const val Address = "address"
    const val ParentAlbumId = "ParentAlbumId"
    const val IsImportMode = "IsImportMode"
    const val ImportModeAlbumId = "IsImportModeAlbumId"
    const val ProjectId = "ProjectId"
    const val Photo = "Photo"
}
