package online.yllh.smartinspect.extension

import android.content.Context
import android.os.Build

/**
 * 当前手机系统是否是 Android13 及其以后
 */
fun equalOrBeyondAndroid13() = Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU

/**
 * 当前手机系统是否是 Android10 及其以前
 */
fun equalOrBeforeAndroid10() = Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q

/**
 * App是否是首次安装
 */
fun isAppFirstInstall(context: Context): Boolean {
    val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
    return packageInfo.firstInstallTime == packageInfo.lastUpdateTime
}
