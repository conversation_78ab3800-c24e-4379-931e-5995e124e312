package online.yllh.smartinspect.extension

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.launch

inline fun <reified T> Flow<T>.observeWithLifecycle(
    lifecycleOwner: LifecycleOwner,
    minActiveState: Lifecycle.State = Lifecycle.State.CREATED,
    action: FlowCollector<T>
): Job = lifecycleOwner.lifecycleScope.launch {
    flowWithLifecycle(lifecycleOwner.lifecycle, minActiveState).collect(action)
}

inline fun <reified T> Flow<T>.observeWithLifecycle(
    activity: FragmentActivity,
    minActiveState: Lifecycle.State = Lifecycle.State.CREATED,
    action: FlowCollector<T>
): Job = activity.lifecycleScope.launch {
    flowWithLifecycle(activity.lifecycle, minActiveState).collect(action)
}

inline fun <reified T> Flow<T>.observeWithLifecycle(
    fragment: Fragment,
    minActiveState: Lifecycle.State = Lifecycle.State.CREATED,
    viewLifecycle: Boolean = true,
    action: FlowCollector<T>
): Job {
    val lifecycle = if (viewLifecycle) fragment.viewLifecycleOwner.lifecycle else fragment.lifecycle
    return lifecycle.coroutineScope.launch {
        flowWithLifecycle(lifecycle, minActiveState).collect(action)
    }
}
