@file:Suppress("NOTHING_TO_INLINE")

package online.yllh.smartinspect.extension

import android.app.Dialog
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.net.ConnectivityManager
import android.net.Uri
import android.os.Bundle
import android.os.Parcel
import android.os.Parcelable
import android.util.Size
import android.view.LayoutInflater
import android.view.View
import android.view.WindowInsets
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import androidx.annotation.DrawableRes
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.app.ActivityOptionsCompat
import androidx.core.content.ContextCompat
import androidx.core.content.getSystemService
import online.yllh.smartinspect.utils.NumberFormatterUtils
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.io.Serializable
import java.util.Locale


/**
 * Convenient for dp to px
 */
val Number.dp: Int get() = ((toFloat() * Resources.getSystem().displayMetrics.density) + 0.5f).toInt()

val Number.dpF: Float get() = (toFloat() * Resources.getSystem().displayMetrics.density)

/**
 * Convenient for sp to px
 */
val Number.sp: Int get() = (toInt() * Resources.getSystem().displayMetrics.scaledDensity).toInt()

/**
 * @return the screen size in pixels
 * Note: this method is only available on Android 11 and above
 */
inline val Context.screenSize: Size
    get() {
        return if (equalOrBeforeAndroid10()) {
            // 获取屏幕宽高
            Size(resources.displayMetrics.widthPixels, resources.displayMetrics.heightPixels)
        } else {
            val windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val metrics = windowManager.currentWindowMetrics
            val windowInsets = metrics.windowInsets
            val insets = windowInsets.getInsetsIgnoringVisibility(
                WindowInsets.Type.navigationBars() or WindowInsets.Type.displayCutout()
            )

            val insetsWidth: Int = insets.right + insets.left
            val insetsHeight: Int = insets.top + insets.bottom
            val bounds: Rect = metrics.bounds
            Size(bounds.width() - insetsWidth, bounds.height() - insetsHeight)
        }
    }

/**
 * Screen Width in pixels
 */
inline val Context.screenWidth
    get() = screenSize.width

/**
 * Screen Height for pixels
 */
inline val Context.screenHeight
    get() = screenSize.height

/**
 * Convenience for color
 */
fun Int.toColorInt(context: Context) = ContextCompat.getColor(context, this)

fun String.isNumber(): Boolean {
    val pattern = Regex("^\\d+$") // 匹配由数字组成的字符串的正则表达式
    return pattern.matches(this)
}

/**
 * Convenient for int resource to text
 */
fun Int.toTextString(context: Context) = context.getString(this)

/**
 *  Convenient for int resource to drawable
 */
fun Context.getDrawableOrNull(@DrawableRes id: Int?): Drawable? {
    return if (id == null || id == 0) null else AppCompatResources.getDrawable(this, id)
}

/**
 * 判断当前布局方向是否是 RTL 方式
 */
fun Context.isRTL(): Boolean {
    return resources.configuration.layoutDirection == View.LAYOUT_DIRECTION_RTL
}

/**
 * 判断系统当前是否是暗黑模式
 */
fun Context.isDarkMode(): Boolean {
    return resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK == Configuration.UI_MODE_NIGHT_YES
}

fun String.isHttpUrl(): Boolean {
    val regex = Regex("^((https|http|ftp|rtsp|mms)?:\\/\\/)[^\\s]+")
    val isNetworkAddress = regex.matches(this)
    return isNetworkAddress
}

/**
 * start an activity with fade_in animation
 */
inline fun <reified T : AppCompatActivity> AppCompatActivity.startActivityWithFadeIn(context: Context) {
    val bundle = ActivityOptionsCompat.makeCustomAnimation(
        context, android.R.anim.fade_in, android.R.anim.fade_out
    ).toBundle()

    val intent = Intent(context, T::class.java)
    startActivity(intent, bundle)
}

/**
 * start an activity with fade_in animation
 */
fun AppCompatActivity.startActivityWithFadeIn(context: Context, destination: Class<*>) {
    val bundle = ActivityOptionsCompat.makeCustomAnimation(
        context, android.R.anim.fade_in, android.R.anim.fade_out
    ).toBundle()

    val intent = Intent(context, destination)
    startActivity(intent, bundle)
}


inline fun <reified T : Serializable> Bundle.serializable(key: String): T? = when {
    equalOrBeyondAndroid13() -> getSerializable(key, T::class.java)
    else -> @Suppress("DEPRECATION") getSerializable(key) as? T
}

inline fun <reified T : Parcelable> Bundle.parcelable(key: String): T? = when {
    equalOrBeyondAndroid13() -> getParcelable(key, T::class.java)
    else -> @Suppress("DEPRECATION") getParcelable(key) as? T
}

inline fun <reified T : Serializable> Intent.serializable(key: String): T? = when {
    equalOrBeyondAndroid13() -> getSerializableExtra(key, T::class.java)
    else -> @Suppress("DEPRECATION") getSerializableExtra(key) as? T
}

inline fun <reified T : Parcelable> Intent.parcelable(key: String): T? = when {
    equalOrBeyondAndroid13() -> getParcelableExtra(key, T::class.java)
    else -> @Suppress("DEPRECATION") getParcelableExtra(key) as? T
}


/**
 * 将浮点数转化为指定格式的字符串
 */
fun Float.toDecimalString(precision: Int): String {
    if (equalOrBeforeAndroid10()) {
        return when (precision) {
            1 -> String.format(Locale.ENGLISH, "%.1f", this)

            2 -> String.format(Locale.ENGLISH, "%.2f", this)

            else -> String.format(Locale.ENGLISH, "%.4f", this)
        }
    }

    return when (precision) {
        1 -> NumberFormatterUtils.numberFormatter1?.format(this).toString()

        2 -> NumberFormatterUtils.numberFormatter2?.format(this).toString()

        else -> NumberFormatterUtils.numberFormatter4?.format(this).toString()
    }
}

/**
 * 将浮点数转化为指定格式的double
 */
fun Double.toDecimalDouble(precision: Int): Double {
    if (equalOrBeforeAndroid10()) {
        return when (precision) {
            1 -> String.format(Locale.ENGLISH, "%.1f", this).toDouble()

            2 -> String.format(Locale.ENGLISH, "%.2f", this).toDouble()

            else -> String.format(Locale.ENGLISH, "%.4f", this).toDouble()
        }
    }
    return when (precision) {
        1 -> NumberFormatterUtils.numberFormatter1?.format(this)?.toBigDecimal()?.toDouble() ?: this

        2 -> NumberFormatterUtils.numberFormatter2?.format(this)?.toBigDecimal()?.toDouble() ?: this

        else -> NumberFormatterUtils.numberFormatter4?.format(this)?.toBigDecimal()?.toDouble() ?: this
    }
}

fun Size.toResolutionText(): String {
    return "${width}x${height}"
}

fun Uri.readBytesFromUri(context: Context): ByteArray? {
    val contentResolver: ContentResolver = context.contentResolver
    val inputStream = contentResolver.openInputStream(this)
    val outputStream = ByteArrayOutputStream()
    val buffer = ByteArray(1024)
    var bytesRead: Int
    try {
        while (inputStream?.read(buffer).also { bytesRead = it!! } != -1) {
            outputStream.write(buffer, 0, bytesRead)
        }
        return outputStream.toByteArray()
    } catch (e: IOException) {
        e.printStackTrace()
    } finally {
        try {
            inputStream?.close()
            outputStream.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }
    return null
}

fun Bitmap.toJpegByteArray(quality: Int): ByteArray {
    val outputStream = ByteArrayOutputStream()
    this.compress(CompressFormat.JPEG, quality, outputStream)
    return outputStream.toByteArray()
}

fun View.hideKeyboard() {
    val imm = context.getSystemService<InputMethodManager>() ?: return
    imm.hideSoftInputFromWindow(windowToken, 0)
}

val Context.layoutInflater: LayoutInflater inline get() = LayoutInflater.from(this)

inline fun <T : Dialog> T.onShow(crossinline listener: T.() -> Unit) = apply {
    setOnShowListener { listener() }
}

fun Context.isNetworkAvailable(): Boolean {
    val cm = getSystemService<ConnectivityManager>() ?: return false
    return cm.activeNetworkInfo.let { it != null && it.isConnected }
}

inline fun Parcel.readBool() = readInt() == 1
inline fun Parcel.writeBool(value: Boolean) = writeInt(if (value) 1 else 0)
