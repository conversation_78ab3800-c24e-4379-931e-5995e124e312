package online.yllh.smartinspect.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogChooseAlbumBinding
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.model.AlbumTree
import online.yllh.smartinspect.network.model.response.CloudAlbumModel

class MovePhotoDialog(context: Context, private val dataSource: List<CloudAlbumModel>, private val currentAlbumId: String) : Dialog(context) {
    private val breadcrumbList = mutableListOf<String>()
    private val nodePath = mutableListOf<AlbumTree>()
    private lateinit var breadcrumbAdapter: BreadCrumbAdapter
    private lateinit var nodeAdapter: NodeAdapter
    private val binding = DialogChooseAlbumBinding.inflate(context.layoutInflater)
    private val albums = convertToAlbumTree(dataSource, currentAlbumId)
    private val rootNode = AlbumTree(id = Long.MIN_VALUE, albumName = "选择目标相册", child = albums)
    private var selectedNode: AlbumTree? = null

    companion object {
        private val AlbumTree.isRootNode get() = id == Long.MIN_VALUE

        fun findAlbumById(albums: List<AlbumTree>?, targetId: Long): AlbumTree? {
            if (albums.isNullOrEmpty()) return null

            for (album in albums) {
                if (album.id == targetId) return album

                val found = findAlbumById(album.child, targetId)
                if (found != null) return found
            }

            return null
        }

        private fun convertToAlbumTree(cloudAlbums: List<CloudAlbumModel>, excludeAlbumId: String): List<AlbumTree> {
            return cloudAlbums.filter { it.id != excludeAlbumId }.map { cloudAlbum ->
                val childAlbums = cloudAlbum.palbumVOS?.let { palbumList ->
                    val defaultAlbum = palbumList.firstOrNull { it.id == null }
                    val realChildren = defaultAlbum?.palbumVOS?.filter { it.id != null } ?: emptyList()
                    convertToAlbumTree(realChildren, excludeAlbumId)
                } ?: emptyList()

                AlbumTree(
                    id = cloudAlbum.id?.toLongOrNull() ?: 0L,
                    albumName = cloudAlbum.albumName,
                    parentAlbumId = cloudAlbum.parentAlbumId?.toLong() ?: 0L,
                    child = childAlbums
                )
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        window?.setDimAmount(0.4f)
        nodePath.add(rootNode)
        breadcrumbList.add(rootNode.albumName.orEmpty())

        breadcrumbAdapter = BreadCrumbAdapter(breadcrumbList) { level ->
            nodePath.subList(level + 1, nodePath.size).clear()
            breadcrumbList.subList(level + 1, breadcrumbList.size).clear()
            selectedNode = null
            updateUI()
        }
        binding.breadcrumbView.adapter = breadcrumbAdapter

        (binding.listView.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false

        binding.cancelButton.setOnClickListener {
            cancel()
        }

        binding.okButton.setOnClickListener {
            val selectedNode = selectedNode
            if (selectedNode == null) {
                Toast.makeText(context, "请选择一个相册", Toast.LENGTH_SHORT).show()
            } else {
                dismiss()
                okButtonListener?.invoke(selectedNode.id.toString())
            }
        }

        updateUI()
    }

    private var okButtonListener: ((String?) -> Unit)? = null

    fun setOkButtonListener(listener: (String?) -> Unit) {
        okButtonListener = listener
    }

    private fun findPathToAlbum(albums: List<AlbumTree>?, targetId: Long): List<AlbumTree>? {
        if (albums.isNullOrEmpty()) return null

        for (album in albums) {
            if (album.id == targetId) return listOf(album)

            val childPath = findPathToAlbum(album.child, targetId)
            if (childPath != null) {
                return listOf(album) + childPath
            }
        }

        return null
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun updateUI() {
        breadcrumbAdapter.notifyDataSetChanged()
        val current = nodePath.last()

        nodeAdapter = NodeAdapter(
            context,
            currentNode = current,
            selectedNode = selectedNode,
            onSelectNode = { this.selectedNode = it },
            onEnterChild = { childNode ->
                selectedNode = null
                nodePath.add(childNode)
                breadcrumbList.add(childNode.albumName ?: "")
                updateUI()
            }
        )
        binding.listView.adapter = nodeAdapter
    }

    class BreadCrumbAdapter(
        private val levels: List<String>,
        private val onClick: (Int) -> Unit
    ) : RecyclerView.Adapter<BreadCrumbAdapter.Holder>() {

        class Holder(view: View) : RecyclerView.ViewHolder(view) {
            val tv: TextView = view.findViewById(R.id.tvBreadcrumb)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            val view = LayoutInflater.from(parent.context).inflate(R.layout.item_breadcrumb, parent, false)
            return Holder(view)
        }

        override fun getItemCount() = levels.size

        override fun onBindViewHolder(holder: Holder, position: Int) {
            holder.tv.text = levels[position]
            holder.tv.setOnClickListener { onClick(position) }
        }
    }

    class NodeAdapter(
        private val context: Context,
        private val currentNode: AlbumTree,
        selectedNode: AlbumTree?,
        private val onSelectNode: (AlbumTree) -> Unit,
        private val onEnterChild: (AlbumTree) -> Unit
    ) : RecyclerView.Adapter<NodeAdapter.Holder>() {

        private var lastSelectedNode: AlbumTree? = selectedNode

        private val addCurrent get() = !currentNode.isRootNode

        class Holder(view: View) : RecyclerView.ViewHolder(view) {
            val tvName: TextView = view.findViewById(R.id.tvName)
            val ivCheck: RadioButton = view.findViewById(R.id.ivCheck)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            val view = LayoutInflater.from(parent.context).inflate(R.layout.item_node, parent, false)
            return Holder(view)
        }

        override fun getItemCount(): Int {
            val nodes = currentNode.child.orEmpty()
            return if (addCurrent) nodes.size + 1 else nodes.size
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            val nodes = currentNode.child.orEmpty()
            val child = if (position == 0 && addCurrent) {
                currentNode
            } else if (addCurrent) {
                nodes[position - 1]
            } else nodes[position]
            holder.tvName.text = child.albumName
            holder.ivCheck.isChecked = child == lastSelectedNode
            holder.ivCheck.setOnClickListener {
                if (lastSelectedNode == child)
                    return@setOnClickListener
                val lastNode = lastSelectedNode
                lastSelectedNode = child
                if (lastNode != null) {
                    if (lastNode == currentNode) {
                        notifyItemChanged(0)
                    } else {
                        val lastIndex = nodes.indexOfFirst { it == lastNode }
                        if (lastIndex != -1) {
                            val pos = if (addCurrent) lastIndex + 1 else lastIndex
                            notifyItemChanged(pos)
                        }
                    }
                }
                notifyItemChanged(position)
                onSelectNode(child)
            }
            holder.itemView.setOnClickListener {
                if (child.child.isNullOrEmpty()) {
                    holder.ivCheck.callOnClick()
                } else if (child == currentNode) {
                    holder.ivCheck.callOnClick()
                } else {
                    onEnterChild(child)
                }
            }
        }
    }
}
