package online.yllh.smartinspect.dialog

import android.app.Dialog
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Spinner
import android.widget.TextView
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogLayoutProjectUploadImageBinding
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.network.model.response.InnerAlbum

class ProjectUploadPhotoDialog(context: Context, val dataSource: List<InnerAlbum>) : Dialog(context) {
    private var binding = DialogLayoutProjectUploadImageBinding.inflate(context.layoutInflater, null, false)
    var selectId: String? = null

    init {
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        window?.setDimAmount(0.4f)
        binding.cancelButton.setOnClickListener {
            dismiss()
        }
        addSpinner(dataSource, "first")
    }

    fun setOkButtonListener(listener: (String?) -> Unit) {
        binding.okButton.setOnClickListener {
            dismiss()
            listener(selectId)
        }
    }

    fun addSpinner(dataSource: List<InnerAlbum>, tag: String): Spinner {
        val spinner = if (binding.spinnerLayout.findViewWithTag<Spinner?>(tag) != null) {
            binding.spinnerLayout.findViewWithTag<Spinner?>(tag)
        } else {
            val s = Spinner(context)
            s.tag = tag
            // 设置 Spinner 的宽度和高度
            val params = ViewGroup.MarginLayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            params.setMargins(0, 0, 0, 16.dp)
            s.layoutParams = params
            binding.spinnerLayout.addView(s)
            s
        }

        setDataSource(spinner, dataSource)
        return spinner
    }

    fun setDataSource(spinner: Spinner, dataSource: List<InnerAlbum>) {
        // 创建适配器
        val adapter = CloudAlbumAdapter(context, dataSource)

        // 设置下拉框的样式
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

        // 将适配器设置给 Spinner
        spinner.adapter = adapter

        // 设置选择监听器
        spinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (dataSource[position].palbumVOS != null) {
                    addSpinner(dataSource[position].palbumVOS!!, "second")
                } else {
                    val selectedAlbum = dataSource[position]
                    selectId = selectedAlbum.id
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
                if (dataSource.size > 0) {
                    val selectedAlbum = dataSource.first()
                    selectId = selectedAlbum.id
                }
            }
        }
    }

    inner class CloudAlbumAdapter(context: Context, private val albums: List<InnerAlbum>) :
        ArrayAdapter<InnerAlbum>(context, 0, albums) {

        override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
            var listItemView = convertView
            if (listItemView == null) {
                listItemView = context.layoutInflater.inflate(
                    android.R.layout.simple_spinner_item,
                    parent,
                    false
                )
            }

            val albumNameTextView = listItemView!!.findViewById<TextView>(android.R.id.text1)
            val album = albums[position]
            albumNameTextView.text = album.albumName

            return listItemView
        }

        override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
            var dropDownItemView = convertView
            if (dropDownItemView == null) {
                dropDownItemView = context.layoutInflater.inflate(
                    android.R.layout.simple_spinner_dropdown_item,
                    parent,
                    false
                )
            }

            val albumNameTextView = dropDownItemView!!.findViewById<TextView>(android.R.id.text1)
            val album = albums[position]
            albumNameTextView.text = album.albumName

            return dropDownItemView
        }
    }
}
