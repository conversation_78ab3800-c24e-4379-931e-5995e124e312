package online.yllh.smartinspect.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.ViewGroup
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogWatermarkWeatherStyleSelectBinding
import online.yllh.smartinspect.databinding.ItemFormatBinding
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.model.WatermarkState.Companion.descByStyle
import online.yllh.smartinspect.view.camera.CameraViewModel

class WatermarkWeatherStyleSelectDialog(context: Context, private val viewModel: CameraViewModel) : Dialog(context) {
    private val binding = DialogWatermarkWeatherStyleSelectBinding.inflate(context.layoutInflater)
    private val lifecycleScope = MainScope()
    private var selectedPos = -1
    private val styleCount = 6

    @SuppressLint("NotifyDataSetChanged")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        selectedPos = viewModel.watermarkState.value.weatherStyle.takeIf { it < styleCount } ?: -1
        (binding.styleList.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        binding.styleList.adapter = object : RecyclerView.Adapter<StyleViewHolder>() {
            override fun getItemCount() = styleCount
            override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StyleViewHolder {
                val holder = StyleViewHolder(ItemFormatBinding.inflate(parent.context.layoutInflater, parent, false))
                holder.binding.root.setOnClickListener {
                    val pos = holder.bindingAdapterPosition
                    if (pos == RecyclerView.NO_POSITION)
                        return@setOnClickListener
                    val preSelectedPos = selectedPos
                    selectedPos = pos
                    if (preSelectedPos != -1 && preSelectedPos != pos) {
                        notifyItemChanged(preSelectedPos)
                        notifyItemChanged(pos)
                    } else if (preSelectedPos == -1) {
                        notifyItemChanged(pos)
                    }
                    lifecycleScope.launch {
                        delay(300)
                        viewModel.watermarkState.value = viewModel.watermarkState.value.copy(weatherStyle = pos)
                        dismiss()
                    }
                }
                return holder
            }

            override fun onBindViewHolder(holder: StyleViewHolder, position: Int) {
                val weather = viewModel.watermarkState.value.weather
                holder.binding.tvFormat.text = weather.descByStyle(position)
                holder.binding.tvFormat.isSelected = position == selectedPos
                holder.binding.ivCheck.isInvisible = position != selectedPos
                holder.binding.divider.isVisible = position != styleCount - 1
            }
        }.also { adapter ->
            lifecycleScope.launch {
                viewModel.watermarkState.map { it.weather }.distinctUntilChanged().collect {
                    adapter.notifyDataSetChanged()
                }
            }
        }
    }

    override fun dismiss() {
        super.dismiss()
        lifecycleScope.cancel()
    }

    private class StyleViewHolder(val binding: ItemFormatBinding) : RecyclerView.ViewHolder(binding.root)
}
