package online.yllh.smartinspect.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import online.yllh.smartinspect.databinding.DialogLayoutEditContentBinding

class AddContentDialog(val content: String) : DialogFragment() {
    private lateinit var binding: DialogLayoutEditContentBinding
    private var okListener: ((String) -> Unit?)? = null
    private var cancelListener: (() -> Unit?)? = null

    init {
        isCancelable = false

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DialogLayoutEditContentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.message.setText(if (content == "输入内容") "" else content)
        binding.okButton.setOnClickListener {
            okListener?.invoke(binding.message.text.toString())
            dismiss()
        }
        binding.cancelButton.setOnClickListener {
            dismiss()
            cancelListener?.invoke()
        }
    }

    fun setCancelListener(listener: () -> Unit) {
        this.cancelListener = listener
    }

    fun setOkListener(listener: (String) -> Unit) {
        this.okListener = listener
    }
}
