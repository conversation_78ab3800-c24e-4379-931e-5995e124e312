package online.yllh.smartinspect.dialog

import android.app.Dialog
import android.content.Context
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogLayoutCreatGrlleryBinding
import online.yllh.smartinspect.extension.layoutInflater

class CreateGalleryDialog(context: Context) : Dialog(context) {
    private var binding = DialogLayoutCreatGrlleryBinding.inflate(context.layoutInflater, null, false)

    init {
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        window?.setDimAmount(0.4f)
        binding.cancelButton.setOnClickListener {
            dismiss()
        }
    }

    fun setOkButtonListener(listener: (String) -> Unit) {
        binding.okButton.setOnClickListener {
            dismiss()
            listener(binding.name.text.toString())
        }
    }
}
