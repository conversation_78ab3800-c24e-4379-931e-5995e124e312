package online.yllh.smartinspect.dialog

import android.app.Dialog
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.AdapterView.OnItemSelectedListener
import android.widget.ArrayAdapter
import android.widget.TextView
import android.widget.Toast
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.DialogLayoutUploadSelectAlbumBinding
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.model.AlbumTree
import online.yllh.smartinspect.network.model.response.IdNameModel
import online.yllh.smartinspect.network.model.response.Project
import online.yllh.smartinspect.view.camera.CameraFragment.Companion.albumTrees

class ProjectUploadSelectAlbumDialog(context: Context, projects: List<Project>, private val onAlbumSelected: (String) -> Unit = {}) : Dialog(context) {
    private val binding = DialogLayoutUploadSelectAlbumBinding.inflate(context.layoutInflater, null, false)
    private val lifecycleScope = MainScope()

    private val selectedAlbum = MutableStateFlow<AlbumTree?>(null)
    private var selectedProject: Project? = null
    private var first = true
    private val sharedPreferences = context.getSharedPreferences(SpKey.PREFS_NAME, Context.MODE_PRIVATE)

    init {
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        window?.setDimAmount(0.4f)
        binding.cancelButton.setOnClickListener { dismiss() }
        binding.okButton.setOnClickListener {
            val album = selectedAlbum.value
            if (album == null) {
                Toast.makeText(context, "请选择相册", Toast.LENGTH_SHORT).show()
            } else {
                dismiss()
                onAlbumSelected(album.id.toString())
            }
        }
        binding.tvAlbum.setOnClickListener {
            selectedProject?.let { showAlbumSelectDialog(it) }
        }

        if (projects.isNotEmpty()) {
            lifecycleScope.launch {
                val cachedAlbumId = sharedPreferences.getLong(ChooseAlbumDialog.KEY_SELECTED_ALBUM_ID, -1)
                if (cachedAlbumId != -1L) {
                    for (i in projects.indices) {
                        val project = projects[i]
                        val albums = albumTrees(project.id.toString())
                        val cachedAlbum = ChooseAlbumDialog.findAlbumById(albums, cachedAlbumId)
                        if (cachedAlbum != null) {
                            binding.spinnerProject.setSelection(i)
                            selectedProject = project
                            selectedAlbum.value = cachedAlbum
                            return@launch
                        }
                    }
                }
            }
        }

        val projectAdapter = SpinnerAdapter(context, projects)
        binding.spinnerProject.adapter = projectAdapter
        binding.spinnerProject.onItemSelectedListener = object : OnItemSelectedListener {
            override fun onNothingSelected(parent: AdapterView<*>?) {}
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                projects[position].let {
                    if(selectedProject == it) {
                        return
                    }
                    selectedAlbum.value = null
                    selectedProject = it
                    if (first) {
                        first = false
                    } else {
                        showAlbumSelectDialog(it)
                    }
                }
            }
        }
        lifecycleScope.launch {
            selectedAlbum.collect {
                binding.tvAlbum.text = it?.albumName.orEmpty()
            }
        }
    }

    private fun showAlbumSelectDialog(project: Project) = lifecycleScope.launch {
        val albums = albumTrees(project.id.toString())
        if (albums.isEmpty()) return@launch
        
        val cachedAlbumId = sharedPreferences.getLong(ChooseAlbumDialog.KEY_SELECTED_ALBUM_ID, -1)
        ChooseAlbumDialog(context, albums, cachedAlbumId) {
            selectedAlbum.value = it
        }.show()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        lifecycleScope.cancel()
        super.dismiss()
    }

    private class SpinnerAdapter(context: Context, private val list: List<IdNameModel>) :
        ArrayAdapter<IdNameModel>(context, 0, list) {

        override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
            var listItemView = convertView
            if (listItemView == null)
                listItemView = context.layoutInflater.inflate(android.R.layout.simple_spinner_item, parent, false)
            listItemView!!.findViewById<TextView>(android.R.id.text1).text = list[position].getModelName()
            return listItemView
        }

        override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
            var dropDownItemView = convertView
            if (dropDownItemView == null)
                dropDownItemView = context.layoutInflater.inflate(android.R.layout.simple_spinner_dropdown_item, parent, false)
            dropDownItemView!!.findViewById<TextView>(android.R.id.text1).text = list[position].getModelName()
            return dropDownItemView
        }
    }
}
