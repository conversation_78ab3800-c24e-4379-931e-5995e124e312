package online.yllh.smartinspect.dialog

import android.Manifest
import android.content.Context
import android.media.MediaRecorder
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import online.yllh.smartinspect.contents.PermissionCode
import online.yllh.smartinspect.databinding.DialogLayoutAddVoiceMessageBinding
import online.yllh.smartinspect.media.AudioDurationCalculator
import online.yllh.smartinspect.provider.ApplicationContextProvider
import pub.devrel.easypermissions.EasyPermissions
import pub.devrel.easypermissions.PermissionRequest
import java.io.File

class AddVoiceMessageDialog(context: Context) : DialogFragment() {
    private lateinit var binding: DialogLayoutAddVoiceMessageBinding
    private lateinit var mediaRecorder: MediaRecorder
    private var isRecording = false
    private var filePath = ""
    private var recordingDialogFragment: RecordingDialogFragment? = null
    private var listener: ((String, Int) -> Unit)? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DialogLayoutAddVoiceMessageBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.message.setOnTouchListener { p0, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    if (checkPermission()) {
                        recordingDialogFragment = RecordingDialogFragment()
                        recordingDialogFragment?.show(childFragmentManager, "recording_dialog")
                        startRecording()
                    }
                    true
                }

                MotionEvent.ACTION_UP -> {
                    recordingDialogFragment?.dismiss()
                    recordingDialogFragment = null
                    stopRecording()
                    true
                }

                MotionEvent.ACTION_CANCEL -> {
                    recordingDialogFragment?.dismiss()
                    recordingDialogFragment = null
                    stopRecording()
                    true
                }

                else -> false
            }
        }

        binding.cancelButton.setOnClickListener {
            filePath = ""
            dismiss()
        }
        binding.okButton.setOnClickListener {
            val second = AudioDurationCalculator.getDurationInSeconds(filePath)
            if (filePath.isNotEmpty()) {
                <EMAIL>?.invoke(filePath, second)
                filePath = ""
            }
            dismiss()
        }
    }

    fun setCancelListener() {

    }

    fun setOkListener(listener: (String, Int) -> Unit) {
        this.listener = listener
    }

    fun checkPermission(): Boolean {
        if (EasyPermissions.hasPermissions(ApplicationContextProvider.context, Manifest.permission.RECORD_AUDIO)) {
            return true
        } else {
            EasyPermissions.requestPermissions(
                PermissionRequest.Builder(this, PermissionCode.RECORD_AUDIO, Manifest.permission.RECORD_AUDIO)
                    .setRationale("未开启录音权限，无法使用此功能")
                    .setPositiveButtonText("申请权限")
                    .setNegativeButtonText("取消")
                    .build()
            )
            return false
        }
    }

    private fun startRecording() {
        if (!isRecording) {
            filePath = getOutputFile().absolutePath
            mediaRecorder = MediaRecorder()
            mediaRecorder.setAudioSource(MediaRecorder.AudioSource.MIC)
            mediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)
            mediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)
            mediaRecorder.setOutputFile(filePath)
            try {
                mediaRecorder.prepare()
                mediaRecorder.start()
                isRecording = true
            } catch (e: Exception) {
                filePath = ""
                e.printStackTrace()
                showToast("录音失败")
            }
        }
    }

    private fun stopRecording() {
        if (isRecording) {
            mediaRecorder.stop()
            mediaRecorder.release()
            isRecording = false
        }
    }

    private fun getOutputFile(): File {
        val dir = File(requireContext().cacheDir, "recordings")
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return File(dir, "recording_${System.currentTimeMillis()}.mp3")
    }

    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }
}
