package online.yllh.smartinspect.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import online.yllh.smartinspect.R
import online.yllh.smartinspect.network.model.response.ApkInfo

class UpdateDialog(
    context: Context,
    private val apkInfo: ApkInfo,
    private val onDownloadClick: () -> Unit,
    private val onLaterClick: () -> Unit
) : Dialog(context, R.style.CustomDialog) {

    private lateinit var btnDownload: Button
    private lateinit var btnLater: Button
    private lateinit var btnComplete: Button
    private lateinit var layoutUpdateContent: LinearLayout
    private lateinit var layoutVersionInfo: LinearLayout
    private lateinit var layoutDownloadProgress: LinearLayout
    private lateinit var layoutButtons: LinearLayout
    private lateinit var layoutDownloadingButton: LinearLayout
    private lateinit var tvProgressText: TextView
    private lateinit var ivErrorIcon: ImageView
    private lateinit var progressBar: ProgressBar
    private var progress: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_update_app, null)
        setContentView(view)

        // 设置对话框宽度
        window?.let { window ->
            val layoutParams = window.attributes
            layoutParams.width = (context.resources.displayMetrics.widthPixels * 0.8).toInt() // 屏幕宽度的80%
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
            window.attributes = layoutParams
        }

        initViews(view)
        setupClickListeners()
        updateContent()

        setCancelable(false)
        setCanceledOnTouchOutside(false)
    }

    private fun initViews(view: android.view.View) {
        btnDownload = view.findViewById(R.id.btn_download)
        btnLater = view.findViewById(R.id.btn_later)
        btnComplete = view.findViewById(R.id.btn_complete)
        layoutUpdateContent = view.findViewById(R.id.layout_update_content)
        layoutVersionInfo = view.findViewById(R.id.layout_version_info)
        layoutDownloadProgress = view.findViewById(R.id.layout_download_progress)
        layoutButtons = view.findViewById(R.id.layout_buttons)
        layoutDownloadingButton = view.findViewById(R.id.layout_downloading_button)
        tvProgressText = view.findViewById(R.id.tv_progress_text)
        ivErrorIcon = view.findViewById(R.id.iv_error_icon)
        progressBar = view.findViewById(R.id.progress_bar)
    }

    private fun setupClickListeners() {
        btnDownload.setOnClickListener {
            onDownloadClick()
        }

        btnLater.setOnClickListener {
            this.progress = 0
            if (ivErrorIcon.visibility == View.VISIBLE) {
                showInitialState()
            } else {
                onLaterClick()
                dismiss()
            }
        }

        btnComplete.setOnClickListener {}
    }

    private fun updateContent() {
        layoutUpdateContent.removeAllViews()
        val content = apkInfo.content
        val lines = if (!content.isNullOrEmpty()) {
            content.split("\n").filter { it.isNotBlank() }
        } else {
            listOf("更新了应用功能", "修复了已知问题")
        }

        lines.forEachIndexed { index, line ->
            val textView = TextView(context).apply {
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                ).apply {
                    if (index < lines.size - 1) {
                        bottomMargin = (4 * context.resources.displayMetrics.density + 0.5f).toInt()
                    }
                }

                val formattedText = if (line.matches(Regex("^\\d+\\."))) {
                    line
                } else {
                    line
                }
                text = formattedText

                setTextColor(context.getColor(android.R.color.black))
                textSize = 12f
            }
            layoutUpdateContent.addView(textView)
        }
    }

    fun updateDownloadProgress(progress: Int) {
        when {
            progress == 0 -> {
                showInitialState()
            }
            progress in 1..99 -> {
                showDownloadingState(progress)
                this.progress = progress
            }
            progress == 100 -> {
                showDownloadCompleteState()
            }
        }
    }

    @SuppressLint("SetTextI18n")
    fun showDownloadError() {
        layoutVersionInfo.visibility = View.GONE
        layoutDownloadProgress.visibility = View.VISIBLE
        layoutButtons.visibility = View.VISIBLE
        btnLater.visibility = View.VISIBLE
        layoutDownloadingButton.visibility = View.GONE

        tvProgressText.text = "下载失败，${this.progress}%"
        ivErrorIcon.visibility = View.VISIBLE
        progressBar.progress = 0

        btnDownload.text = "重新下载"
        btnDownload.isEnabled = true
        btnLater.text = "稍后下载"
    }

    private fun showInitialState() {
        layoutVersionInfo.visibility = View.VISIBLE
        layoutDownloadProgress.visibility = View.GONE
        layoutButtons.visibility = View.VISIBLE
        btnLater.visibility = View.GONE
        layoutDownloadingButton.visibility = View.GONE

        btnDownload.text = "下载"
        btnDownload.isEnabled = true
        btnLater.text = "稍后下载"
    }

    private fun showDownloadingState(progress: Int) {
        layoutVersionInfo.visibility = View.GONE
        layoutDownloadProgress.visibility = View.VISIBLE
        layoutButtons.visibility = View.GONE
        layoutDownloadingButton.visibility = View.VISIBLE

        tvProgressText.text = "正在下载，进度${progress}%"
        ivErrorIcon.visibility = View.GONE
        progressBar.progress = progress

        btnComplete.isEnabled = false
    }

    private fun showDownloadCompleteState() {
        layoutVersionInfo.visibility = View.GONE
        layoutDownloadProgress.visibility = View.VISIBLE
        layoutButtons.visibility = View.VISIBLE
        layoutDownloadingButton.visibility = View.GONE

        btnDownload.text = "安装"
        tvProgressText.text = "下载完成"
        btnDownload.isEnabled = true
        btnLater.text = "稍后下载"
    }
}
