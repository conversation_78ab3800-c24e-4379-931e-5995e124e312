package online.yllh.smartinspect.dialog

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogLayoutAddMessageBinding
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.media.AudioPlayer
import online.yllh.smartinspect.model.MessageUIStatus
import online.yllh.smartinspect.network.model.response.Message
import online.yllh.smartinspect.utils.DownloadUtils
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID

class AddMessageDialog(context: Context, message: MutableList<Message>) : DialogFragment() {
    private lateinit var binding: DialogLayoutAddMessageBinding
    val messageList = message
    val adapter = MessageAdapter(message.map {
        MessageUIStatus(
            data = it,
        )
    }.toMutableList())
    private var okListener: ((MutableList<Message>) -> Unit?)? = null
    private var voiceListener: ((MutableList<Message>) -> Unit?)? = null
    private var cancelListener: (() -> Unit?)? = null

    init {
        isCancelable = false
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DialogLayoutAddMessageBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.messageList.adapter = adapter
        binding.messageList.layoutManager = LinearLayoutManager(context)
        binding.count.text = "共计${messageList.count()}条"
        binding.okButton.setOnClickListener {
            if (binding.message.text.isNotEmpty()) {
                adapter.message.add(
                    MessageUIStatus(
                        data = Message(inputTime = getCurrentDateTime(), type = 0, value = binding.message.text.toString())
                    )
                )
            }
            okListener?.invoke(adapter.message.map {
                it.data
            }.toMutableList())
            dismiss()
        }
        binding.cancelButton.setOnClickListener {
            dismiss()
            cancelListener?.invoke()
        }
        binding.voice.setOnClickListener {
            this.voiceListener?.invoke(adapter.message.map {
                it.data
            }.toMutableList())
            dismiss()
        }
    }


    fun setCancelListener(listener: () -> Unit) {
        this.cancelListener = listener
    }

    fun setOkListener(listener: (MutableList<Message>) -> Unit) {
        this.okListener = listener
    }

    fun setVoiceListener(listener: (MutableList<Message>) -> Unit) {
        this.voiceListener = listener

    }

    private fun getCurrentDateTime(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return dateFormat.format(Date())
    }

    inner class MessageAdapter(
        val message: MutableList<MessageUIStatus>
    ) : RecyclerView.Adapter<MessageAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_add_message, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val msg = message[position]
            if (msg.data.type == 0) {
                holder.messageItem.visibility = View.VISIBLE
                holder.voiceItem.visibility = View.GONE
                holder.message.text = msg.data.value
            } else {
                holder.messageItem.visibility = View.GONE
                holder.voiceItem.visibility = View.VISIBLE
            }
            when (msg.status) {
                0 -> {
                    holder.stop.visibility = View.GONE
                    holder.play.visibility = View.VISIBLE
                    holder.progress.visibility = View.GONE
                }

                1 -> {
                    holder.stop.visibility = View.GONE
                    holder.play.visibility = View.GONE
                    holder.progress.visibility = View.VISIBLE
                }

                2 -> {
                    holder.stop.visibility = View.VISIBLE
                    holder.play.visibility = View.GONE
                    holder.progress.visibility = View.GONE
                }
            }
            holder.second.text = "${msg.data.second}s"
            holder.dateTime.text = msg.data.inputTime
            holder.play.setOnClickListener {
                if (msg.data.type == 1) {
                    if (AudioPlayer.getInstance(requireContext()).isPlaying()) {
                        AudioPlayer.getInstance(requireContext()).stop()
                        message.forEach {
                            it.status = 0
                        }
                        notifyDataSetChanged()
                    }
                    msg.status = 1
                    notifyDataSetChanged()
                    lifecycleScope.launch(Dispatchers.IO) {
                        val bytes = msg.data.value?.let { it1 -> DownloadUtils.downloadFile(it1) }
                        if (bytes != null) {
                            val file = saveBytesToCache(bytes, "${UUID.randomUUID().toString()}", requireContext())
                            launch(Dispatchers.Main) {
                                msg.status = 2
                                notifyDataSetChanged()
                                AudioPlayer.getInstance(requireContext()).play(file?.path.orEmpty()) {
                                    msg.status = 0
                                    notifyDataSetChanged()
                                }
                            }
                        }
                    }
                }
            }
            holder.stop.setOnClickListener {
                AudioPlayer.getInstance(requireContext()).stop()
                msg.status = 0
                notifyDataSetChanged()
            }
            holder.delete.setOnClickListener {
                message.removeAt(position)
                notifyDataSetChanged()
            }
        }

        override fun getItemCount(): Int {
            return message.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val play: ImageView = itemView.findViewById(R.id.play)
            val stop: ImageView = itemView.findViewById(R.id.stop)
            val progress: ProgressBar = itemView.findViewById(R.id.progress)
            val delete: ImageView = itemView.findViewById(R.id.delete)
            val messageItem: LinearLayout = itemView.findViewById(R.id.messageItem)
            val voiceItem: LinearLayout = itemView.findViewById(R.id.voiceItem)
            val message: TextView = itemView.findViewById(R.id.message)
            val dateTime: TextView = itemView.findViewById(R.id.dateTime)
            val second: TextView = itemView.findViewById(R.id.second)
        }
    }

    fun saveBytesToCache(bytes: ByteArray, fileName: String, context: Context): File? {
        val cacheDir = context.cacheDir
        val file = File(cacheDir, fileName)

        try {
            FileOutputStream(file).use { outputStream ->
                outputStream.write(bytes)
            }
            return file
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
    }

    override fun onPause() {
        super.onPause()
        AudioPlayer.getInstance(requireContext()).stop()
    }
}
