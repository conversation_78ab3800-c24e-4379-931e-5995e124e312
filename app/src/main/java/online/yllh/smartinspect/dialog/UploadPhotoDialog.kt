package online.yllh.smartinspect.dialog

import android.app.Dialog
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.TextView
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogLayoutUploadImageBinding
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.network.model.response.CloudAlbumModel

class UploadPhotoDialog(context: Context, val dataSource: List<CloudAlbumModel>) : Dialog(context) {
    private var binding = DialogLayoutUploadImageBinding.inflate(context.layoutInflater, null, false)
    var selectId: String? = null

    init {
        setContentView(binding.root)
        setCanceledOnTouchOutside(false)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        window?.setDimAmount(0.4f)
        setDataSource()
        binding.cancelButton.setOnClickListener {
            dismiss()
        }
    }

    fun setOkButtonListener(listener: (String?) -> Unit) {
        binding.okButton.setOnClickListener {
            dismiss()
            listener(selectId)
        }
    }

    fun setDataSource() {
        // 创建适配器
        val adapter = CloudAlbumAdapter(context, dataSource)

        // 设置下拉框的样式
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

        // 将适配器设置给 Spinner
        binding.spinner.adapter = adapter

        // 设置选择监听器
        binding.spinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                val selectedAlbum = dataSource[position]
                selectId = selectedAlbum.id
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
                if (dataSource.size > 0) {
                    val selectedAlbum = dataSource.first()
                    selectId = selectedAlbum.id
                }
            }
        }
    }

    inner class CloudAlbumAdapter(context: Context, private val albums: List<CloudAlbumModel>) :
        ArrayAdapter<CloudAlbumModel>(context, 0, albums) {

        override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
            var listItemView = convertView
            if (listItemView == null) {
                listItemView = context.layoutInflater.inflate(
                    android.R.layout.simple_spinner_item,
                    parent,
                    false
                )
            }

            val albumNameTextView = listItemView!!.findViewById<TextView>(android.R.id.text1)
            val album = albums[position]
            albumNameTextView.text = album.albumName

            return listItemView
        }

        override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
            var dropDownItemView = convertView
            if (dropDownItemView == null) {
                dropDownItemView = context.layoutInflater.inflate(
                    android.R.layout.simple_spinner_dropdown_item,
                    parent,
                    false
                )
            }

            val albumNameTextView = dropDownItemView!!.findViewById<TextView>(android.R.id.text1)
            val album = albums[position]
            albumNameTextView.text = album.albumName

            return dropDownItemView
        }
    }
}
