package online.yllh.smartinspect.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogEditWatermarkBinding
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.view.camera.CameraViewModel

class EditWatermarkDialog(
    context: Context,
    private val viewModel: CameraViewModel,
) : Dialog(context) {
    private val binding = DialogEditWatermarkBinding.inflate(context.layoutInflater)
    private val lifecycleScope = MainScope()

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        window?.setDimAmount(0.4f)

        val watermarkState = viewModel.watermarkState.value
        binding.switchTitle.isChecked = watermarkState.showTitle
        binding.switchTime.isChecked = watermarkState.showTime
        binding.switchWeather.isChecked = watermarkState.showWeather
        binding.switchLocation.isChecked = watermarkState.showLocation
        binding.switchAltitude.isChecked = watermarkState.showAltitude
        binding.switchAzimuth.isChecked = watermarkState.showAzimuth
        binding.switchAddress.isChecked = watermarkState.showAddress
        binding.cancelButton.setOnClickListener {
            cancel()
        }
        binding.okButton.setOnClickListener {
            viewModel.watermarkState.value = watermarkState.copy(
                showTitle = binding.switchTitle.isChecked,
                showTime = binding.switchTime.isChecked,
                showWeather = binding.switchWeather.isChecked,
                showLocation = binding.switchLocation.isChecked,
                showAltitude = binding.switchAltitude.isChecked,
                showAzimuth = binding.switchAzimuth.isChecked,
                showAddress = binding.switchAddress.isChecked,
            )
            dismiss()
        }
        lifecycleScope.launch {
            viewModel.watermarkState.collect {
                binding.tvTitle.text = it.title
                binding.tvTime.text = "拍摄时间：${it.readableTime}"
                binding.tvWeather.text = "天气：${it.weatherDesc}"
                binding.tvLocation.text = "定位：${it.locationDesc}"
                binding.tvAltitude.text = "海拔：${it.altitudeDesc}"
                binding.tvAzimuth.text = "角度：${it.azimuthDesc}"
                binding.tvAddress.text = "地点：${it.address}"
            }
        }
    }

    override fun dismiss() {
        super.dismiss()
        lifecycleScope.cancel()
    }
}
