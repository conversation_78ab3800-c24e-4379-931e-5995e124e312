package online.yllh.smartinspect.dialog

import android.app.Dialog
import android.content.Context
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogLayoutClearCacheBinding
import online.yllh.smartinspect.extension.layoutInflater

class ClearCacheDialog(context: Context) : Dialog(context) {
    private var binding = DialogLayoutClearCacheBinding.inflate(context.layoutInflater, null, false)

    init {
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        window?.setDimAmount(0.4f)
    }

    fun setCancelListener(listener: () -> Unit) {
        binding.cancelButton.setOnClickListener {
            dismiss()
            listener()
        }
    }

    fun setOkListener(listener: () -> Unit) {
        binding.okButton.setOnClickListener {
            dismiss()
            listener()
        }
    }
}
