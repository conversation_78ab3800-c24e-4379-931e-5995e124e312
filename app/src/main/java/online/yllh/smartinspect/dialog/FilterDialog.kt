package online.yllh.smartinspect.dialog

import android.app.DatePickerDialog
import android.app.Dialog
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.TextView
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogLayoutFilterProjectListBinding
import online.yllh.smartinspect.extension.layoutInflater
import java.util.Calendar

class FilterDialog(context: Context) : Dialog(context) {
    private var binding = DialogLayoutFilterProjectListBinding.inflate(context.layoutInflater, null, false)
    var okButtonClick: ((String, String, String, String, String) -> Unit)? = null
    var status = ""

    init {
        setContentView(binding.root)
        binding.startTimeText.text = "选择项目开始时间"
        binding.endTimeText.text = "选择项目结束时间"
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        window?.setDimAmount(0.4f)
        binding.cancelButton.setOnClickListener {
            dismiss()
        }
        binding.okButton.setOnClickListener {
            dismiss()
            if (binding.startTimeText.text == "选择项目开始时间") {
                binding.startTimeText.text = ""
            }
            if (binding.endTimeText.text == "选择项目结束时间") {
                binding.endTimeText.text = ""
            }
            okButtonClick?.invoke(
                binding.name.text.toString(),
                binding.owner.text.toString(),
                status,
                binding.startTimeText.text.toString(),
                binding.endTimeText.text.toString()
            )
        }
        binding.startTime.setOnClickListener {
            val calendar = Calendar.getInstance()
            val year = calendar.get(Calendar.YEAR)
            val month = calendar.get(Calendar.MONTH)
            val day = calendar.get(Calendar.DAY_OF_MONTH)
            val datePickerDialog = DatePickerDialog(
                context,
                { view, selectedYear, selectedMonth, selectedDayOfMonth ->
                    // 处理用户选择的日期
                    val selectedDate = "$selectedYear-${selectedMonth + 1}-$selectedDayOfMonth"
                    binding.startTimeText.text = selectedDate
                },
                year,
                month,
                day
            )
            datePickerDialog.show()
        }
        binding.endTimeText.setOnClickListener {
            val calendar = Calendar.getInstance()
            val year = calendar.get(Calendar.YEAR)
            val month = calendar.get(Calendar.MONTH)
            val day = calendar.get(Calendar.DAY_OF_MONTH)
            val datePickerDialog = DatePickerDialog(
                context,
                { view, selectedYear, selectedMonth, selectedDayOfMonth ->
                    // 处理用户选择的日期
                    val selectedDate = "$selectedYear-${selectedMonth + 1}-$selectedDayOfMonth"
                    binding.endTimeText.text = selectedDate
                },
                year,
                month,
                day
            )
            datePickerDialog.show()
        }
        setDataSource()
    }

    fun setDataSource() {
        val dataSource = arrayListOf("选择项目状态", "未开始", "已开始", "完成")
        // 创建适配器
        val adapter = SpinnerAdapter(context, dataSource)

        // 设置下拉框的样式
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

        // 将适配器设置给 Spinner
        binding.statusSelect.adapter = adapter

        // 设置选择监听器
        binding.statusSelect.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                status = if (position == 0) {
                    ""
                } else {
                    "${position - 1}"
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
                status = ""
            }
        }
    }

    inner class SpinnerAdapter(context: Context, val dataSource: List<String>) :
        ArrayAdapter<String>(context, 0, dataSource) {

        override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
            var listItemView = convertView
            if (listItemView == null) {
                listItemView = context.layoutInflater.inflate(
                    android.R.layout.simple_spinner_item,
                    parent,
                    false
                )
            }

            val albumNameTextView = listItemView!!.findViewById<TextView>(android.R.id.text1)
            albumNameTextView.text = dataSource[position]

            return listItemView
        }

        override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
            var dropDownItemView = convertView
            if (dropDownItemView == null) {
                dropDownItemView = context.layoutInflater.inflate(
                    android.R.layout.simple_spinner_dropdown_item,
                    parent,
                    false
                )
            }

            val albumNameTextView = dropDownItemView!!.findViewById<TextView>(android.R.id.text1)
            if (position == 0) {
                albumNameTextView.text = "无"
            } else {
                albumNameTextView.text = dataSource[position]
            }

            return dropDownItemView
        }
    }
}
