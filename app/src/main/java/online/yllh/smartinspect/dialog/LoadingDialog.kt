package online.yllh.smartinspect.dialog

import android.app.Dialog
import android.content.Context
import android.view.View
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogLayoutLoadingBinding
import online.yllh.smartinspect.extension.layoutInflater

class LoadingDialog(context: Context) : Dialog(context) {
    private var binding = DialogLayoutLoadingBinding.inflate(context.layoutInflater, null, false)

    init {
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        window?.setDimAmount(0.4f)
    }

    fun setTitle(title: String) {
        binding.title.text = title
    }

    fun setMessage(message: String) {
        binding.message.text = message
        binding.message.visibility = View.VISIBLE
    }

    fun setButtonVisibility(visibility: Int) {
        binding.okButton.visibility = visibility
    }

    fun setButtonListener(listener: () -> Unit) {
        binding.okButton.setOnClickListener {
            dismiss()
            listener()
        }
    }
}
