package online.yllh.smartinspect.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.ViewGroup
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogWatermarkTimeFormatSelectBinding
import online.yllh.smartinspect.databinding.ItemFormatBinding
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.view.camera.CameraViewModel
import java.text.SimpleDateFormat
import java.util.Locale

class WatermarkTimeFormatSelectDialog(context: Context, private val viewModel: CameraViewModel) : Dialog(context) {
    private val binding = DialogWatermarkTimeFormatSelectBinding.inflate(context.layoutInflater)
    private val lifecycleScope = MainScope()
    private var selectedPos = -1

    private val formats = listOf(
        "yyyy-MM-dd HH:mm:ss",
        "yyyy-MM-dd HH:mm",
        "yyyy-MM-dd E",
        "E HH:mm",
        "yyyy-MM-dd",
        "yyyy-MM-dd E HH:mm",
        "E",
        "HH:mm",
        "a HH:mm yyyy.M.d E",
    ).map { SimpleDateFormat(it, Locale.getDefault()) }

    @SuppressLint("NotifyDataSetChanged")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        selectedPos = formats.indexOfFirst { it.toPattern() == viewModel.watermarkState.value.timeFormat }
        (binding.formatList.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        binding.formatList.adapter = object : RecyclerView.Adapter<FormatViewHolder>() {
            override fun getItemCount() = formats.size
            override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FormatViewHolder {
                val holder = FormatViewHolder(ItemFormatBinding.inflate(parent.context.layoutInflater, parent, false))
                holder.binding.root.setOnClickListener {
                    val pos = holder.bindingAdapterPosition
                    if (pos == RecyclerView.NO_POSITION)
                        return@setOnClickListener
                    val preSelectedPos = selectedPos
                    selectedPos = pos
                    if (preSelectedPos != -1 && preSelectedPos != pos) {
                        notifyItemChanged(preSelectedPos)
                        notifyItemChanged(pos)
                    } else if (preSelectedPos == -1) {
                        notifyItemChanged(pos)
                    }
                    lifecycleScope.launch {
                        delay(300)
                        viewModel.watermarkState.value = viewModel.watermarkState.value.copy(timeFormat = formats[pos].toPattern())
                        dismiss()
                    }
                }
                return holder
            }

            override fun onBindViewHolder(holder: FormatViewHolder, position: Int) {
                val time = viewModel.watermarkState.value.time
                val timeFormatted = formats[position].format(time)
                holder.binding.tvFormat.text = timeFormatted
                holder.binding.tvFormat.isSelected = position == selectedPos
                holder.binding.ivCheck.isInvisible = position != selectedPos
                holder.binding.divider.isVisible = position != formats.size - 1
            }
        }.also { adapter ->
            lifecycleScope.launch {
                viewModel.watermarkState.map { it.time }.distinctUntilChanged().collect {
                    adapter.notifyDataSetChanged()
                }
            }
        }
    }

    override fun dismiss() {
        super.dismiss()
        lifecycleScope.cancel()
    }

    private class FormatViewHolder(val binding: ItemFormatBinding) : RecyclerView.ViewHolder(binding.root)
}
