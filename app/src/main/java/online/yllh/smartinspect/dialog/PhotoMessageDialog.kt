package online.yllh.smartinspect.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.FrameLayout
import androidx.fragment.app.DialogFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.view.message.PhotoMessageFragment

class PhotoMessageDialog(
    private val messageListJson: String,
    private val imageId: String
) : DialogFragment() {

    private lateinit var photoMessageFragment: PhotoMessageFragment
    private lateinit var containerLayout: FrameLayout

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // 创建一个FrameLayout作为容器
        containerLayout = FrameLayout(requireContext()).apply {
            id = View.generateViewId()
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            setBackgroundResource(R.drawable.dialog_rounded_bg)
        }
        
        // 设置dialog样式
        dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        
        return containerLayout
    }

    override fun onStart() {
        super.onStart()
        // 设置dialog大小和边距
        val window = dialog?.window
        val displayMetrics = resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels
        val margin = (24 * displayMetrics.density).toInt() // 24dp转px
        
        window?.setLayout(
            screenWidth - margin * 2,
            screenHeight - margin * 2
        )
        
        // 设置dialog居中显示和圆角效果
        val layoutParams = window?.attributes
        layoutParams?.let {
            it.width = screenWidth - margin * 2
            it.height = screenHeight - margin * 2
            window.attributes = it
        }
        
        // 确保圆角背景正确显示
        window?.setBackgroundDrawableResource(android.R.color.transparent)
        window?.decorView?.setBackgroundResource(android.R.color.transparent)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 创建PhotoMessageFragment实例
        photoMessageFragment = PhotoMessageFragment()
        
        // 传递参数给Fragment
        val args = Bundle().apply {
            putString(FragmentArguments.MessageList, messageListJson)
            putString(FragmentArguments.ImageId, imageId)
            putBoolean("isInDialog", true) // 标记这个Fragment在dialog中
        }
        photoMessageFragment.arguments = args
        
        // 设置返回回调
        photoMessageFragment.setBackCallback {
            dismiss()
        }
    }

    override fun onResume() {
        super.onResume()
        
        // 在onResume中添加Fragment，确保Dialog已经完全初始化
        if (!photoMessageFragment.isAdded) {
            childFragmentManager.beginTransaction()
                .add(containerLayout.id, photoMessageFragment)
                .commitAllowingStateLoss()
        }
    }

    fun setUploadListener(listener: (Any) -> Unit) {
        // 可以在这里设置回调监听器
    }
}
