package online.yllh.smartinspect.dialog

import android.app.Dialog
import android.content.Context
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogLayoutLogoutBinding
import online.yllh.smartinspect.extension.layoutInflater

class LogoutDialog(context: Context) : Dialog(context) {
    private var binding = DialogLayoutLogoutBinding.inflate(context.layoutInflater, null, false)

    init {
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        window?.setDimAmount(0.4f)
    }

    fun setCancelListener(listener: () -> Unit) {
        binding.cancelButton.setOnClickListener {
            dismiss()
            listener()
        }
    }

    fun setOkListener(listener: () -> Unit) {
        binding.okButton.setOnClickListener {
            dismiss()
            listener()
        }
    }

    fun setUserName(string: String) {
        binding.nickName.text = string
    }

    fun setAvatar(imageUrl: String) {
        Glide.with(context)
            .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
            .load(imageUrl)
            .placeholder(R.drawable.ic_defult_avatar)
            .into(binding.avatar)
    }
}
