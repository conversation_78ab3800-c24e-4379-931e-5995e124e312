package online.yllh.smartinspect.dialog

import android.app.Dialog
import android.content.Context
import android.view.View
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogLayoutTipsBinding
import online.yllh.smartinspect.extension.layoutInflater

class TipsDialog(context: Context) : Dialog(context) {
    private var binding = DialogLayoutTipsBinding.inflate(context.layoutInflater, null, false)

    init {
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        window?.setDimAmount(0.4f)
    }

    fun setOkListener(listener: () -> Unit) {
        binding.okButton.setOnClickListener {
            dismiss()
            listener()
        }
    }

    fun setCancelListener(listener: () -> Unit) {
        binding.cancelButton.visibility = View.VISIBLE
        binding.cancelButton.setOnClickListener {
            dismiss()
            listener()
        }
    }

    fun setTitle(title: String) {
        binding.dialogTitle.text = title
    }
}
