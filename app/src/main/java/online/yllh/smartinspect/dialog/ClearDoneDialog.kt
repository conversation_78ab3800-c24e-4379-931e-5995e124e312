package online.yllh.smartinspect.dialog

import android.app.Dialog
import android.content.Context
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogLayoutClearDoneBinding
import online.yllh.smartinspect.extension.layoutInflater

class ClearDoneDialog(context: Context) : Dialog(context) {
    private var binding = DialogLayoutClearDoneBinding.inflate(context.layoutInflater, null, false)

    init {
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        window?.setDimAmount(0.4f)
    }

    fun setCacheText(text: String) {
        binding.clearText.text = text
    }

    fun setOkListener(listener: () -> Unit) {
        binding.okButton.setOnClickListener {
            dismiss()
            listener()
        }
    }
}
