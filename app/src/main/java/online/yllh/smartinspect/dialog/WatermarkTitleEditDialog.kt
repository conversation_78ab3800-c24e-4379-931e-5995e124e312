package online.yllh.smartinspect.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.widget.Toast
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.DialogWatermarkTitleEditBinding
import online.yllh.smartinspect.extension.layoutInflater

class WatermarkTitleEditDialog(context: Context, private val title: String, private val onConfirm: (title: String) -> Unit) : Dialog(context) {
    private val binding = DialogWatermarkTitleEditBinding.inflate(context.layoutInflater)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        window?.setBackgroundDrawableResource(R.drawable.dialog_bg)
        binding.titleInput.setText(title)
        binding.cancelButton.setOnClickListener { cancel() }
        binding.okButton.setOnClickListener {
            val text = binding.titleInput.text.toString().trim()
            if (text.isEmpty()) {
                Toast.makeText(context, "标题不能为空", Toast.LENGTH_SHORT).show()
            } else {
                dismiss()
                onConfirm(text)
            }
        }
    }
}
