package online.yllh.smartinspect.network

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import online.yllh.smartinspect.BuildConfig
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.provider.ApplicationContextProvider
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit


object RetrofitClient {

    private const val BASE_URL = "${BuildConfig.API_URL}/api/"

    val okHttpClient = OkHttpClient.Builder()
        .callTimeout(10, TimeUnit.SECONDS)
        .addInterceptor(HttpLoggingInterceptor {
            Log.d("RetrofitClient", it)
        }.apply {
            level = if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE
        })
        .addInterceptor { chain ->
            val request = chain.request()
            val nonToken = request.header("token").isNullOrEmpty()
            val newRequest = if (nonToken) {
                val context = ApplicationContextProvider.context
                val prefs = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                val token = prefs.getString(SpKey.UserCookies, "").orEmpty()
                request.newBuilder().header("token", token).build()
            } else request
            chain.proceed(newRequest)
        }.build()

    var gson: Gson = GsonBuilder()
        .setDateFormat("yyyy-MM-dd hh:mm:ss")
        .create()

    val retrofit: Retrofit = Retrofit.Builder()
        .baseUrl(BASE_URL)
        .addConverterFactory(GsonConverterFactory.create(gson))
        .client(okHttpClient)
        .build()

    val service: Service = retrofit.create(Service::class.java)
}
