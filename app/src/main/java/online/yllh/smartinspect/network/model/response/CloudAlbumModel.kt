package online.yllh.smartinspect.network.model.response

data class CloudAlbumModel(
    val createTime: String?,
    val updateTime: String?,
    val delFlag: String?,
    val id: String?,
    val albumName: String?,
    val albumCover: String?,
    val startingTime: String?,
    val status: String?,
    val creatorUserId: String?,
    val creatorPhone: String?,
    val nickName: String?,
    val parentAlbumId: Int?,
    val photoInfos: List<Any>?,
    val palbumVOS: List<CloudAlbumModel>?,
    val isProjectAlbum: String?,
    val projectId: Int?
)
