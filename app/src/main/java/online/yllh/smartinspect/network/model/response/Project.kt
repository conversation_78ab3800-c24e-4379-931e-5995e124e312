package online.yllh.smartinspect.network.model.response

interface IdNameModel {
    fun getModelId(): String?
    fun getModelName(): String?
}

data class Project(
    val createTime: String?,
    val updateTime: String?,
    val delFlag: String?,
    val id: String?,
    val name: String?,
    val description: String?,
    val cover: String?,
    val initiatorId: String?,
    val startTime: String?,
    val endTime: String?,
    val status: String?,
    val isCreator: String?,
    val initiatorName: String?,
    val albumVOS: List<InnerAlbum>?
) : IdNameModel {
    override fun getModelId() = id
    override fun getModelName() = name
}

data class InnerAlbum(
    val createTime: String? = null,
    val updateTime: String? = null,
    val delFlag: String? = null,
    val id: String? = null,
    val albumName: String? = null,
    val albumCover: String? = null,
    val startingTime: String? = null,
    val status: String? = null,
    val creatorUserId: String? = null,
    val creatorPhone: String? = null,
    val nickName: String? = null,
    val parentAlbumId: Int? = null,
    val photoInfos: List<Any>? = null,
    val palbumVOS: List<InnerAlbum>? = null,
) : IdNameModel {
    override fun getModelId() = id
    override fun getModelName() = albumName
}
