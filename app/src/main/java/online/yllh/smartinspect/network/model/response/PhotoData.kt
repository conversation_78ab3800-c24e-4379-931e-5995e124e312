package online.yllh.smartinspect.network.model.response

data class PhotoData(
    val createTime: String?,
    val updateTime: String?,
    val delFlag: String?,
    val id: String?,
    val photoAddress: String?,
    val authorUserId: String?,
    val authorNickName: String?,
    val authorPhone: String,
    val shootingTime: String,
    val programDate: String?,
    val resolutionRatio: String,
    val width: String?,
    val height: String?,
    val bitDepth: String?,
    val compress: String?,
    val resolutionUnit: String?,
    val color: String?,
    val colorPixel: String?,
    val isRemark: String?,
    val customMessages: List<Message>?,
    val voiceMessages: List<Message>?,
    val advancedInfo: AdvancedInfo?,
    val photoCameraInfo: PhotoCameraInfo?,
    val photoSensorInfo: String?,
    val timeValue: String?,
    val yresolution: String?,
    val xresolution: String?
)

data class AdvancedInfo(
    val createTime: String,
    val updateTime: String?,
    val delFlag: String,
    val id: String,
    val photoInfoId: Int,
    val cameraLensManufacturer: String?,
    val cameraLensModel: String?,
    val flashlightManufacturer: String?,
    val flashlightModel: String?,
    val cameraSn: String?,
    val contrast: String?,
    val luminance: String?,
    val lightSource: String?,
    val exposureProgram: String,
    val saturability: String?,
    val definition: String?,
    val whiteBalance: String?,
    val luminosityExplain: String?,
    val figureZoom: String?,
    val exifVersion: String?,
    val ycbcrPositioning: String,
    val exposureTime: String?,
    val isoSpeedRatings: String?,
    val componentConfiguration: String?,
    val shutterSpeedValue: String?,
    val apertureValue: String,
    val exposureBiasValue: String,
    val maxApertureValue: String,
    val meteringMode: String?,
    val flash: String?,
    val focalLength: String?,
    val gpsLatitude: String,
    val gpsLongitude: String,
    val fnumber: String?
)

data class PhotoCameraInfo(
    val createTime: String,
    val updateTime: String?,
    val delFlag: String,
    val id: String,
    val photoInfoId: Int,
    val programDate: String?,
    val resolutionRatio: String?,
    val width: String?,
    val height: String?,
    val bitDepth: String?,
    val compress: String?,
    val resolutionUnit: String?,
    val color: String?,
    val colorPixel: String?,
    val yresolution: String?,
    val xresolution: String?
)

data class Message(
    val createTime: String? = null,
    val updateTime: String? = null,
    val delFlag: String? = null,
    val id: String? = null,
    val primaryId: Int? = null,
    val value: String? = null,
    val type: Int? = null,
    val inputTime: String?,
    val second: Int? = 0,
    val lockStatus: String? = null,
)

data class CustomMessage(
    val createTime: String?,
    val updateTime: String?,
    val delFlag: String?,
    val id: String?,
    val primaryId: String?,
    val primaryType: String?,
    val value: String?,
    val voiceValue: String?,
    val type: String?,
    val second: Int?,
    val inputTime: String?,
    val lockStatus: String?
)

data class Photo(
    val createTime: String?,
    val updateTime: String?,
    val delFlag: String?,
    val id: String?,
    val photoAddress: String?,
    val authorUserId: String?,
    val authorNickName: String?,
    val authorPhone: String?,
    val shootingTime: String?,
    val programDate: String?,
    val resolutionRatio: String?,
    val width: String?,
    val height: String?,
    val bitDepth: String?,
    val compress: String?,
    val resolutionUnit: String?,
    val color: String?,
    val colorPixel: String?,
    val isRemark: String?,
    val operatingRecords: String?,
    val customMessages: List<CustomMessage>?,
    val voiceMessages: Any?,
    val advancedInfo: Any?,
    val photoCameraInfo: Any?,
    val photoSensorInfo: Any?,
    val timeValue: Any?,
    val yresolution: String?,
    val xresolution: String?
)
