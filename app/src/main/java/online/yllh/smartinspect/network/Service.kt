package online.yllh.smartinspect.network

import okhttp3.MultipartBody
import online.yllh.smartinspect.model.AlbumTree
import online.yllh.smartinspect.model.WeatherResults
import online.yllh.smartinspect.network.model.request.ChangeUserModel
import online.yllh.smartinspect.network.model.request.CreateCloudAlbumModel
import online.yllh.smartinspect.network.model.request.CreateProjectAlbumModel
import online.yllh.smartinspect.network.model.request.DeletePhotos
import online.yllh.smartinspect.network.model.request.LoginUser
import online.yllh.smartinspect.network.model.request.MessageUpdate
import online.yllh.smartinspect.network.model.request.MovePhotos
import online.yllh.smartinspect.network.model.request.RegisterUser
import online.yllh.smartinspect.network.model.request.UploadImageToGallery
import online.yllh.smartinspect.network.model.response.ApkInfo
import online.yllh.smartinspect.network.model.response.BaseResponse
import online.yllh.smartinspect.network.model.response.CloudAlbumModel
import online.yllh.smartinspect.network.model.response.CloudPhotoAlbumModel
import online.yllh.smartinspect.network.model.response.Message
import online.yllh.smartinspect.network.model.response.Photo
import online.yllh.smartinspect.network.model.response.PhotoData
import online.yllh.smartinspect.network.model.response.Project
import online.yllh.smartinspect.network.model.response.UserInfo
import online.yllh.smartinspect.network.model.response.UserLogin
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Query

interface Service {
    @POST("user/register")
    fun registerUser(@Body user: RegisterUser): Call<BaseResponse<UserLogin>>

    @POST("user/forgetPassWord")
    fun forgetPassWord(@Body user: RegisterUser): Call<BaseResponse<Boolean>>

    @GET("sms/smsByRegister")
    fun sendSmsCode(@Query("phone") phone: String): Call<BaseResponse<Boolean>>

    @GET("sms/smsByUpdatePhone")
    fun smsByUpdatePhone(@Query("phone") phone: String): Call<BaseResponse<Boolean>>

    @GET("sms/smsByForgetPassWord")
    fun smsByForgetPassWord(@Query("phone") phone: String): Call<BaseResponse<Boolean>>

    @POST("user/appLogin")
    fun appLogin(@Body user: LoginUser): Call<BaseResponse<UserLogin>>

    @GET("user/getUserInfoBySession")
    fun getOneByUser(): Call<BaseResponse<UserInfo>>

    @POST("user/appUpdateUser")
    fun changeUserInfo(@Body user: ChangeUserModel): Call<BaseResponse<Boolean>>

    @GET("albumInfo/selectAlbumInfoByUserInfo")
    fun getAlbumInfoByUserInfo(): Call<BaseResponse<List<CloudAlbumModel>>>

    @GET("albumInfo/getAlbumInfos")
    @Headers("Content-Type: application/json")
    fun getAlbumInfos(): Call<BaseResponse<List<CloudAlbumModel>>>

    @POST("albumInfo/saOrUpAlbumInfo")
    fun createCloudAlbum(@Body album: CreateCloudAlbumModel): Call<BaseResponse<Boolean>>

    @POST("upload/upload")
    @Multipart
    fun uploadFile(@Part file: MultipartBody.Part): Call<BaseResponse<String>>

    @POST("photoInfo/savePhotoInfoInCloudAlbum")
    fun saveImageToCloudAlbum(@Body image: UploadImageToGallery): Call<BaseResponse<String>>

    @POST("photoInfo/saveMessages")
    fun saveMessage(@Body image: UploadImageToGallery): Call<BaseResponse<String>>

    @GET("albumInfo/appSelectPhotosByAlbum")
    fun getPhotosByAlbum(@Query("albumId") albumId: String): Call<BaseResponse<List<CloudPhotoAlbumModel>>>

    @GET("photoInfo/getPhotoById")
    fun getPhotoById(@Query("id") albumId: String): Call<BaseResponse<PhotoData>>

    @GET("project/projectList")
    fun getProjectList(
        @Query("id") id: String? = null,
        @Query("status") status: String? = null,
        @Query("initiatorName") initiatorName: String? = null,
        @Query("name") name: String? = null,
        @Query("createEndTime") createEndTime: String? = null,
        @Query("createBeginTime") createBeginTime: String? = null,
    ): Call<BaseResponse<List<Project>>>

    @GET("photoInfo/updateMessageLock")
    fun updateMessageLock(@Query("messageId") messageId: String?, @Query("lock") lock: String?): Call<BaseResponse<Message>>

    @DELETE("albumInfo/deleteAlbum")
    fun deleteAlbum(@Query("albumId") albumId: String): Call<BaseResponse<String>>

    @GET("albumInfo/appSelectPhotosVOByAlbum")
    fun appSelectPhotosVOByAlbum(@Query("albumId") albumId: String): Call<BaseResponse<List<Photo>>>

    @DELETE("message/deleteMessageInfo")
    fun deleteMessageInfo(@Query("messageId") messageId: String): Call<BaseResponse<String>>

    @POST("message/updateMessageInfo")
    fun updateMessageInfo(@Body message: MessageUpdate): Call<BaseResponse<String>>

    @POST("message/insertPhotoMessageInfo")
    fun insertPhotoMessageInfo(@Body message: MessageUpdate): Call<BaseResponse<String>>

    @GET("albumInfo/getAlbumInfosByAlbumIds")
    fun getAlbumInfosByAlbumIds(@Query("albumId") albumId: String): Call<BaseResponse<List<CloudAlbumModel>>>

    @POST("project/savePAlbum")
    fun savePAlbum(@Body album: CreateProjectAlbumModel): Call<BaseResponse<String>>

    @POST("albumInfo/albumDelPhoto")
    fun albumDelPhoto(@Body deletePhotos: DeletePhotos): Call<BaseResponse<String>>

    @POST("albumInfo/albumMovePhoto")
    fun albumMovePhoto(@Body movePhotos: MovePhotos): Call<BaseResponse<String>>

    @GET("project/projectListAlbum")
    fun projectListAlbum(): Call<BaseResponse<List<Project>>>

    @GET("apk/verifyApkVersion")
    suspend fun verifyApkVersion(@Query("apkVersion") apkVersion: String): BaseResponse<Boolean>

    @GET("apk/getNewestApkUrl")
    suspend fun getNewestApkUrl(): BaseResponse<ApkInfo>

    @GET("project/projectListById")
    suspend fun getProjAlbumsByProjId(@Query("id") id: String): BaseResponse<List<AlbumTree>>

    // 其实直接调用的三方接口
    // https://api.seniverse.com/v3/weather/now.json?key=S2mzOjWWzO-wa9wvp&location=30:104&language=zh-Hans&unit=c
    @GET("albumInfo/getWeather")
    suspend fun getWeather(@Query("latitude") latitude: String, @Query("longitude") longitude: String): BaseResponse<WeatherResults>
}
