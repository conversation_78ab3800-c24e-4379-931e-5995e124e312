package online.yllh.smartinspect.network

import android.content.Context
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.core.content.edit
import androidx.navigation.findNavController
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.network.model.request.UploadImageToGallery
import online.yllh.smartinspect.network.model.response.BaseResponse
import online.yllh.smartinspect.provider.ApplicationContextProvider
import online.yllh.smartinspect.utils.MediaTypeUtils
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import retrofit2.awaitResponse
import java.io.File
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

object PublicRequest {
    suspend fun uploadFile(file: File): String? {
        val fileUrl = suspendCoroutine {
            try {
                val mediaType = MediaTypeUtils.getMediaTypeFromFileName(file.name).toMediaType()
                val part = MultipartBody.Part.createFormData("pic", file.name, file.asRequestBody(mediaType))
                val request = RetrofitClient.service.uploadFile(part)
                request.enqueue(object : Callback<BaseResponse<String>> {
                    override fun onResponse(call: Call<BaseResponse<String>>, response: Response<BaseResponse<String>>) {
                        if (response.isSuccessful) {
                            val body = response.body()
                            if ("500" == body?.code) {
                                Toast.makeText(ApplicationContextProvider.context, response.body()?.msg, Toast.LENGTH_SHORT).show()
                                it.resume(null)
                            }
                            if (body?.data != null && body.ok) {
                                it.resume(body.data)
                            }
                        } else {
                            Toast.makeText(ApplicationContextProvider.context, "上传失败", Toast.LENGTH_SHORT).show()
                            it.resume(null)
                        }
                    }

                    override fun onFailure(call: Call<BaseResponse<String>>, t: Throwable) {
                        t.printStackTrace()
                        it.resume(null)
                    }
                })
            } catch (e: Exception) {
                e.printStackTrace()
                it.resume(null)
            }
        }

        return fileUrl
    }

    suspend fun uploadImage(context: Context, imageUrl: String, albumId: String, root: View, isImport: Boolean = false) {
        var dialog: LoadingDialog? = null
        withContext(Dispatchers.Main) {
            dialog = LoadingDialog(context)
            dialog.setTitle("正在同步云相册")
            dialog.setButtonVisibility(View.GONE)
            dialog.setCancelable(false)
            dialog.show()
        }
        withContext(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.saveImageToCloudAlbum(UploadImageToGallery(photoAddress = imageUrl, albumId = albumId))
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            if (isImport) {
                                Toast.makeText(context, "导入成功", Toast.LENGTH_SHORT).show()
                            } else {
                                Toast.makeText(context, "同步成功", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            if (isImport) {
                                Toast.makeText(context, "导入失败 请重新上传", Toast.LENGTH_SHORT).show()
                            } else {
                                Toast.makeText(context, "同步失败 请重新上传", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                launch(Dispatchers.Main) {
                    Toast.makeText(context, "网络连接失败 请重新上传", Toast.LENGTH_SHORT).show()
                }
            } finally {
                launch(Dispatchers.Main) {
                    dialog?.dismiss()
                }
            }
        }
    }
}
