package online.yllh.smartinspect.view.gallery.local

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.contents.FragmentArguments.AlbumId
import online.yllh.smartinspect.contents.FragmentArguments.AlbumName
import online.yllh.smartinspect.databinding.FragmentPhotoBinding
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.media.getAllAlbums
import online.yllh.smartinspect.model.Album


class AlbumFragment(
    private val initAlbumScrollY: Int,
    private val scrollListener: (Int) -> Unit,
    private val isImportModel: Boolean = false,
    private val importModeAlbumId: String? = null,
) : BaseFragment() {
    private lateinit var binding: FragmentPhotoBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPhotoBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        lifecycleScope.launch(Dispatchers.IO) {
            val albums = getAllAlbums(requireContext())
            launch(Dispatchers.Main) {
                binding.photoRecyclerView.adapter = AlbumAdapter(albums)
                binding.photoRecyclerView.layoutManager = GridLayoutManager(requireContext(), 3)
                binding.photoRecyclerView.scrollTo(0, initAlbumScrollY)
                binding.photoRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                    var mDy = 0
                    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                        super.onScrolled(recyclerView, dx, dy)
                        mDy += dy
                        scrollListener.invoke(mDy)
                    }
                })
            }
        }
    }

    inner class AlbumAdapter(private val photoList: List<Album>) : RecyclerView.Adapter<AlbumAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_album, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val photoUri = photoList[position]
            val context = holder.itemView.context
            val displayMetrics = context.resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val imageWidth = (screenWidth - 18.dp) / 3
            holder.photoImageView.layoutParams.width = imageWidth
            holder.photoImageView.layoutParams.height = imageWidth
            holder.delete.visibility = View.GONE
            val requestOptions = RequestOptions()
                .format(DecodeFormat.PREFER_RGB_565) // 设置图片格式为 RGB_565，降低内存消耗
                .override(imageWidth, imageWidth)
            Glide.with(holder.itemView.context)
                .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                .load(photoUri.uri)
                .placeholder(R.drawable.placeholder)
                .apply(requestOptions)
                .into(holder.photoImageView)
            holder.title.text = photoUri.name
            holder.subTitle.text = "${photoUri.photoCount}"
            holder.itemView.setOnClickListener {
                binding.root.findNavController().navigate(R.id.action_galleryFragment_to_photoAlbumFragment, Bundle().apply {
                    this.putSerializable(AlbumId, photoUri.id)
                    this.putSerializable(AlbumName, photoUri.name)
                    this.putBoolean(FragmentArguments.IsImportMode, isImportModel)
                    this.putString(FragmentArguments.ImportModeAlbumId, importModeAlbumId)
                })
            }
        }

        override fun getItemCount(): Int {
            return photoList.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val photoImageView: ImageView = itemView.findViewById(R.id.imageView)
            val title: TextView = itemView.findViewById(R.id.itemTitle)
            val subTitle: TextView = itemView.findViewById(R.id.subTitle)
            val delete: FrameLayout = itemView.findViewById(R.id.delete)
        }
    }
}
