package online.yllh.smartinspect.view.editImage

import android.app.Activity
import android.app.RecoverableSecurityException
import android.content.ContentUris
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.Toast
import online.yllh.smartinspect.uiwidget.ZoomableImageView
import androidx.core.net.toUri
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.tapapk.camera_engine.utils.Exif
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.databinding.FragmentEditImageBinding
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.dialog.TipsDialog
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.extension.readBytesFromUri
import online.yllh.smartinspect.extension.toJpegByteArray
import online.yllh.smartinspect.media.getAllImagesSortedByTime
import online.yllh.smartinspect.media.getAllPhotosInAlbum
import online.yllh.smartinspect.model.EditModel
import online.yllh.smartinspect.model.Image
import online.yllh.smartinspect.provider.ApplicationContextProvider
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import kotlin.math.roundToInt

class EditImageFragment : BaseFragment() {

    private lateinit var binding: FragmentEditImageBinding
    private val viewModel by viewModels<EditImageViewModel>()
    private val imageUri: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.ImageUri) ?: throw IllegalArgumentException("imageInfo is null")
    }
    private val albumId: String? by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.AlbumId)
    }
    private val albumType: String? by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString("albumType")
    }
    private val initialPhotoIndex: Int by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getInt("currentPhotoIndex", 0) ?: 0
    }

    private var photoList: List<Image> = emptyList()
    private lateinit var photoAdapter: EditPhotoPagerAdapter
    private var currentPhotoIndex: Int = 0
    private var currentImage: Bitmap? = null
    private val rotationStates = mutableMapOf<String, Float>()
    private var pendingDeletePhoto: Image? = null
    private var pendingUpdatedPhotoList: List<Image>? = null
    private val DELETE_REQUEST_CODE = 1001

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentEditImageBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_HIDE_BAR)
            .init()

        setupPhotoList()
        setupButtons()
    }

    private fun setupPhotoList() {
        lifecycleScope.launch(Dispatchers.IO) {
            photoList = when {
                albumId != null -> getAllPhotosInAlbum(requireContext(), albumId!!)
                albumType == "all_photos" -> getAllImagesSortedByTime(requireContext())
                else -> {
                    // 如果没有相册信息，只显示当前图片
                    val currentUri = imageUri.toUri()
                    listOf(Image("", "", currentUri, System.currentTimeMillis()))
                }
            }

            // 找到当前照片在列表中的位置
            currentPhotoIndex = if (photoList.size > 1) {
                photoList.indexOfFirst { it.uri.toString() == imageUri }.takeIf { it >= 0 } ?: initialPhotoIndex.coerceIn(0, photoList.size - 1)
            } else {
                0
            }

            launch(Dispatchers.Main) {
                setupViewPager()
            }
        }
    }

    private fun setupViewPager() {
        photoAdapter = EditPhotoPagerAdapter(photoList)
        binding.photoViewPager.adapter = photoAdapter
        binding.photoViewPager.setCurrentItem(currentPhotoIndex, false)

        updatePhotoIndicator()

        binding.photoViewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                currentPhotoIndex = position
                updatePhotoIndicator()
                // 重置旋转状态为当前照片的旋转状态
                val currentPhotoUri = photoList[position].uri.toString()
                val rotation = rotationStates[currentPhotoUri] ?: 0f
                viewModel.setImageRotate(rotation)
            }
        })

        // 观察旋转状态变化
        observeOn(viewModel.imageRotate) { rotation ->
            val currentPhotoUri = photoList[currentPhotoIndex].uri.toString()
            rotationStates[currentPhotoUri] = rotation
            photoAdapter.updateRotation(currentPhotoIndex, rotation)
        }
    }

    private fun updatePhotoIndicator() {
        binding.photoIndicator.text = "${currentPhotoIndex + 1}/${photoList.size}"
    }

    private fun getCurrentPhoto(): Image? {
        return if (photoList.isNotEmpty() && currentPhotoIndex < photoList.size) {
            photoList[currentPhotoIndex]
        } else null
    }

    private fun setupButtons() {
        binding.rotate.setOnClickListener {
            viewModel.switchImageRotate()
        }

        binding.deleteButton.setOnClickListener {
            val currentPhoto = getCurrentPhoto()
            if (currentPhoto != null) {
                showDeleteConfirmationDialog(currentPhoto)
            }
        }

        binding.cancelButton.setOnClickListener {
            binding.root.findNavController().popBackStack()
        }

        binding.okButton.setOnClickListener {
            val currentPhoto = getCurrentPhoto()
            if (currentPhoto != null) {
                lifecycleScope.launch(Dispatchers.IO) {
                    val cacheDir = File(ApplicationContextProvider.context.cacheDir.path + "/cameraTmp")
                    if (!cacheDir.exists()) {
                        cacheDir.mkdir()
                    }
                    val imageFile = File(cacheDir.path + "/" + System.currentTimeMillis() + ".jpg")
                    if (!imageFile.exists()) {
                        cacheDir.createNewFile()
                    }
                    val exifData = Exif.getExifData(currentPhoto.uri.toString().toUri().readBytesFromUri(requireContext()))
                    val imageData = Exif.exifToJpegData(currentImage?.toJpegByteArray(100), exifData)
                    val outputStream = FileOutputStream(imageFile)
                    outputStream.write(imageData)
                    outputStream.close()
                    lifecycleScope.launch(Dispatchers.Main) {
                        binding.root.findNavController().navigate(R.id.action_editImageFragment_to_watermarkEditFragment, Bundle().apply {
                            putParcelable(
                                FragmentArguments.EditModel, EditModel(
                                    imageFile.path,
                                    currentImage?.width ?: 0,
                                    currentImage?.height ?: 0,
                                    283.dp, // 使用固定宽度
                                    ((currentImage?.height ?: 0) * 283.dp / (currentImage?.width ?: 1)) // 计算对应高度
                                )
                            )
                        })
                    }
                }
            }
        }
    }

    private fun showDeleteConfirmationDialog(photo: Image) {
        val dialog = TipsDialog(requireContext())
        dialog.setTitle("确定要删除这张图片吗？")
        dialog.setCancelListener { }
        dialog.setOkListener {
            deletePhoto(photo)
        }
        dialog.show()
    }

    private fun deletePhoto(photo: Image) {
        val dialog = LoadingDialog(requireContext())
        dialog.setTitle("删除中")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // 获取当前照片的URI
                val photoUri = photo.uri
                
                // 从列表中移除照片
                val updatedPhotoList = photoList.toMutableList()
                val position = updatedPhotoList.indexOf(photo)
                if (position != -1) {
                    updatedPhotoList.removeAt(position)
                    
                    // 删除文件
                    photoUri?.let { uri ->
                        try {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                                // Android 11 及以上使用 MediaStore.createDeleteRequest
                                withContext(Dispatchers.Main) {
                                    try {
                                        val pendingIntent = MediaStore.createDeleteRequest(requireContext().contentResolver, listOf(uri))
                                        startIntentSenderForResult(
                                            pendingIntent.intentSender,
                                            DELETE_REQUEST_CODE,
                                            null,
                                            0,
                                            0,
                                            0,
                                            null
                                        )
                                        // 保存当前状态以便在 onActivityResult 中处理
                                        pendingDeletePhoto = photo
                                        pendingUpdatedPhotoList = updatedPhotoList
                                        dialog.dismiss()
                                    } catch (e: Exception) {
                                        dialog.dismiss()
                                        Toast.makeText(requireContext(), "删除失败: ${e.message}", Toast.LENGTH_SHORT).show()
                                    }
                                }
                            } else {
                                // Android 10 及以下使用 ContentResolver.delete
                                val contentResolver = requireContext().contentResolver
                                try {
                                    val deletedRows = contentResolver.delete(uri, null, null)
                                    
                                    // 更新UI
                                    withContext(Dispatchers.Main) {
                                        dialog.dismiss()
                                        
                                        if (deletedRows > 0) {
                                            Toast.makeText(requireContext(), "图片已删除", Toast.LENGTH_SHORT).show()
                                            
                                            // 更新照片列表
                                            photoList = updatedPhotoList
                                            
                                            // 如果删除后列表为空，返回上一页
                                            if (updatedPhotoList.isEmpty()) {
                                                binding.root.findNavController().popBackStack()
                                                return@withContext
                                            }
                                            
                                            // 更新适配器
                                            photoAdapter = EditPhotoPagerAdapter(photoList)
                                            binding.photoViewPager.adapter = photoAdapter
                                            
                                            // 调整当前索引
                                            currentPhotoIndex = when {
                                                position >= updatedPhotoList.size -> updatedPhotoList.size - 1
                                                else -> position
                                            }
                                            
                                            // 更新ViewPager位置和指示器
                                            binding.photoViewPager.setCurrentItem(currentPhotoIndex, true)
                                            updatePhotoIndicator()
                                            
                                            // 通知媒体库更新
                                            val intent = Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE)
                                            intent.data = uri
                                            requireContext().sendBroadcast(intent)
                                        } else {
                                            Toast.makeText(requireContext(), "删除失败", Toast.LENGTH_SHORT).show()
                                        }
                                    }
                                } catch (e: SecurityException) {
                                    // Android 10 可能会抛出权限错误
                                    withContext(Dispatchers.Main) {
                                        dialog.dismiss()
                                        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.Q) {
                                            // 在 Android 10 上使用 RecoverableSecurityException
                                            try {
                                                val exception = e as? RecoverableSecurityException
                                                val intentSender = exception?.userAction?.actionIntent?.intentSender
                                                if (intentSender != null) {
                                                    startIntentSenderForResult(
                                                        intentSender,
                                                        DELETE_REQUEST_CODE,
                                                        null,
                                                        0,
                                                        0,
                                                        0,
                                                        null
                                                    )
                                                    // 保存当前状态以便在 onActivityResult 中处理
                                                    pendingDeletePhoto = photo
                                                    pendingUpdatedPhotoList = updatedPhotoList
                                                } else {
                                                    Toast.makeText(requireContext(), "删除失败: 无法获取权限", Toast.LENGTH_SHORT).show()
                                                }
                                            } catch (ex: Exception) {
                                                Toast.makeText(requireContext(), "删除失败: ${ex.message}", Toast.LENGTH_SHORT).show()
                                            }
                                        } else {
                                            Toast.makeText(requireContext(), "删除失败: ${e.message}", Toast.LENGTH_SHORT).show()
                                        }
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            withContext(Dispatchers.Main) {
                                dialog.dismiss()
                                Toast.makeText(requireContext(), "删除失败: ${e.message}", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } ?: run {
                        withContext(Dispatchers.Main) {
                            dialog.dismiss()
                            Toast.makeText(requireContext(), "无法找到要删除的图片", Toast.LENGTH_SHORT).show()
                        }
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        dialog.dismiss()
                        Toast.makeText(requireContext(), "无法找到要删除的图片", Toast.LENGTH_SHORT).show()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    dialog.dismiss()
                    Toast.makeText(requireContext(), "删除失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == DELETE_REQUEST_CODE) {
            if (resultCode == Activity.RESULT_OK) {
                if (pendingDeletePhoto != null && pendingUpdatedPhotoList != null) {
                    // 保存临时变量用于后续比较
                    val deletedPhotoUri = pendingDeletePhoto!!.uri.toString()
                    val position = photoList.indexOfFirst { it.uri.toString() == deletedPhotoUri }
                    
                    // 更新照片列表
                    photoList = pendingUpdatedPhotoList!!
                    pendingUpdatedPhotoList = null
                    pendingDeletePhoto = null
                    
                    // 如果删除后列表为空，返回上一页
                    if (photoList.isEmpty()) {
                        binding.root.findNavController().popBackStack()
                        return
                    }
                    
                    // 更新适配器
                    photoAdapter = EditPhotoPagerAdapter(photoList)
                    binding.photoViewPager.adapter = photoAdapter
                    
                    // 调整当前索引
                    currentPhotoIndex = when {
                        position != -1 && position < photoList.size -> position
                        position >= photoList.size -> photoList.size - 1
                        else -> 0
                    }
                    
                    // 更新ViewPager位置和指示器
                    binding.photoViewPager.setCurrentItem(currentPhotoIndex, true)
                    updatePhotoIndicator()
                    
                    // 显示成功消息
                    Toast.makeText(requireContext(), "图片已删除", Toast.LENGTH_SHORT).show()
                }
            } else {
                Toast.makeText(requireContext(), "删除失败或取消", Toast.LENGTH_SHORT).show()
            }
        }
    }

    fun loadImageWithRotation(context: Context, bitmap: Bitmap, targetImageView: ZoomableImageView, degrees: Float) {
        val matrix = Matrix()
        matrix.postRotate(degrees)
        val newBitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
        if (degrees == 0f || degrees == 180f) {
            targetImageView.layoutParams.width = 283.dp
            targetImageView.layoutParams.height = ((bitmap.height / bitmap.width.toFloat()) * targetImageView.layoutParams.width).roundToInt()
            targetImageView.requestLayout()
        } else {
            targetImageView.layoutParams.width = 283.dp
            targetImageView.layoutParams.height = ((bitmap.width / bitmap.height.toFloat()) * targetImageView.layoutParams.width).roundToInt()
            targetImageView.requestLayout()
        }
        Glide.with(context)
            .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
            .load(newBitmap)
            .placeholder(R.drawable.placeholder)
            .into(targetImageView)
        if (targetImageView == photoAdapter.getCurrentImageView()) {
            currentImage = newBitmap
        }
    }

    inner class EditPhotoPagerAdapter(private val photos: List<Image>) : RecyclerView.Adapter<EditPhotoPagerAdapter.PhotoViewHolder>() {
        private val viewHolders = mutableMapOf<Int, PhotoViewHolder>()

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PhotoViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_edit_photo, parent, false)
            return PhotoViewHolder(view)
        }

        override fun onBindViewHolder(holder: PhotoViewHolder, position: Int) {
            viewHolders[position] = holder
            val photo = photos[position]
            val rotation = rotationStates[photo.uri.toString()] ?: 0f

            var bitmap: Bitmap?
            var inputStream: InputStream? = null
            try {
                inputStream = requireContext().contentResolver.openInputStream(photo.uri ?: return)
                bitmap = BitmapFactory.decodeStream(inputStream)
            } catch (e: IOException) {
                bitmap = null
                e.printStackTrace()
            } finally {
                inputStream?.close()
            }

            if (bitmap != null) {
                loadImageWithRotation(requireContext(), bitmap, holder.photoImageView, rotation)
            }

            // 设置图片源，支持全屏预览
            holder.photoImageView.setImageSource(photo.uri)
        }

        override fun getItemCount(): Int = photos.size

        fun updateRotation(position: Int, rotation: Float) {
            val holder = viewHolders[position]
            if (holder != null) {
                val photo = photos[position]
                var bitmap: Bitmap?
                var inputStream: InputStream? = null
                try {
                    inputStream = requireContext().contentResolver.openInputStream(photo.uri ?: return)
                    bitmap = BitmapFactory.decodeStream(inputStream)
                } catch (e: IOException) {
                    bitmap = null
                    e.printStackTrace()
                } finally {
                    inputStream?.close()
                }

                if (bitmap != null) {
                    loadImageWithRotation(requireContext(), bitmap, holder.photoImageView, rotation)
                }
            }
        }

        fun getCurrentImageView(): ZoomableImageView? {
            return viewHolders[currentPhotoIndex]?.photoImageView
        }
        inner class PhotoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val photoImageView: ZoomableImageView = itemView.findViewById(R.id.photoImageView)
        }
    }
}
