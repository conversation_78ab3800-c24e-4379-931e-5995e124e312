package online.yllh.smartinspect.view.gallery.unupload

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.Toast
import androidx.core.content.edit
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import online.yllh.smartinspect.AppDatabase
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentUnUploadGalleryBinding
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.dialog.TipsDialog
import online.yllh.smartinspect.dialog.ProjectUploadSelectAlbumDialog
import online.yllh.smartinspect.entity.Photo
import online.yllh.smartinspect.entity.RecycledPhoto
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.media.isPhotoExists
import online.yllh.smartinspect.network.PublicRequest
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.UploadImageToGallery
import online.yllh.smartinspect.provider.ApplicationContextProvider
import retrofit2.awaitResponse
import java.io.File

class UnUploadGalleryFragment : BaseFragment() {
    private lateinit var binding: FragmentUnUploadGalleryBinding
    private var adapter: PhotoAdapter? = null
    private var isSelectionMode = false
    private val selectedPhotos = mutableSetOf<Photo>()
    private val viewModel by viewModels<UploadPhotoViewModel>()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = FragmentUnUploadGalleryBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupButtons()
        loadPhotos()
    }

    private fun setupButtons() {
        binding.selectAllButton.setOnClickListener {
            adapter?.let { adapter ->
                if (selectedPhotos.size == adapter.photoList.size) {
                    // 取消全选
                    selectedPhotos.clear()
                } else {
                    // 全选
                    selectedPhotos.clear()
                    selectedPhotos.addAll(adapter.photoList)
                }
                updateSelectionUI()
                adapter.notifyDataSetChanged()
            }
        }
        
        binding.uploadButton.setOnClickListener {
            if (selectedPhotos.isEmpty()) {
                Toast.makeText(context, "请选择要上传的照片", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            // 显示加载对话框
            val loadingDialog = LoadingDialog(requireContext())
            loadingDialog.setTitle("获取项目信息")
            loadingDialog.setButtonVisibility(View.GONE)
            loadingDialog.setCancelable(false)
            loadingDialog.show()
            
            lifecycleScope.launch(Dispatchers.IO) {
                try {
                    // 获取项目列表
                    val context = ApplicationContextProvider.context
                    val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                    if (sp.getString(SpKey.UserCookies, "").isNullOrEmpty()) {
                        withContext(Dispatchers.Main) {
                            loadingDialog.dismiss()
                            binding.root.findNavController().navigate("loginFragment")
                        }
                        return@launch
                    }

                    // 调用 ViewModel 的方法获取项目列表
                    val response = RetrofitClient.service.projectListAlbum().awaitResponse()

                    withContext(Dispatchers.Main) {
                        loadingDialog.dismiss()

                        if (response.isSuccessful) {
                            val projects = response.body()?.data.orEmpty()
                            if (projects.isNotEmpty()) {
                                ProjectUploadSelectAlbumDialog(requireContext(), projects) { albumId ->
                                    uploadMultiplePhotos(albumId)
                                }.show()
                            } else {
                                Toast.makeText(context, "未获取到项目信息", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            if (response.code() == 401) {
                                val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                                sp.edit { putString(SpKey.UserCookies, "") }
                                binding.root.findNavController().navigate("loginFragment")
                                Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                            } else {
                                Toast.makeText(context, "获取项目信息失败", Toast.LENGTH_SHORT).show()
                            }
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    withContext(Dispatchers.Main) {
                        loadingDialog.dismiss()
                        Toast.makeText(context, "网络请求失败", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }

        binding.deleteButton.setOnClickListener {
            if (selectedPhotos.isEmpty()) {
                Toast.makeText(context, "请选择要删除的照片", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            // 显示确认删除对话框
            showDeleteConfirmDialog()
        }

        binding.toggleSelectionButton.setOnClickListener {
            isSelectionMode = !isSelectionMode
            selectedPhotos.clear()
            updateSelectionUI()
            adapter?.notifyDataSetChanged()
        }
    }

    private fun updateSelectionUI() {
        binding.selectAllButton.isVisible = isSelectionMode
        binding.uploadButton.isVisible = isSelectionMode
        binding.deleteButton.isVisible = isSelectionMode

        if (isSelectionMode) {
            binding.toggleSelectionButton.setText("取消")
            val selectedCount = selectedPhotos.size
            val totalCount = adapter?.photoList?.size ?: 0
            binding.selectionCountText.text = "已选择 $selectedCount/$totalCount"
            binding.selectionCountText.visibility = View.VISIBLE
        } else {
            binding.toggleSelectionButton.setText("多选")
            binding.selectionCountText.visibility = View.GONE
        }
    }

    private fun uploadMultiplePhotos(albumId: String) {
        if (selectedPhotos.isEmpty()) return

        val loadingDialog = LoadingDialog(requireContext())
        loadingDialog.setTitle("批量上传中")
        loadingDialog.setMessage("0/${selectedPhotos.size}")
        loadingDialog.setButtonVisibility(View.GONE)
        loadingDialog.setCancelable(false)
        loadingDialog.show()

        val photoList = selectedPhotos.toList()
        var successCount = 0
        var failCount = 0

        lifecycleScope.launch(Dispatchers.IO) {
            for ((index, photo) in photoList.withIndex()) {
                withContext(Dispatchers.Main) {
                    loadingDialog.setMessage("${index + 1}/${photoList.size}")
                }

                try {
                    // 上传文件
                    val url = PublicRequest.uploadFile(File(photo.cachePath))
                    if (!url.isNullOrEmpty()) {
                        // 保存到云相册
                        val request = RetrofitClient.service.saveImageToCloudAlbum(
                            UploadImageToGallery(photoAddress = url, albumId = albumId)
                        )
                        val response = request.awaitResponse()

                        if (response.isSuccessful) {
                            val body = response.body()
                            if (body?.data != null && body.ok) {
                                // 更新照片状态
                                val photoDao = AppDatabase.get().photoDao()
                                val photoEntity = photoDao.getPhotoByUri(photo.uri)
                                if (photoEntity != null) {
                                    photoEntity.upload = true
                                    photoDao.update(photoEntity)
                                }
                                successCount++
                            } else {
                                failCount++
                            }
                        } else {
                            if (response.code() == 401) {
                                val sp = ApplicationContextProvider.context
                                    .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                                sp.edit { putString(SpKey.UserCookies, "") }
                                
                                withContext(Dispatchers.Main) {
                                    loadingDialog.dismiss()
                                    binding.root.findNavController().navigate("loginFragment")
                                    Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                                }
                                return@launch
                            }
                            failCount++
                        }
                    } else {
                        failCount++
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    failCount++
                }
            }
            
            withContext(Dispatchers.Main) {
                loadingDialog.dismiss()
                Toast.makeText(
                    context, 
                    "上传完成: 成功${successCount}张, 失败${failCount}张", 
                    Toast.LENGTH_SHORT
                ).show()
                
                // 刷新照片列表
                loadPhotos()
                
                // 重置选择模式
                isSelectionMode = false
                selectedPhotos.clear()
                updateSelectionUI()
            }
        }
    }
    
    private fun loadPhotos() {
        lifecycleScope.launch(Dispatchers.IO) {
            val photoDao = AppDatabase.get().photoDao()
            val images = photoDao.getUnUploadPhotos()
            val context = ApplicationContextProvider.context
            val validImages = images.filter { isPhotoExists(context, it.uri.toUri()) }
            if (images.size != validImages.size) {
                val invalidImages = images - validImages.toSet()
                invalidImages.forEach { photoDao.delete(it) }
            }
            launch(Dispatchers.Main) {
                binding.photoRecyclerView.adapter = PhotoAdapter(validImages).also { adapter = it }
                binding.photoRecyclerView.layoutManager = GridLayoutManager(context, 3)
                
                // 更新空状态视图
                binding.emptyStateLayout?.isVisible = validImages.isEmpty()
            }
        }
    }

    private fun showDeleteConfirmDialog() {
        val selectedCount = selectedPhotos.size
        val dialog = TipsDialog(requireContext())
        dialog.setTitle("确定要删除选中的 $selectedCount 张照片吗？此操作不可撤销。")
        dialog.setCancelListener { }
        dialog.setOkListener {
            deleteSelectedPhotos()
        }
        dialog.show()
    }

    private fun deleteSelectedPhotos() {
        if (selectedPhotos.isEmpty()) return

        val loadingDialog = LoadingDialog(requireContext())
        loadingDialog.setTitle("移至回收站中")
        loadingDialog.setMessage("0/${selectedPhotos.size}")
        loadingDialog.setButtonVisibility(View.GONE)
        loadingDialog.setCancelable(false)
        loadingDialog.show()

        val photoList = selectedPhotos.toList()
        var successCount = 0
        var failCount = 0

        lifecycleScope.launch(Dispatchers.IO) {
            for ((index, photo) in photoList.withIndex()) {
                withContext(Dispatchers.Main) {
                    loadingDialog.setMessage("${index + 1}/${photoList.size}")
                }

                try {
                    val photoDao = AppDatabase.get().photoDao()
                    val recycledPhotoDao = AppDatabase.get().recycledPhotoDao()
                    
                    // 从原始照片表删除
                    val photoEntity = photoDao.getPhotoByUri(photo.uri)
                    if (photoEntity != null) {
                        // 创建回收站照片记录
                        val recycledPhoto = RecycledPhoto(
                            uri = photoEntity.uri,
                            cachePath = photoEntity.cachePath,
                            shotTime = photoEntity.shotTime,
                            longitude = photoEntity.longitude,
                            latitude = photoEntity.latitude,
                            deleteTime = System.currentTimeMillis()
                        )
                        
                        // 移动到回收站
                        recycledPhotoDao.insert(recycledPhoto)
                        photoDao.delete(photoEntity)
                        
                        // 注意：不删除本地文件，保留在回收站中
                    }

                    successCount++
                } catch (e: Exception) {
                    e.printStackTrace()
                    failCount++
                }
            }

            withContext(Dispatchers.Main) {
                loadingDialog.dismiss()

                val message = if (failCount == 0) {
                    "成功移至回收站 $successCount 张照片"
                } else {
                    "操作完成：成功 $successCount 张，失败 $failCount 张"
                }
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()

                // 重新加载照片列表
                loadPhotos()

                // 退出选择模式
                isSelectionMode = false
                selectedPhotos.clear()
                updateSelectionUI()
            }
        }
    }

    inner class PhotoAdapter(var photoList: List<Photo>) : RecyclerView.Adapter<PhotoAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_photo_selectable, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val photo = photoList[position]
            val context = holder.itemView.context
            val displayMetrics = context.resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val imageWidth = (screenWidth - 18.dp) / 3
            holder.photoImageView.layoutParams.width = imageWidth
            holder.photoImageView.layoutParams.height = imageWidth
            val requestOptions = RequestOptions()
                .format(DecodeFormat.PREFER_RGB_565)
                .override(imageWidth, imageWidth)
            Glide.with(holder.itemView.context)
                .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                .asDrawable()
                .sizeMultiplier(0.5f)
                .skipMemoryCache(true)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .load(photo.uri)
                .placeholder(R.drawable.placeholder)
                .apply(requestOptions)
                .into(holder.photoImageView)
                
            // 处理选择模式
            holder.checkBox.isVisible = isSelectionMode
            holder.checkBox.isChecked = selectedPhotos.contains(photo)
            
            holder.itemView.setOnClickListener {
                if (isSelectionMode) {
                    if (selectedPhotos.contains(photo)) {
                        selectedPhotos.remove(photo)
                    } else {
                        selectedPhotos.add(photo)
                    }
                    notifyItemChanged(position)
                    updateSelectionUI()
                } else {
                    binding.root.findNavController().navigate(R.id.action_galleryFragment_to_uploadPhotoFragment, Bundle().apply {
                        putSerializable(FragmentArguments.Photo, photo)
                    })
                }
            }
            
            holder.itemView.setOnLongClickListener {
                if (!isSelectionMode) {
                    isSelectionMode = true
                    selectedPhotos.add(photo)
                    notifyDataSetChanged()
                    updateSelectionUI()
                    return@setOnLongClickListener true
                }
                return@setOnLongClickListener false
            }
        }

        override fun getItemCount(): Int {
            return photoList.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val photoImageView: ImageView = itemView.findViewById(R.id.photoImageView)
            val checkBox: CheckBox = itemView.findViewById(R.id.photoCheckBox)
        }
    }
}
