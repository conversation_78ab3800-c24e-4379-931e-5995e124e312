package online.yllh.smartinspect.view.settings

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import online.yllh.smartinspect.databinding.FragmentCacheSettingsBinding
import online.yllh.smartinspect.utils.ImageCacheManager
import java.text.DecimalFormat

/**
 * 缓存设置页面
 * 提供缓存查看和清理功能
 */
class CacheSettingsFragment : Fragment() {

    private lateinit var binding: FragmentCacheSettingsBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCacheSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupClickListeners()
        updateCacheInfo()
    }

    private fun setupClickListeners() {
        // 清除内存缓存
        binding.clearMemoryCache.setOnClickListener {
            ImageCacheManager.clearMemoryCache(requireContext())
            Toast.makeText(requireContext(), "内存缓存已清除", Toast.LENGTH_SHORT).show()
            updateCacheInfo()
        }

        // 清除磁盘缓存
        binding.clearDiskCache.setOnClickListener {
            lifecycleScope.launch {
                withContext(Dispatchers.IO) {
                    ImageCacheManager.clearDiskCache(requireContext())
                }
                withContext(Dispatchers.Main) {
                    Toast.makeText(requireContext(), "磁盘缓存已清除", Toast.LENGTH_SHORT).show()
                    updateCacheInfo()
                }
            }
        }

        // 清除所有缓存
        binding.clearAllCache.setOnClickListener {
            lifecycleScope.launch {
                // 清除内存缓存
                ImageCacheManager.clearMemoryCache(requireContext())
                
                // 清除磁盘缓存
                withContext(Dispatchers.IO) {
                    ImageCacheManager.clearDiskCache(requireContext())
                }
                
                withContext(Dispatchers.Main) {
                    Toast.makeText(requireContext(), "所有缓存已清除", Toast.LENGTH_SHORT).show()
                    updateCacheInfo()
                }
            }
        }

        // 刷新缓存信息
        binding.refreshCacheInfo.setOnClickListener {
            updateCacheInfo()
        }
    }

    private fun updateCacheInfo() {
        lifecycleScope.launch {
            val cacheSize = withContext(Dispatchers.IO) {
                ImageCacheManager.getCacheSize(requireContext())
            }
            
            withContext(Dispatchers.Main) {
                binding.cacheSizeText.text = "缓存大小: ${formatFileSize(cacheSize)}"
            }
        }
    }

    private fun formatFileSize(size: Long): String {
        if (size <= 0) return "0 B"
        
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        val digitGroups = (Math.log10(size.toDouble()) / Math.log10(1024.0)).toInt()
        
        return DecimalFormat("#,##0.#").format(size / Math.pow(1024.0, digitGroups.toDouble())) + " " + units[digitGroups]
    }
}
