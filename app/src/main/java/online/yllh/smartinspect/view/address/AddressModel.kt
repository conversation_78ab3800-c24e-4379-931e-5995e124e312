package online.yllh.smartinspect.view.address

import android.content.Context
import androidx.lifecycle.ViewModel
import com.baidu.mapapi.search.sug.SuggestionSearch
import com.baidu.mapapi.search.sug.SuggestionSearchOption
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.model.PoiInfo
import online.yllh.smartinspect.provider.ApplicationContextProvider
import kotlin.coroutines.resume


enum class AddressType {
    NONE,
    COUNTRY,
    PROVINCE,
    CITY,
    DISTRICT,
    STREET,
    TOWN
}

class AddressModel : ViewModel() {
    val addressType = MutableStateFlow(AddressType.CITY)
    val addressStr = MutableStateFlow("定位中")
    val poiList = MutableStateFlow<List<PoiInfo>>(emptyList())
    val enableLevel = MutableStateFlow(
        ApplicationContextProvider.context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
            .getBoolean(SpKey.EnableLevel, true)
    )
    private val viewModelScope = CoroutineScope(Dispatchers.Main)
    private var lastSearchTask: Job? = null

    fun performSearch(city: String, keyword: String, address: String) {
        cancelLastSearch()
        lastSearchTask = viewModelScope.launch {
            try {
                searchPoi(city, keyword, address)
                // 处理结果
            } catch (e: Exception) {
                // 处理异常
                println("Error: ${e.message}")
            }
        }
    }

    fun cancelLastSearch() {
        lastSearchTask?.cancel()
    }

    suspend fun searchPoi(city: String, keyword: String, address: String) {
        val resultList = suspendCancellableCoroutine { cont ->
            val suggestionSearch = SuggestionSearch.newInstance()
            suggestionSearch.setOnGetSuggestionResultListener { result ->
                val resultList = result?.allSuggestions?.map { PoiInfo(it.key, it.address) }.orEmpty()
                suggestionSearch.destroy()
                cont.resume(resultList)
            }
            suggestionSearch.requestSuggestion(SuggestionSearchOption().city(city).keyword("$keyword $address".trim()).citylimit(true))
            cont.invokeOnCancellation { suggestionSearch.destroy() }
        }
        poiList.value = resultList
    }
}
