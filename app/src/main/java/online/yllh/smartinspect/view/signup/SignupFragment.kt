package online.yllh.smartinspect.view.signup

import android.content.Context
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.edit
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentSignupBinding
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.RegisterUser
import retrofit2.awaitResponse

class SignupFragment : BaseFragment() {
    private lateinit var binding: FragmentSignupBinding
    private lateinit var countDownTimer: CountDownTimer

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentSignupBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        binding.okButton.setOnClickListener {
            val phone = binding.phoneNumEditText.text
            val verifyCode = binding.verifyCodeEditText.text
            val pass = binding.passEditText.text
            val prepeatpass = binding.prepeatpassEditText.text
            if (phone != null && verifyCode != null && pass != null && prepeatpass != null) {
                if (pass.toString() == prepeatpass.toString()) {
                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            val request = RetrofitClient.service.registerUser(
                                RegisterUser(
                                    binding.phoneNumEditText.text.toString(),
                                    binding.passEditText.text.toString(),
                                    binding.prepeatpassEditText.text.toString(),
                                    binding.verifyCodeEditText.text.toString(),
                                )
                            )
                            val result = request.awaitResponse()
                            if (result.isSuccessful) {
                                val body = result.body()
                                launch(Dispatchers.Main) {
                                    if (body?.data != null) {
                                        val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                                        sp.edit { putString(SpKey.UserCookies, body.data.token) }
                                        Toast.makeText(requireContext(), "注册成功", Toast.LENGTH_SHORT).show()
                                        binding.root.findNavController().navigate(R.id.action_signupFragment_to_myFragment)
                                    } else {
                                        Toast.makeText(requireContext(), "${body?.msg}", Toast.LENGTH_SHORT).show()
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            launch(Dispatchers.Main) {
                                Toast.makeText(requireContext(), "网络连接失败", Toast.LENGTH_SHORT).show()
                            }
                        }
                    }
                } else {
                    Toast.makeText(requireContext(), "两次密码不一致", Toast.LENGTH_SHORT).show()
                }
            }
        }
        binding.cancelButton.setOnClickListener {
            binding.root.findNavController().popBackStack()
        }
        binding.appBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }
        binding.sendSmsCode.setOnClickListener {
            countDownTimer = object : CountDownTimer(60000, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    // 在计时器每隔一秒触发时更新文本
                    val secondsLeft = millisUntilFinished / 1000
                    binding.sendSmsCode.setText("$secondsLeft s")
                    binding.sendSmsCode.isEnabled = false
                }

                override fun onFinish() {
                    // 倒计时完成时执行的操作
                    binding.sendSmsCode.setText("发送")
                    binding.sendSmsCode.isEnabled = true
                    // 这里可以添加倒计时结束后的逻辑
                }
            }
            if (binding.phoneNumEditText.text.toString().length != 11) {
                Toast.makeText(requireContext(), "手机号不正确", Toast.LENGTH_SHORT).show()
            } else {
                lifecycleScope.launch(Dispatchers.IO) {
                    try {
                        val result = RetrofitClient.service.sendSmsCode(binding.phoneNumEditText.text.toString())
                        val response = result.awaitResponse()
                        if (response.isSuccessful) {
                            val body = response.body()
                            if (body != null && body.ok) {
                                launch(Dispatchers.Main) {
                                    countDownTimer.start()
                                    Toast.makeText(requireContext(), "发送成功", Toast.LENGTH_SHORT).show()
                                }
                            } else if (body != null) {
                                launch(Dispatchers.Main) {
                                    Toast.makeText(requireContext(), body.msg, Toast.LENGTH_SHORT).show()
                                }
                            }
                        }
                    } catch (e: Exception) {
                        launch(Dispatchers.Main) {
                            Toast.makeText(requireContext(), "网络连接失败", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            }
        }
    }
}
