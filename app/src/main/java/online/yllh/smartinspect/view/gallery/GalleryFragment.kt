package online.yllh.smartinspect.view.gallery

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.findNavController
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentGalleryBinding
import online.yllh.smartinspect.view.gallery.cloud.CloudGalleryFragment
import online.yllh.smartinspect.view.gallery.cloud.CloudGalleryLoginFragment
import online.yllh.smartinspect.view.gallery.local.LocalGalleryFragment
import online.yllh.smartinspect.view.gallery.recyclebin.RecycleBinFragment
import online.yllh.smartinspect.view.gallery.unupload.UnUploadGalleryFragment

class GalleryFragment : BaseFragment() {
    private lateinit var binding: FragmentGalleryBinding
    private val viewModel by viewModels<GalleryViewModel>()
    private val isImportMode: Boolean by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getBoolean(FragmentArguments.IsImportMode) == true
    }
    private val importModeAlbumId: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.ImportModeAlbumId).orEmpty()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentGalleryBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        binding.localAlbum.setOnClickListener {
            viewModel.setAlbumType(AlbumType.LOCAL)
        }
        binding.cloudAlbum.setOnClickListener {
            viewModel.setAlbumType(AlbumType.REMOTE)
        }
        binding.unUploadAlbum.setOnClickListener {
            viewModel.setAlbumType(AlbumType.UN_UPLOAD)
        }
        binding.recycleBin.setOnClickListener {
            viewModel.setAlbumType(AlbumType.RECYCLE_BIN)
        }
        observeOn(viewModel.albumType) {
            binding.localAlbum.isSelected = it == AlbumType.LOCAL
            binding.cloudAlbum.isSelected = it == AlbumType.REMOTE
            binding.unUploadAlbum.isSelected = it == AlbumType.UN_UPLOAD
            binding.recycleBin.isSelected = it == AlbumType.RECYCLE_BIN
            if (it == AlbumType.LOCAL) {
                showFragment(
                    LocalGalleryFragment(
                        initPhotoScrollY = viewModel.photoScrollY.value,
                        initAlbumScrollY = viewModel.albumScrollY.value,
                        photoScrollListener = { scrollY ->
                            viewModel.photoScrollY.value = scrollY
                        },
                        albumScrollListener = { scrollY ->
                            viewModel.albumScrollY.value = scrollY
                        },
                        isImportModel = isImportMode,
                        importModeAlbumId = importModeAlbumId
                    ),
                )
            } else if (it == AlbumType.REMOTE) {
                val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                if ((sp.getString(SpKey.UserCookies, "").orEmpty()).isEmpty()) {
                    showFragment(CloudGalleryLoginFragment())
                } else {
                    showFragment(CloudGalleryFragment(isImportMode, importModeAlbumId))
                }
            } else if (it == AlbumType.UN_UPLOAD) {
                showFragment(UnUploadGalleryFragment())
            } else if (it == AlbumType.RECYCLE_BIN) {
                showFragment(RecycleBinFragment())
            }
        }
        binding.appTitleBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }
    }

    private fun showFragment(fragment: Fragment) {
        val transaction = childFragmentManager.beginTransaction()
        transaction.replace(binding.fragmentContainer.id, fragment)
        transaction.commit()
    }
}
