package online.yllh.smartinspect.view.gallery.cloud

import android.content.Context
import android.view.View
import android.widget.Toast
import androidx.core.content.edit
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.findNavController
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.CreateProjectAlbumModel
import online.yllh.smartinspect.network.model.response.CloudAlbumModel
import online.yllh.smartinspect.provider.ApplicationContextProvider
import retrofit2.awaitResponse

class CloudAlbumViewModel : ViewModel() {
    val albums = MutableStateFlow<List<CloudAlbumModel>>(emptyList())

    fun getAlbumInfosByAlbumIds(context: Context, albumId: String, root: View) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在同步云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.getAlbumInfosByAlbumIds(albumId)
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            albums.value = body.data
                        } else {
                            Toast.makeText(context, "同步失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                launch(Dispatchers.Main) { dialog.dismiss() }
            }
        }
    }


    fun deleteGallery(context: Context, albumId: String, parentAlbumId: String, root: View) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在删除云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.deleteAlbum(albumId)
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            getAlbumInfosByAlbumIds(context, parentAlbumId, root)
                        } else {
                            Toast.makeText(context, "删除失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                launch(Dispatchers.Main) { dialog.dismiss() }
            }
        }
    }

    fun addProjectGallery(context: Context, projectId: String, parentAlbumId: String, albumName: String, root: View) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在同步云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.savePAlbum(CreateProjectAlbumModel(projectId, parentAlbumId, albumName))
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            Toast.makeText(context, "同步成功", Toast.LENGTH_SHORT).show()
                            getAlbumInfosByAlbumIds(context, parentAlbumId, root)
                        } else {
                            Toast.makeText(context, "同步失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }
}
