package online.yllh.smartinspect.view.gallery.unupload

import android.app.AlertDialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import online.yllh.smartinspect.uiwidget.ZoomableImageView
import android.widget.Toast
import androidx.core.content.edit
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import online.yllh.smartinspect.AppDatabase
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentUploadPhotoBinding
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.dialog.ProjectUploadSelectAlbumDialog
import online.yllh.smartinspect.entity.Photo
import online.yllh.smartinspect.entity.RecycledPhoto
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.extension.serializable
import online.yllh.smartinspect.network.PublicRequest
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.UploadImageToGallery
import online.yllh.smartinspect.provider.ApplicationContextProvider
import retrofit2.awaitResponse
import java.io.File

class UploadPhotoFragment : BaseFragment() {
    private lateinit var binding: FragmentUploadPhotoBinding
    private val viewModel by viewModels<UploadPhotoViewModel>()

    private val photo: Photo by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.serializable(FragmentArguments.Photo) ?: error("need photo argument")
    }

    private var photoList: List<Photo> = emptyList()
    private lateinit var photoAdapter: PhotoPagerAdapter
    private var currentPhotoIndex: Int = 0

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentUploadPhotoBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_HIDE_BAR)
            .init()

        setupPhotoList()
        setupButtons()

        val context = ApplicationContextProvider.context
        val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        if (!sp.getString(SpKey.UserCookies, "").isNullOrEmpty()) {
            viewModel.fetchProjects(binding.root)
        }
    }

    private fun setupPhotoList() {
        lifecycleScope.launch(Dispatchers.IO) {
            val photoDao = AppDatabase.get().photoDao()
            photoList = photoDao.getUnUploadPhotos()

            // 找到当前照片在列表中的位置
            currentPhotoIndex = photoList.indexOfFirst { it.uri == photo.uri }
            if (currentPhotoIndex == -1) {
                currentPhotoIndex = 0
            }

            launch(Dispatchers.Main) {
                setupViewPager()
            }
        }
    }

    private fun setupViewPager() {
        photoAdapter = PhotoPagerAdapter(photoList)
        binding.photoViewPager.adapter = photoAdapter
        binding.photoViewPager.setCurrentItem(currentPhotoIndex, false)

        updatePhotoIndicator()

        binding.photoViewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                currentPhotoIndex = position
                updatePhotoIndicator()
            }
        })
    }

    private fun updatePhotoIndicator() {
        binding.photoIndicator.text = "${currentPhotoIndex + 1}/${photoList.size}"
    }

    private fun setupButtons() {
        binding.cancelButton.setOnClickListener {
            binding.root.findNavController().popBackStack()
        }

        binding.deleteButton.setOnClickListener {
            deleteLocalPhoto()
        }

        binding.okButton.setOnClickListener {
            val context = ApplicationContextProvider.context
            val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
            if (sp.getString(SpKey.UserCookies, "").isNullOrEmpty()) {
                binding.root.findNavController().navigate("loginFragment")
            } else {
                val projects = viewModel.projects.value
                if (projects.isNotEmpty()) {
                    ProjectUploadSelectAlbumDialog(requireContext(), projects, this@UploadPhotoFragment::upload).show()
                }
            }
        }
    }

    private fun deleteLocalPhoto() {
        if (photoList.isEmpty()) return

        val currentPhoto = photoList[currentPhotoIndex]
        val dialog = LoadingDialog(requireContext())
        dialog.setTitle("删除中")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val photoDao = AppDatabase.get().photoDao()
                val recycledPhotoDao = AppDatabase.get().recycledPhotoDao()
                
                val photoEntity = photoDao.getPhotoByUri(currentPhoto.uri)
                if (photoEntity != null) {
                    // 创建回收站照片记录
                    val recycledPhoto = RecycledPhoto(
                        uri = photoEntity.uri,
                        cachePath = photoEntity.cachePath,
                        shotTime = photoEntity.shotTime,
                        longitude = photoEntity.longitude,
                        latitude = photoEntity.latitude,
                        deleteTime = System.currentTimeMillis()
                    )
                    
                    // 移动到回收站
                    recycledPhotoDao.insert(recycledPhoto)
                    photoDao.delete(photoEntity)
                    
                    // 注意：不删除本地文件，保留在回收站中
                }

                withContext(Dispatchers.Main) {
                    dialog.dismiss()
                    Toast.makeText(context, "照片已移至回收站", Toast.LENGTH_SHORT).show()

                    // 从列表中移除已删除的照片
                    val mutableList = photoList.toMutableList()
                    mutableList.removeAt(currentPhotoIndex)
                    photoList = mutableList

                    if (photoList.isEmpty()) {
                        // 如果没有照片了，返回上一页
                        binding.root.findNavController().popBackStack()
                    } else {
                        // 更新适配器和当前位置
                        photoAdapter.updatePhotoList(photoList)

                        // 调整当前位置
                        if (currentPhotoIndex >= photoList.size) {
                            currentPhotoIndex = photoList.size - 1
                        }

                        binding.photoViewPager.setCurrentItem(currentPhotoIndex, false)
                        updatePhotoIndicator()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    dialog.dismiss()
                    Toast.makeText(context, "移至回收站失败", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun upload(albumId: String) {
        showUploadDialog(albumId)
    }

    fun showUploadDialog(albumId: String) {
        if (photoList.isEmpty()) return

        val currentPhoto = photoList[currentPhotoIndex]
        val dialog = LoadingDialog(requireContext())
        dialog.setTitle("上传中")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            val url = PublicRequest.uploadFile(File(currentPhoto.cachePath))
            if (url.isNullOrEmpty()) {
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "上传失败", Toast.LENGTH_SHORT).show()
                    dialog.dismiss()
                }
            } else {
                withContext(Dispatchers.Main) {
                    dialog.dismiss()
                    uploadImage(url, albumId, currentPhoto)
                }
            }
        }
    }

    fun uploadImage(imageUrl: String, albumId: String, currentPhoto: Photo) {
        val dialog = LoadingDialog(requireContext())
        dialog.setTitle("正在同步云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.saveImageToCloudAlbum(
                    UploadImageToGallery(photoAddress = imageUrl, albumId = albumId)
                )
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            withContext(Dispatchers.IO) {
                                val photoDao = AppDatabase.get().photoDao()
                                val photoEntity = photoDao.getPhotoByUri(currentPhoto.uri)
                                if (photoEntity != null) {
                                    photoEntity.upload = true
                                    photoDao.update(photoEntity)
                                }
                            }
                            Toast.makeText(context, "同步成功", Toast.LENGTH_SHORT).show()
                            delay(500)

                            // 从列表中移除已上传的照片
                            val mutableList = photoList.toMutableList()
                            mutableList.removeAt(currentPhotoIndex)
                            photoList = mutableList

                            if (photoList.isEmpty()) {
                                // 如果没有照片了，返回上一页
                                binding.root.findNavController().popBackStack()
                            } else {
                                // 更新适配器和当前位置
                                photoAdapter.updatePhotoList(photoList)

                                // 调整当前位置
                                if (currentPhotoIndex >= photoList.size) {
                                    currentPhotoIndex = photoList.size - 1
                                }

                                binding.photoViewPager.setCurrentItem(currentPhotoIndex, false)
                                updatePhotoIndicator()
                            }
                        } else {
                            Toast.makeText(context, body?.msg ?: "同步失败 请重新上传", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            binding.root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                launch(Dispatchers.Main) {
                    Toast.makeText(context, "网络连接失败 请重新上传", Toast.LENGTH_SHORT).show()
                }
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }

    inner class PhotoPagerAdapter(private var photos: List<Photo>) : RecyclerView.Adapter<PhotoPagerAdapter.PhotoViewHolder>() {

        fun updatePhotoList(newPhotos: List<Photo>) {
            photos = newPhotos
            notifyDataSetChanged()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PhotoViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_upload_photo, parent, false)
            return PhotoViewHolder(view)
        }

        override fun onBindViewHolder(holder: PhotoViewHolder, position: Int) {
            // 存储ViewHolder引用
            viewHolders[position] = holder

            val photo = photos[position]

            // 使用ZoomableImageView的setImageSource方法
            holder.photoImageView.setImageSource(photo.uri)
        }

        override fun getItemCount(): Int = photos.size

        private val viewHolders = mutableMapOf<Int, PhotoViewHolder>()
        inner class PhotoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val photoImageView: ZoomableImageView = itemView.findViewById(R.id.photoImageView)
        }
    }
}
