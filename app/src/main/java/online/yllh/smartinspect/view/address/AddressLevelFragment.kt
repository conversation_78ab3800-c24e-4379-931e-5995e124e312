package online.yllh.smartinspect.view.address

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.edit
import androidx.navigation.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentAddressLevelBinding
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.extension.parcelable
import online.yllh.smartinspect.model.AddressDataModel


class AddressLevelFragment : BaseFragment() {

    private lateinit var binding: FragmentAddressLevelBinding
    private var addressLevelIsEnable = true
    private var level = ""
    private val addressData: AddressDataModel by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.parcelable(FragmentArguments.Address) ?: throw IllegalArgumentException("imageInfo is null")
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ) = FragmentAddressLevelBinding.inflate(inflater, container, false).also { binding = it }.root

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        val adapter = AddressAdapter(
            arrayListOf(
                addressData.country,
                addressData.province,
                addressData.city,
                addressData.district,
                addressData.town,
                addressData.street,
            )
        )
        binding.addressList.layoutManager = LinearLayoutManager(requireContext())
        binding.addressList.adapter = adapter
        binding.levelSwitch.isChecked = sp.getBoolean(SpKey.EnableLevel, true)
        if (!binding.levelSwitch.isChecked) {
            binding.area.text = "已关闭地址级别"
            binding.addressList.visibility = View.INVISIBLE
        } else {
            setTitleText(sp)
            binding.addressList.visibility = View.VISIBLE
        }
        binding.levelSwitch.setOnCheckedChangeListener { _, isChecked ->
            addressLevelIsEnable = isChecked
            if (!isChecked) {
                binding.area.text = "已关闭地址级别"
                binding.addressList.visibility = View.INVISIBLE
                sp.edit { putBoolean(SpKey.EnableLevel, false) }
            } else {
                setTitleText(sp)
                binding.addressList.visibility = View.VISIBLE
                sp.edit { putBoolean(SpKey.EnableLevel, true) }
            }
        }
        binding.appBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }
    }

    fun setTitleText(sp: SharedPreferences) {
        binding.area.text = when (sp.getString(SpKey.LocationLevel, "city")) {
            "country" -> addressData.country
            "province" -> addressData.province
            "city" -> addressData.city
            "district" -> addressData.district
            "town" -> addressData.town
            "street" -> addressData.street
            else -> "已关闭地址级别"
        }
    }

    inner class AddressAdapter(private var addressData: List<String>) : RecyclerView.Adapter<AddressAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_poi, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.poiName.text = addressData[position]
            holder.poiAddress.visibility = View.GONE
            holder.itemView.setOnClickListener {
                level = when (position) {
                    0 -> "country"
                    1 -> "province"
                    2 -> "city"
                    3 -> "district"
                    4 -> "town"
                    5 -> "street"
                    else -> ""
                }
                val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                sp.edit { putString(SpKey.LocationLevel, level) }
                binding.root.findNavController().popBackStack()
            }
        }

        override fun getItemCount(): Int {
            return addressData.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val poiName: TextView = itemView.findViewById(R.id.poiName)
            val poiAddress: TextView = itemView.findViewById(R.id.poiAddress)
        }
    }
}
