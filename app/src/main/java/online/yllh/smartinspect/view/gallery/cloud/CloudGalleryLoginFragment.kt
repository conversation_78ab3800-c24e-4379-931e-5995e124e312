package online.yllh.smartinspect.view.gallery.cloud

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.findNavController
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.FragmentCloudGalleryLoginBinding

class CloudGalleryLoginFragment : BaseFragment() {
    private lateinit var binding: FragmentCloudGalleryLoginBinding
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCloudGalleryLoginBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.login.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_galleryFragment_to_loginFragment)
        }
    }
}
