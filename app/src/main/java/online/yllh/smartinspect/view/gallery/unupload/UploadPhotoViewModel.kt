package online.yllh.smartinspect.view.gallery.unupload

import android.content.Context
import android.view.View
import android.widget.Toast
import androidx.core.content.edit
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.findNavController
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.response.Project
import online.yllh.smartinspect.provider.ApplicationContextProvider
import retrofit2.awaitResponse

class UploadPhotoViewModel : ViewModel() {
    val projects = MutableStateFlow(listOf<Project>())

    fun fetchProjects(root: View) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val response = RetrofitClient.service.projectListAlbum().awaitResponse()
                withContext(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        projects.value = response.body()?.data.orEmpty()
                    } else {
                        val context = ApplicationContextProvider.context
                        if (response.code() == 401) {
                            val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            launch(Dispatchers.Main) {
                                root.findNavController().navigate("loginFragment")
                                Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                val context = ApplicationContextProvider.context
            }
        }
    }
}
