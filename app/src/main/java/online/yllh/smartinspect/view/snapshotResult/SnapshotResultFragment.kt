package online.yllh.smartinspect.view.snapshotResult

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Matrix
import android.graphics.SurfaceTexture
import android.os.Bundle
import android.view.LayoutInflater
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.core.content.edit
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.google.gson.Gson
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.tapapk.camera_engine.utils.Exif
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentSnapshotResultBinding
import online.yllh.smartinspect.extension.hideKeyboard
import online.yllh.smartinspect.extension.screenWidth
import online.yllh.smartinspect.extension.toJpegByteArray
import online.yllh.smartinspect.media.saveImageToGallery
import online.yllh.smartinspect.model.AddressDataModel
import online.yllh.smartinspect.provider.ApplicationContextProvider
import online.yllh.smartinspect.sticker.EmptySticker
import online.yllh.smartinspect.sticker.PhotoExtraInfoSticker
import online.yllh.smartinspect.sticker.PhotoExtraInfoNewSticker
import online.yllh.smartinspect.sticker.StickerManager
import online.yllh.smartinspect.uiwidget.sticker.StickerGalleryFragment
import online.yllh.smartinspect.uiwidget.sticker.StickerPanel
import online.yllh.smartinspect.view.camera.CameraViewModel
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream


class SnapshotResultFragment : BaseFragment() {
    private var previewSurface: SurfaceTexture? = null
    private var previewWidth: Int = 0
    private var previewHeight: Int = 0
    private lateinit var binding: FragmentSnapshotResultBinding
    private val viewModel by viewModels<SnapshotViewModel>()
    private val cameraViewModel by activityViewModels<CameraViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentSnapshotResultBinding.inflate(inflater, container, false)
        return binding.root
    }

    private val textureViewListener = object : TextureView.SurfaceTextureListener {
        override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
            previewWidth = width
            previewHeight = height
            previewSurface = surface
            viewModel.setSurfaceTextureAvailable(true)
        }

        override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {
            viewModel.surfaceSizeChange(width, height)
        }

        override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
            viewModel.release()
            viewModel.setSurfaceTextureAvailable(false)
            return true
        }

        override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
        }
    }

    private val backPressedCallback = object : OnBackPressedCallback(false) {
        override fun handleOnBackPressed() {
            if (binding.stickerPanel.showing)
                binding.stickerPanel.hide()
            if (binding.addressLevelPanel.showing) {
                binding.addressLevelPanel.hide()
            } else if (binding.addressSelectPanel.showing) {
                binding.addressSelectPanel.hide()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_HIDE_BAR)
            .init()
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, backPressedCallback)
        binding.backContainer.setOnClickListener {
            binding.root.findNavController().popBackStack()
        }
        initStickerPanel()
        binding.previewView.surfaceTextureListener = textureViewListener
        viewModel.currentSticker.value = cameraViewModel.currentSticker.value
        viewModel.poiName = cameraViewModel.poiName
        viewModel.watermarkState.value = cameraViewModel.snapshot.watermarkState
        viewModel.isShowWatermark.value = cameraViewModel.isShowWatermark.value

        binding.okButton.setOnClickListener {
            binding.locationContainer.visibility = View.GONE
            binding.backContainer.visibility = View.GONE
            binding.okButton.visibility = View.GONE
            binding.watermarkCon.visibility = View.GONE
            viewModel.editEngineCore.outBitmap(cameraViewModel.snapshot.imageWidth, cameraViewModel.snapshot.imageHeight) {
                viewLifecycleScope.launch(Dispatchers.IO) {
                    val width: Int = it.width
                    val height: Int = it.height
                    val matrix = Matrix()
                    matrix.postScale(-1f, 1f)
                    matrix.postRotate(180f)
                    // 创建一个旋转后的Bitmap对象
                    val rotatedBitmap = Bitmap.createBitmap(it, 0, 0, width, height, matrix, true)
                    val cacheDir = File(ApplicationContextProvider.context.cacheDir.path + "/cameraTmp")
                    if (!cacheDir.exists()) {
                        cacheDir.mkdir()
                    }
                    val imageFile = File(cacheDir.path + "/" + System.currentTimeMillis() + ".jpg")
                    if (!imageFile.exists()) {
                        cacheDir.createNewFile()
                    }
                    val exifData = Exif.getExifData(cameraViewModel.snapshot.imagePath)
                    val fileByte = Exif.exifToJpegData(rotatedBitmap.toJpegByteArray(100), exifData)
                    val outputStream = FileOutputStream(imageFile)
                    outputStream.write(fileByte)
                    outputStream.close()
                    val uri = saveImageToGallery(requireContext(), imageFile)
                    launch(Dispatchers.Main) {
                        binding.root.findNavController().navigate(R.id.action_snapshotResultFragment_to_editResultFragment, Bundle().apply {
                            this.putSerializable(FragmentArguments.ImageUri, uri.toString())
                            this.putSerializable(FragmentArguments.ImageCachePath, imageFile.path)
                        })
                    }
                }
            }
        }

        val layoutParams = binding.previewView.layoutParams
        val previewWidth = requireContext().screenWidth
        val previewHeight = previewWidth * 4 / 3

        layoutParams.width = previewWidth
        layoutParams.height = previewHeight
        binding.previewView.layoutParams = layoutParams
        observeOn(viewModel.previewTextureIsReady) {
            if (it) {
                lifecycleScope.launch(Dispatchers.IO) {
                    var inputStream: FileInputStream? = null
                    try {
                        inputStream = FileInputStream(File(cameraViewModel.snapshot.imagePath))
                        val bytes = inputStream.readBytes()
                        viewModel.initEditEnvironment(requireContext(), previewSurface!!, previewWidth, previewHeight, bytes)
                        setSticker()
                    } finally {
                        inputStream?.close()
                    }
                }
            }
        }

        binding.watermark.setOnClickListener {
            binding.stickerPanel.show()
            backPressedCallback.isEnabled = true
        }
        observeOn(viewModel.currentSticker) {
            setSticker()
            viewModel.recentStickerAdapter?.stickerList = StickerManager.getRecentlyStickerList(requireContext())
            viewModel.setCurrentSticker(it)
        }

        binding.stickerPanel.hideListener = {
            backPressedCallback.isEnabled = false
        }

        observeOn(viewModel.watermarkState) {
            setSticker()
        }

        binding.location.setOnClickListener {
            // 点击地点，默认添加一个水印
            val stickerList = viewModel.recentStickerAdapter?.stickerList
            if (!viewModel.isShowWatermark.value && !stickerList.isNullOrEmpty()) {
                val sticker = stickerList.first()
                viewModel.selectListener.invoke(sticker.stickerId)
            }
            binding.addressSelectPanel.show(
                AddressDataModel(
                    country = viewModel.location.value.country,
                    province = viewModel.location.value.province,
                    city = viewModel.location.value.city,
                    district = viewModel.location.value.district,
                    street = viewModel.location.value.street,
                    town = viewModel.location.value.town,
                    poiList = viewModel.location.value.poiList,
                )
            )
            backPressedCallback.isEnabled = true
        }

        binding.addressSelectPanel.lifecycleOwner = viewLifecycleOwner
        binding.addressSelectPanel.hideListener = {
            backPressedCallback.isEnabled = false
        }
        binding.addressSelectPanel.showLevelListener = {
            view.hideKeyboard()
            binding.addressLevelPanel.show(
                AddressDataModel(
                    country = viewModel.location.value.country,
                    province = viewModel.location.value.province,
                    city = viewModel.location.value.city,
                    district = viewModel.location.value.district,
                    street = viewModel.location.value.street,
                    town = viewModel.location.value.town,
                )
            )
        }
        binding.addressSelectPanel.selectAddressListener = {
            requireActivity().supportFragmentManager.setFragmentResult(
                "AddressFragment", Bundle().apply { putString("address", it) }
            )
        }
        binding.addressLevelPanel.onLocationLevelChanged = {
            binding.addressSelectPanel.updateAddressType()
            requireActivity().supportFragmentManager.setFragmentResult(
                "AddressFragment", Bundle().apply { putString("addressLevel", it) }
            )
        }
        binding.addressLevelPanel.onEnableLevelChanged = {
            binding.addressSelectPanel.onEnableLevelChanged(it)
            requireActivity().supportFragmentManager.setFragmentResult(
                "AddressFragment", Bundle().apply { putInt("enableLevel", if (it) 1 else 0) }
            )
        }

        requireActivity().supportFragmentManager.setFragmentResultListener(
            "AddressFragment", viewLifecycleOwner
        ) { _, bundle ->
            val result = bundle.getString("address", "")
            if (result.isNotEmpty()) {
                viewModel.updateWatermarkAddress(result)
            }
            val enableLevel = bundle.getInt("enableLevel", -1)
            val addressLevel = bundle.getString("addressLevel", "")
            if (enableLevel != -1 || addressLevel.isNotEmpty()) {
                viewModel.refreshLocationLevel()
            }
        }
    }

    private fun setSticker() {
        if (previewWidth == 0 || previewHeight == 0) {
            return
        }
        val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        val recentlySticker = sp.getString(SpKey.RecentlySticker, "").orEmpty()
        val gson = Gson()
        if (recentlySticker.isNotEmpty()) {
            var list = gson.fromJson<List<String>>(recentlySticker, List::class.java)
            if (!list.contains(viewModel.currentSticker.value)) {
                list = list.plus(viewModel.currentSticker.value)
            }
            sp.edit { putString(SpKey.RecentlySticker, gson.toJson(list)) }
        } else {
            val list = listOf(viewModel.currentSticker.value)
            sp.edit { putString(SpKey.RecentlySticker, gson.toJson(list)) }
        }

        if (viewModel.currentSticker.value == "dateAndLocationSticker") {
            viewModel.isShowWatermark.value = true
            viewModel.setSticker(
                PhotoExtraInfoSticker(
                    true,
                    cameraViewModel.isShowProjectView.value,
                    cameraViewModel.projectName.value,
                    cameraViewModel.projectRemark.value,
                    viewModel.watermarkState.value,
                ).createBitmap(previewWidth, previewHeight)
            )
        } else if (viewModel.currentSticker.value == "dateAndLocationNewSticker") {
            viewModel.isShowWatermark.value = true
            viewModel.setSticker(
                PhotoExtraInfoNewSticker(
                    true,
                    cameraViewModel.isShowProjectView.value,
                    cameraViewModel.projectName.value,
                    cameraViewModel.projectRemark.value,
                    viewModel.watermarkState.value,
                ).createBitmap(previewWidth, previewHeight)
            )
        } else if (cameraViewModel.isShowProjectView.value) {
            viewModel.isShowWatermark.value = false
            viewModel.setSticker(
                PhotoExtraInfoSticker(
                    false,
                    cameraViewModel.isShowProjectView.value,
                    cameraViewModel.projectName.value,
                    cameraViewModel.projectRemark.value,
                    viewModel.watermarkState.value,
                ).createBitmap(previewWidth, previewHeight)
            )
        } else {
            viewModel.isShowWatermark.value = false
            viewModel.setSticker(EmptySticker().createBitmap(previewWidth, previewHeight))
        }
    }

    private fun initStickerPanel() {
        if (viewModel.workStickerAdapter == null) {
            viewModel.workStickerAdapter = StickerGalleryFragment.StickerAdapter(StickerManager.stickerList, {
                binding.stickerPanel.hide()
                viewModel.selectListener.invoke(it)
            }, viewModel.currentSticker.value)
        }
        if (viewModel.recentStickerAdapter == null) {
            viewModel.recentStickerAdapter = StickerGalleryFragment.StickerAdapter(StickerManager.getRecentlyStickerList(requireContext()), {
                binding.stickerPanel.hide()
                viewModel.selectListener.invoke(it)
            }, viewModel.currentSticker.value)
        }
        if (viewModel.stickerPanelAdapter == null) {
            viewModel.stickerPanelAdapter = StickerPanel.StickerPanelAdapter(
                childFragmentManager, viewModel.workStickerAdapter!!, viewModel.recentStickerAdapter!!
            )
        }
        binding.stickerPanel.initAdapter(viewModel.stickerPanelAdapter!!) {
            viewModel.selectListener.invoke(it)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.editEngineCore.release()
    }
}
