package online.yllh.smartinspect.view.gallery.local

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.Toast
import androidx.core.net.toUri
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.tapapk.camera_engine.utils.Exif
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.databinding.FragmentPhotoBinding
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.extension.readBytesFromUri
import online.yllh.smartinspect.extension.toJpegByteArray
import online.yllh.smartinspect.media.getAllImagesSortedByTime
import online.yllh.smartinspect.model.Image
import online.yllh.smartinspect.network.PublicRequest
import online.yllh.smartinspect.provider.ApplicationContextProvider
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.lang.Thread.sleep

class PhotoFragment(
    private val initPhotoScrollY: Int,
    private val scrollListener: (Int) -> Unit,
    private val isImportModel: Boolean = false,
    private val importModeAlbumId: String? = null,
) : BaseFragment() {
    private lateinit var binding: FragmentPhotoBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPhotoBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        lifecycleScope.launch(Dispatchers.IO) {
            val images = getAllImagesSortedByTime(requireContext())
            launch(Dispatchers.Main) {
                binding.photoRecyclerView.adapter = PhotoAdapter(images)
                binding.photoRecyclerView.layoutManager = GridLayoutManager(requireContext(), 3)
                Log.e("initPhotoScrollY", "初始化位置${initPhotoScrollY}")
                binding.photoRecyclerView.scrollBy(0, initPhotoScrollY)
                binding.photoRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                    var mDy = 0
                    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                        super.onScrolled(recyclerView, dx, dy)
                        mDy += dy
                        scrollListener.invoke(mDy)
                    }
                })
            }
        }
    }

    inner class PhotoAdapter(private val photoList: List<Image>) : RecyclerView.Adapter<PhotoAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_photo, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val photoUri = photoList[position]
            val context = holder.itemView.context
            val displayMetrics = context.resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val imageWidth = (screenWidth - 18.dp) / 3
            holder.photoImageView.layoutParams.width = imageWidth
            holder.photoImageView.layoutParams.height = imageWidth
            val requestOptions = RequestOptions()
                .format(DecodeFormat.PREFER_RGB_565) // 设置图片格式为 RGB_565，降低内存消耗
                .override(imageWidth, imageWidth)
            Glide.with(holder.itemView.context)
                .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                .asDrawable()               //相对而言，asDrawable()比asBitmap要省（具体相关可以去百度）
                .sizeMultiplier(0.5f)
                .skipMemoryCache(true)              //跳过内存缓存
                .diskCacheStrategy(DiskCacheStrategy.ALL)//全部使用磁盘缓存
                .load(photoUri.uri)
                .placeholder(R.drawable.placeholder)
                .apply(requestOptions)
                .into(holder.photoImageView)
            holder.itemView.setOnClickListener {
                if (isImportModel) {
                    val dialog = LoadingDialog(requireContext())
                    dialog.setButtonVisibility(View.GONE)
                    dialog.setTitle("正在上传")
                    dialog.setCancelable(false)
                    dialog.setCanceledOnTouchOutside(false)
                    dialog.show()
                    lifecycleScope.launch(Dispatchers.IO) {
                        var bitmap: Bitmap?
                        var inputStream: InputStream? = null
                        try {
                            inputStream = requireContext().contentResolver.openInputStream(photoUri.uri.toString().toUri())
                            bitmap = BitmapFactory.decodeStream(inputStream)
                        } catch (e: IOException) {
                            bitmap = null
                            e.printStackTrace()
                        } finally {
                            inputStream?.close()
                        }
                        val cacheDir: File = File(ApplicationContextProvider.context.cacheDir.path + "/cameraTmp")
                        if (!cacheDir.exists()) {
                            cacheDir.mkdir()
                        }
                        val imageFile = File(cacheDir.path + "/" + System.currentTimeMillis() + ".jpg")
                        if (!imageFile.exists()) {
                            cacheDir.createNewFile()
                        }
                        val exifData = Exif.getExifData(photoUri.uri.toString().toUri().readBytesFromUri(requireContext()))
                        val imageData = Exif.exifToJpegData(bitmap?.toJpegByteArray(100), exifData)
                        val outputStream = FileOutputStream(imageFile)
                        outputStream.write(imageData)
                        outputStream.close()
                        val url = PublicRequest.uploadFile(imageFile)
                        if (url != null && it != null) {
                            PublicRequest.uploadImage(requireContext(), url, importModeAlbumId.orEmpty(), binding.root)
                        }
                        launch(Dispatchers.Main) {
                            sleep(1000)
                            dialog.dismiss()
                        }
                    }
                } else {
                    binding.root.findNavController().navigate(R.id.action_galleryFragment_to_editImageFragment, Bundle().apply {
                        this.putSerializable(FragmentArguments.ImageUri, photoUri.uri.toString())
                        this.putString("albumType", "all_photos")
                        this.putInt("currentPhotoIndex", position)
                    })
                }
            }
        }

        override fun getItemCount(): Int {
            return photoList.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val photoImageView: ImageView = itemView.findViewById(R.id.photoImageView)
        }
    }
}

