package online.yllh.smartinspect.view.setting

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.edit
import androidx.navigation.findNavController
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentSettingBinding

class SettingFragment : BaseFragment() {
    private lateinit var binding: FragmentSettingBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ) = FragmentSettingBinding.inflate(inflater, container, false).also { binding = it }.root

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        binding.myAccount.setOnClickListener {
            val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
            val userCookies = sp.getString(SpKey.UserCookies, "")
            if (userCookies?.isEmpty() == false) {
                binding.root.findNavController().navigate(R.id.action_settingFragment_to_myFragment)
            } else {
                binding.root.findNavController().navigate(R.id.action_settingFragment_to_loginFragment)
            }
        }
        binding.titleBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }
        val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        binding.saveOrgImageSwitch.isChecked = sp.getBoolean(SpKey.SaveOrgImage, false)
        binding.saveOrgImageSwitch.setOnCheckedChangeListener { _, isChecked ->
            sp.edit {
                if (!isChecked) {
                    putBoolean(SpKey.SaveOrgImage, false)
                } else {
                    putBoolean(SpKey.SaveOrgImage, true)
                }
            }
        }
        binding.cahche.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_settingFragment_to_cacheFragment)

        }
        binding.about.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_settingFragment_to_userAgentFragment)
        }
        binding.privacy.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_settingFragment_to_privacyFragment)
        }
        binding.checkUpadte.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_settingFragment_to_updateFragment)
        }
    }
}
