package online.yllh.smartinspect.view.changeUserInfo

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.core.content.edit
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.github.dhaval2404.imagepicker.ImagePicker
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentChangeUserInfoBinding
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.extension.serializable
import online.yllh.smartinspect.network.PublicRequest
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.ChangeUserModel
import retrofit2.awaitResponse
import java.io.File
import java.io.FileOutputStream

class ChangeUserInfoFragment : BaseFragment() {
    private lateinit var binding: FragmentChangeUserInfoBinding
    private lateinit var countDownTimer: CountDownTimer
    private var headImg: String = ""
    private var originalAvatar: String = "" // 保存原始头像URL，用于取消时恢复
    private var isUploadingAvatar: Boolean = false // 头像上传状态

    // 处理系统返回键
    private val backPressedCallback = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            restoreOriginalAvatar()
            binding.root.findNavController().popBackStack()
        }
    }
    private val phone: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.serializable("phone") ?: ""
    }
    private val userId: Int by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.serializable("id") ?: -1
    }
    private val avatar: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.serializable("avatar") ?: ""
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentChangeUserInfoBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        
        // 检查必要参数是否存在，如果不存在则返回上一页
        if (userId == -1) {
            Toast.makeText(requireContext(), "用户信息获取失败", Toast.LENGTH_SHORT).show()
            binding.root.findNavController().popBackStack()
            return
        }

        // 保存原始头像URL
        originalAvatar = avatar

        // 注册系统返回键处理
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, backPressedCallback)

        binding.appBar.setBackButtonClickListener {
            restoreOriginalAvatar()
            binding.root.findNavController().popBackStack()
        }
        binding.cancelButton.setOnClickListener {
            restoreOriginalAvatar()
            binding.root.findNavController().popBackStack()
        }
        binding.orgPhoneNum.text = phone

        binding.sendSmsCode.setOnClickListener {
            sendSmsCode()
        }
        binding.okButton.setOnClickListener {
            changeUserInfo(requireContext(), binding.root)
        }
        binding.avatar.setOnClickListener {
            ImagePicker.with(this)
                .start()
        }
        binding.changePhoneNum.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_changeUserInfoFragment_to_changeUserPhoneFragment, Bundle().apply {
                putSerializable("phone", phone)
                putSerializable("id", userId)
                putSerializable("avatar", avatar)
            })
        }

        // 初始化显示头像
        loadAvatar(avatar)
    }

    fun changeUserInfo(context: Context, root: View) {
        // 检查用户ID是否有效
        if (userId == -1) {
            Toast.makeText(context, "用户信息获取失败，无法修改", Toast.LENGTH_SHORT).show()
            return
        }

        // 如果正在上传头像，禁止提交
        if (isUploadingAvatar) {
            Toast.makeText(context, "头像正在上传中，请稍候", Toast.LENGTH_SHORT).show()
            return
        }

        val dialog = LoadingDialog(context)
        dialog.setTitle("请稍候")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                val request = RetrofitClient.service.changeUserInfo(
                    ChangeUserModel(
                        nickName = binding.nickName.text.toString().ifEmpty { null },
                        newPhone = binding.phoneNum.text.toString().ifEmpty { null },
                        newPhoneVerificationCode = binding.verifyCode.text.toString().ifEmpty { null },
                        headImg = headImg,
                        id = userId
                    )
                )
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            if (binding.phoneNum.text.toString().isNotEmpty()) {
                                sp.edit { remove(SpKey.UserCookies) }
                                binding.root.findNavController().navigate(R.id.action_changeUserInfoFragment_to_loginFragment)
                                Toast.makeText(context, "修改用户信息成功 请重新登录", Toast.LENGTH_SHORT).show()
                            } else {
                                Toast.makeText(context, "修改用户信息成功", Toast.LENGTH_SHORT).show()
                                binding.root.findNavController().popBackStack()
                            }
                        } else {
                            Toast.makeText(context, "修改失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            //Image Uri will not be null for RESULT_OK
            val uri: Uri = data?.data!!

            // 显示上传loading并禁用完成按钮
            val uploadDialog = LoadingDialog(requireContext())
            uploadDialog.setTitle("正在上传头像...")
            uploadDialog.setButtonVisibility(View.GONE)
            uploadDialog.setCancelable(false)
            uploadDialog.show()

            isUploadingAvatar = true
            binding.okButton.isEnabled = false

            lifecycleScope.launch(Dispatchers.IO) {
                try {
                    saveUriToCache(requireContext(), uri)?.let { localFile ->
                        launch(Dispatchers.Main) {
                            Glide.with(requireContext())
                                .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.NONE))
                                .load(localFile)
                                .placeholder(R.drawable.ic_defult_avatar)
                                .centerCrop()
                                .into(binding.avatar)
                        }
                        headImg = PublicRequest.uploadFile(localFile).orEmpty()

                        launch(Dispatchers.Main) {
                            if (headImg.isNotEmpty()) {
                                Glide.with(requireContext()).clear(binding.avatar)
                                Glide.with(requireContext())
                                    .applyDefaultRequestOptions(
                                        RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.NONE)
                                            .skipMemoryCache(true)
                                    )
                                    .load(headImg + "?t=" + System.currentTimeMillis())
                                    .placeholder(R.drawable.ic_defult_avatar)
                                    .centerCrop()
                                    .into(binding.avatar)
                                Toast.makeText(requireContext(), "头像上传成功", Toast.LENGTH_SHORT).show()
                            } else {
                                loadAvatar(originalAvatar)
                                Toast.makeText(requireContext(), "头像上传失败", Toast.LENGTH_SHORT).show()
                            }
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    launch(Dispatchers.Main) {
                        loadAvatar(originalAvatar)
                        Toast.makeText(requireContext(), "头像上传失败", Toast.LENGTH_SHORT).show()
                    }
                } finally {
                    launch(Dispatchers.Main) {
                        uploadDialog.dismiss()
                        isUploadingAvatar = false
                        binding.okButton.isEnabled = true
                    }
                }
            }
        }
    }

    fun saveUriToCache(context: Context, uri: Uri): File? {
        val inputStream = context.contentResolver.openInputStream(uri)
        val cacheDir = context.cacheDir
        val fileName = "cached_file"
        val file = File(cacheDir, fileName)

        inputStream?.use { input ->
            FileOutputStream(file).use { output ->
                input.copyTo(output)
            }
        }

        return file
    }

    fun sendSmsCode() {
        countDownTimer = object : CountDownTimer(60000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                // 在计时器每隔一秒触发时更新文本
                val secondsLeft = millisUntilFinished / 1000
                binding.sendSmsCode.setText("$secondsLeft s")
                binding.sendSmsCode.isEnabled = false
            }

            override fun onFinish() {
                // 倒计时完成时执行的操作
                binding.sendSmsCode.setText("发送")
                binding.sendSmsCode.isEnabled = true
                // 这里可以添加倒计时结束后的逻辑
            }
        }
        countDownTimer.start()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = RetrofitClient.service.smsByUpdatePhone(binding.phoneNum.text.toString())
                if (result.awaitResponse().isSuccessful) {
                    launch(Dispatchers.Main) {
                        Toast.makeText(requireContext(), "发送成功", Toast.LENGTH_SHORT).show()
                    }
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    Toast.makeText(requireContext(), "网络连接失败", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    /**
     * 加载头像的通用方法
     */
    private fun loadAvatar(avatarUrl: String) {
        Glide.with(requireContext())
            .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
            .load(avatarUrl)
            .placeholder(R.drawable.ic_defult_avatar)
            .centerCrop() // 添加centerCrop以实现类似CSS object-fit: cover的效果
            .into(binding.avatar)
    }

    /**
     * 恢复原始头像，用于用户取消操作时
     */
    private fun restoreOriginalAvatar() {
        if (headImg.isNotEmpty() && headImg != originalAvatar) {
            // 如果用户上传了新头像但没有保存，恢复到原始头像
            headImg = originalAvatar
            loadAvatar(originalAvatar)
        }
    }
}
