package online.yllh.smartinspect.view.my

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.edit
import androidx.fragment.app.viewModels
import androidx.navigation.findNavController
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentMyBinding
import online.yllh.smartinspect.dialog.LogoutDialog

class MyFragment : BaseFragment() {

    private lateinit var binding: FragmentMyBinding
    private val viewModel by viewModels<MyFragmentViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ) = FragmentMyBinding.inflate(inflater, container, false).also { binding = it }.root

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        viewModel.getUserInfo(requireContext(), binding.root)
        binding.logOut.setOnClickListener {
            val dialog = LogoutDialog(requireContext())
            dialog.show()
            dialog.setOkListener {

            }
            dialog.setCancelListener {

            }
        }
        binding.changeUserInfo.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_myFragment_to_changeUserInfoFragment, Bundle().apply {
                putSerializable("phone", viewModel.userPhone.value)
                putSerializable("id", viewModel.userInfo?.id)
                putSerializable("avatar", viewModel.userInfo?.headImg.orEmpty())
            })
        }
        binding.appBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }
        observeOn(viewModel.userPhone) {
            binding.phoneNum.text = it
        }
        observeOn(viewModel.userName) {
            binding.nickName.text = it
        }
        observeOn(viewModel.headImg) {
            Glide.with(requireContext())
                .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                .load(it)
                .placeholder(R.drawable.ic_defult_avatar)
                .centerCrop() // 添加centerCrop以实现类似CSS object-fit: cover的效果
                .into(binding.avatar)
        }
        binding.logOut.setOnClickListener {
            val dialog = LogoutDialog(requireContext())
            dialog.setUserName(viewModel.userName.value)
            dialog.setAvatar(viewModel.headImg.value)
            dialog.setOkListener {
                val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                sp.edit { remove(SpKey.UserCookies) }
                binding.root.findNavController().navigate(R.id.action_myFragment_to_loginFragment)
            }
            dialog.setCancelListener { }
            dialog.show()
        }
    }
}
