package online.yllh.smartinspect.view.camera

import android.Manifest
import android.content.Context
import android.graphics.SurfaceTexture
import android.hardware.Camera
import android.os.Build
import android.util.Log
import androidx.core.content.edit
import androidx.exifinterface.media.ExifInterface
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.tapapk.camera_engine.core.CameraEngineCore
import com.tapapk.camera_engine.core.FlashMode
import com.tapapk.camera_engine.`interface`.GLCreateListener
import com.tapapk.camera_engine.utils.Exif
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import online.yllh.smartinspect.GlobalVals
import online.yllh.smartinspect.contents.PermissionCode
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.extension.toJpegByteArray
import online.yllh.smartinspect.media.saveImageToGallery
import online.yllh.smartinspect.model.Location
import online.yllh.smartinspect.model.SnapshotModel
import online.yllh.smartinspect.model.WatermarkState
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.response.Project
import online.yllh.smartinspect.provider.ApplicationContextProvider
import online.yllh.smartinspect.sticker.BaseSticker
import online.yllh.smartinspect.sticker.EmptySticker
import online.yllh.smartinspect.uiwidget.sticker.StickerGalleryFragment
import online.yllh.smartinspect.uiwidget.sticker.StickerPanel
import online.yllh.smartinspect.utils.LocationHelper
import online.yllh.smartinspect.utils.waitUntilNextSecond
import pub.devrel.easypermissions.EasyPermissions
import pub.devrel.easypermissions.EasyPermissions.PermissionCallbacks
import pub.devrel.easypermissions.PermissionRequest
import retrofit2.awaitResponse
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlin.math.roundToInt


enum class FocalMode {
    Macro,
    Long,
    Standard,
    Wide
}

enum class CountDownMode {
    OFF,
    THREE_SECONDS,
    FIVE_SECONDS,
    TEN_SECONDS
}

class CameraViewModel : ViewModel(), PermissionCallbacks {
    val cameraEngineCore = CameraEngineCore()
    private var _cameraIsInit = false
    private val _cameraTextureIsReady = MutableStateFlow(false)
    private val _cameraPermissionIsReady = MutableStateFlow(false)
    val cameraStoragePermissionIsReady = MutableStateFlow(false)
    val cameraLocationPermissionIsReady = MutableStateFlow(false)
    val currentStickerObj = MutableStateFlow<BaseSticker>(EmptySticker())
    val cameraFlashMode = MutableStateFlow(FlashMode.OFF)
    val cameraCountDownEnable = MutableStateFlow(false)
    val cameraCountDown = MutableStateFlow(0)
    val countDownMode = MutableStateFlow(CountDownMode.OFF)
    val focalMode = MutableStateFlow(FocalMode.Standard)
    val cameraFacing = MutableStateFlow(Camera.CameraInfo.CAMERA_FACING_BACK)
    private val _isSnapshotInProgress = MutableStateFlow(false)
    val isSnapshotInProgress = _isSnapshotInProgress
    val isShowWatermark = MutableStateFlow(false)
    val currentSticker = MutableStateFlow("dateAndLocationSticker")
    var isShowProjectView = MutableStateFlow(false)
    var projectName = MutableStateFlow("")
    var projectId = MutableStateFlow("")
    var projectRemark = MutableStateFlow("输入内容")
    var actionProjectJsonUiStatus = MutableSharedFlow<String>()

    var poiName: String = ""
    val location = LocationHelper.location
    val located = LocationHelper.located
    val watermarkState = MutableStateFlow(WatermarkState())

    var workStickerAdapter: StickerGalleryFragment.StickerAdapter? =
        null //= StickerGalleryFragment.StickerAdapter(StickerManager.stickerList,selectListener,currentSticker)
    var recentStickerAdapter: StickerGalleryFragment.StickerAdapter? = null //= StickerGalleryFragment.StickerAdapter(StickerManager,selectListener,currentSticker)
    var stickerPanelAdapter: StickerPanel.StickerPanelAdapter? = null

    var snapshot = SnapshotModel()

    init {
        viewModelScope.launch {
            location.collect { loc ->
                Log.d("Maosi", "loc: $loc")
                val state = watermarkState.value
                poiName = loc.poiName
                val address = getWatermarkAddress(poiName, loc)
                val newState = state.copy(
                    longitude = loc.longitude,
                    latitude = loc.latitude,
                    altitude = loc.altitude,
                    address = address,
                )
                watermarkState.value = newState
            }
        }
        viewModelScope.launch {
            GlobalVals.weather.collect { weather ->
                watermarkState.value = watermarkState.value.copy(weather = weather)
            }
        }
        viewModelScope.launch {
            waitUntilNextSecond()
            while (true) {
                watermarkState.value = watermarkState.value.copy(time = System.currentTimeMillis())
                delay(1000)
            }
        }
    }

    fun startLocating() {
        if (cameraLocationPermissionIsReady.value) {
            LocationHelper.start()
            viewModelScope.launch {
                while (true) {
                    delay(5000)
                    LocationHelper.refreshLocation()
                }
            }
        }
    }

    fun stopLocating() {
        LocationHelper.stop()
    }

    suspend fun initCameraEnvironment(context: Context, surface: SurfaceTexture, width: Int, height: Int): Boolean {
        if (_cameraIsInit) {
            return true
        }
        return suspendCoroutine {
            cameraEngineCore.create(
                context = context,
                surface = surface,
                width = width,
                height = height,
                glCreateListener = object : GLCreateListener {
                    override fun success() {
                        _cameraIsInit = true
                        switchFocalMode(FocalMode.Standard)
                        switchFlash(cameraFlashMode.value)
                        val bitmap = EmptySticker().createBitmap(width, height)
                        cameraEngineCore.setSticker(bitmap) //这里一定要设置一个空白贴纸，不然预览黑屏
                        it.resume(true)
                    }

                    override fun error() {
                        _cameraIsInit = false
                        it.resume(false)
                    }
                }
            )
        }
    }

    val selectListener: (String) -> Unit = {
        currentSticker.value = it
    }

    fun setCurrentSticker(sticker: String) {
        workStickerAdapter?.currentSticker = sticker
        recentStickerAdapter?.currentSticker = sticker
        workStickerAdapter?.notifyDataSetChanged()
        recentStickerAdapter?.notifyDataSetChanged()
    }

    fun getWatermarkAddress(poiName: String, location: Location = this.location.value): String {
        val sp = ApplicationContextProvider.context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        var address = if (sp.getBoolean(SpKey.EnableLevel, true)) {
            val addressLevel = getAddressLevel(ApplicationContextProvider.context, location)
            "${addressLevel}·${poiName}"
        } else poiName
        address = address.trim().let { if (it == "·") "" else it }
        return address
    }

    fun updateWatermarkAddress(poiName: String) {
        this.poiName = poiName
        watermarkState.value = watermarkState.value.copy(address = getWatermarkAddress(poiName))
    }

    fun refreshLocationLevel() {
        updateWatermarkAddress(poiName)
    }

    private fun getAddressLevel(context: Context, location: Location = this.location.value): String {
        val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        return when (sp.getString(SpKey.LocationLevel, "city")) {
            "country" -> location.country
            "province" -> location.province
            "city" -> location.city
            "district" -> location.district
            "street" -> location.street
            "town" -> location.town
            "none" -> ""
            else -> ""
        }
    }

    fun setSticker(sticker: BaseSticker, width: Int, height: Int) {
        cameraEngineCore.setSticker(sticker.createBitmap(width, height))
        currentStickerObj.value = sticker
    }

    fun destroyCameraEnvironment() {
        cameraEngineCore.destroy()
        _cameraIsInit = false
    }

    fun surfaceSizeChange(width: Int, height: Int) {
        cameraEngineCore.changeCameraViewSize(width, height)
    }

    fun setSurfaceTextureAvailable(available: Boolean) {
        _cameraTextureIsReady.update { available }
    }

    fun checkPermission(fragment: CameraFragment) {
        val hasCameraPermission = EasyPermissions.hasPermissions(ApplicationContextProvider.context, Manifest.permission.CAMERA)
        val hasStoragePermission = if (Build.VERSION.SDK_INT >= 33) {
            EasyPermissions.hasPermissions(ApplicationContextProvider.context, Manifest.permission.READ_MEDIA_IMAGES)
        } else {
            EasyPermissions.hasPermissions(ApplicationContextProvider.context, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE)
        }
        if (hasCameraPermission && hasStoragePermission) {
            _cameraPermissionIsReady.value = true
        } else {
            if (Build.VERSION.SDK_INT >= 33) {
                EasyPermissions.requestPermissions(
                    PermissionRequest.Builder(
                        fragment, PermissionCode.CAMERA,
                        Manifest.permission.CAMERA,
                        Manifest.permission.READ_MEDIA_IMAGES,
                    )
                        .setRationale("未开启相机或者存储权限，无法使用相机功能")
                        .setPositiveButtonText("申请权限")
                        .setNegativeButtonText("取消")
                        .build()
                )
            } else {
                EasyPermissions.requestPermissions(
                    PermissionRequest.Builder(
                        fragment, PermissionCode.CAMERA,
                        Manifest.permission.CAMERA,
                        Manifest.permission.READ_EXTERNAL_STORAGE,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                    )
                        .setRationale("未开启相机或者存储权限，无法使用相机功能")
                        .setPositiveButtonText("申请权限")
                        .setNegativeButtonText("取消")
                        .build()
                )
            }

        }
    }

    fun checkLocation(fragment: CameraFragment) {
        Log.d("kofua", "checkLocation 1")
        val hasLocationPermission =
            EasyPermissions.hasPermissions(ApplicationContextProvider.context, Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION)
        if (hasLocationPermission) {
            cameraLocationPermissionIsReady.value = true
            Log.d("kofua", "checkLocation 2")
        } else {
            Log.d("kofua", "checkLocation 3")
            EasyPermissions.requestPermissions(
                PermissionRequest.Builder(fragment, PermissionCode.LOCATION, Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION)
                    .setRationale("未开启定位权限，无法使用贴纸定位和照片定位功能")
                    .setPositiveButtonText("申请权限")
                    .setNegativeButtonText("取消")
                    .build()
            )
        }
    }


    fun switchCountDown() {
        cameraCountDownEnable.value = !cameraCountDownEnable.value
    }

    fun switchCountDownMode(mode: CountDownMode) {
        countDownMode.value = mode
        cameraCountDownEnable.value = mode != CountDownMode.OFF
    }

    fun switchFlash() {
        if (cameraFlashMode.value == FlashMode.OFF) {
            cameraFlashMode.value = FlashMode.ON
        } else {
            cameraFlashMode.value = FlashMode.OFF
        }
        cameraEngineCore.setFlashMode(cameraFlashMode.value)
    }

    fun switchFlash(mode: FlashMode) {
        cameraEngineCore.setFlashMode(mode)
    }

    fun switchFocalMode(focalMode: FocalMode) {
        this.focalMode.value = focalMode
        val zoomRatio = when (focalMode) {
            FocalMode.Wide -> 0f
            FocalMode.Standard -> 0.05f
            FocalMode.Long -> 0.5f
            FocalMode.Macro -> 1f
        }
        cameraEngineCore.setCameraZoom((zoomRatio * cameraEngineCore.maxZoom).roundToInt())
    }

    fun switchCamera() {
        if (cameraFacing.value == Camera.CameraInfo.CAMERA_FACING_BACK) {
            cameraFacing.value = Camera.CameraInfo.CAMERA_FACING_FRONT
        } else {
            cameraFacing.value = Camera.CameraInfo.CAMERA_FACING_BACK
        }
        cameraEngineCore.switchCamera(cameraFacing.value)
        switchFocalMode(FocalMode.Standard)
    }

    fun writeLocationToExif(imagePath: File, latitude: Double, longitude: Double) {
        try {
            val exifInterface = ExifInterface(imagePath)
            exifInterface.setLatLong(latitude, longitude)
            exifInterface.saveAttributes()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    fun snapshot(context: Context, callback: (String, Int, Int) -> Unit) {
        if (_isSnapshotInProgress.value) {
            return
        }

        viewModelScope.launch {
            try {
                _isSnapshotInProgress.value = true

                if (cameraCountDownEnable.value) {
                    val countDownSeconds = when (countDownMode.value) {
                        CountDownMode.THREE_SECONDS -> 3
                        CountDownMode.FIVE_SECONDS -> 5
                        CountDownMode.TEN_SECONDS -> 10
                        CountDownMode.OFF -> 0
                    }
                    for (i in countDownSeconds downTo 1) {
                        cameraCountDown.value = i
                        delay(1000)
                    }
                    cameraCountDown.value = 0
                }

                cameraEngineCore.snapshot { bitmap, exifData ->
                    viewModelScope.launch(Dispatchers.IO) {
                        try {
                            val cacheDir = File(ApplicationContextProvider.context.cacheDir.path + "/cameraTmp")
                            if (!cacheDir.exists()) {
                                cacheDir.mkdirs()
                            }
                            val imageFile = File(cacheDir.path + "/" + System.currentTimeMillis() + ".jpg")
                            if (!imageFile.exists()) {
                                imageFile.createNewFile()
                            }
                            val fileByte = Exif.exifToJpegData(bitmap.toJpegByteArray(100), exifData)
                            val outputStream = FileOutputStream(imageFile)
                            outputStream.write(fileByte)
                            outputStream.close()
                            if (location.value.latitude != 0.0 && location.value.longitude != 0.0) {
                                writeLocationToExif(imageFile, location.value.latitude, location.value.longitude)
                            }
                            val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            if (sp.getBoolean(SpKey.SaveOrgImage, false)) {
                                saveImageToGallery(context, imageFile)
                            }
                            viewModelScope.launch(Dispatchers.Main) {
                                callback.invoke(imageFile.path, bitmap.width, bitmap.height)
                                _isSnapshotInProgress.value = false
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            viewModelScope.launch(Dispatchers.Main) {
                                _isSnapshotInProgress.value = false
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                _isSnapshotInProgress.value = false
            }
        }
    }

    val cameraIsReady = combine(
        _cameraTextureIsReady,
        _cameraPermissionIsReady
    ) { cameraTextureIsReady, cameraPermissionIsReady ->
        cameraTextureIsReady && cameraPermissionIsReady
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Lazily,
        initialValue = false
    )

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this)
    }

    override fun onPermissionsGranted(requestCode: Int, perms: MutableList<String>) {
        when (requestCode) {
            PermissionCode.CAMERA -> {
                _cameraPermissionIsReady.value = true
            }

            PermissionCode.STORAGE -> {
                cameraStoragePermissionIsReady.value = true
            }

            PermissionCode.LOCATION -> {
                Log.d("kofua", "checkLocation granted")
                cameraLocationPermissionIsReady.value = true
            }
        }
    }

    override fun onPermissionsDenied(requestCode: Int, perms: MutableList<String>) {
        when (requestCode) {
            PermissionCode.CAMERA -> {
                _cameraPermissionIsReady.value = false
            }

            PermissionCode.STORAGE -> {
                cameraStoragePermissionIsReady.value = false
            }

            PermissionCode.LOCATION -> {
                Log.d("kofua", "checkLocation denied")
                cameraLocationPermissionIsReady.value = false
            }
        }
    }

    fun checkProjectList(context: Context) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                val request = RetrofitClient.service.getProjectList()
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            val actionProjectJson = sp.getString(SpKey.ActionProject, "").orEmpty()
                            if (actionProjectJson.isNotEmpty()) {
                                val actionProject = Gson().fromJson(actionProjectJson, Project::class.java)
                                val project = body.data.firstOrNull { it.id == actionProject.id }
                                if (project == null) {
                                    sp.edit { putString(SpKey.ActionProject, "") }
                                    actionProjectJsonUiStatus.emit("")
                                } else {
                                    actionProjectJsonUiStatus.emit(actionProjectJson)
                                }
                            } else {
                                actionProjectJsonUiStatus.emit("")
                            }
                        } else {
//                            Toast.makeText(context,"获取列表失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            sp.edit {
                                putString(SpKey.UserCookies, "")
                                putString(SpKey.ActionProject, "")
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}
