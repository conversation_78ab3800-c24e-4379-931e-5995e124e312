package online.yllh.smartinspect.view.gallery.cloud

import android.content.Context
import android.view.View
import android.widget.Toast
import androidx.core.content.edit
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.findNavController
import com.baidu.mapapi.model.LatLng
import com.baidu.mapapi.search.geocode.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.UploadImageToGallery
import online.yllh.smartinspect.network.model.response.Message
import online.yllh.smartinspect.provider.ApplicationContextProvider
import online.yllh.smartinspect.utils.DownloadUtils
import retrofit2.awaitResponse


class CloudPhotoExifDetailViewModel : ViewModel() {
    val imageByte = MutableStateFlow<ByteArray?>(null)
    val address = MutableStateFlow<String?>("")
    var loadingDialog: LoadingDialog? = null
    var voiceList = MutableStateFlow<List<Message>?>(emptyList())
    var messageList = MutableStateFlow<List<Message>?>(emptyList())
    var imageId = ""
    var photoAddress = ""

    fun getImageDetail(context: Context, imageId: String, root: View) {
        loadingDialog = LoadingDialog(context)
        loadingDialog?.setTitle("正在获取照片详情")
        loadingDialog?.setButtonVisibility(View.GONE)
        loadingDialog?.setCancelable(false)
        loadingDialog?.show()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.getPhotoById(imageId)
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            voiceList.value = body.data.voiceMessages
                            messageList.value = body.data.customMessages
                            <EMAIL> = body.data.id.orEmpty()
                            <EMAIL> = body.data.photoAddress.orEmpty()
                            loadingDialog?.dismiss()
                            loadingDialog = null
                            launch(Dispatchers.IO) {
                                getImageByte(body.data.photoAddress.orEmpty())
                            }
                        } else {
                            Toast.makeText(context, "获取详情失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {

            }
        }
    }

    fun uploadImage(context: Context, data: UploadImageToGallery, root: View) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在同步云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.saveMessage(data)
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        getImageDetail(context, imageId, root)
                        if (body?.data != null && body.ok) {
                            Toast.makeText(context, "同步成功", Toast.LENGTH_SHORT).show()
                        } else {
                            Toast.makeText(context, "同步失败 请重新上传", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                launch(Dispatchers.Main) {
                    Toast.makeText(context, "网络连接失败 请重新上传", Toast.LENGTH_SHORT).show()
                }
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }


    private fun getImageByte(imageUrl: String) {
        if (imageByte.value != null) {
            loadingDialog?.dismiss()
            loadingDialog = null
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            imageByte.value = DownloadUtils.downloadFile(imageUrl)
        }
    }

    private val listener: OnGetGeoCoderResultListener = object : OnGetGeoCoderResultListener {
        override fun onGetGeoCodeResult(p0: GeoCodeResult?) {
        }

        override fun onGetReverseGeoCodeResult(result: ReverseGeoCodeResult?) {
            if (result == null) {
                return
            }
            address.value = result.address
        }

    }

    fun getAddress(latitude: Float, longitude: Float) {
        val geoCoder = GeoCoder.newInstance()
        geoCoder.setOnGetGeoCodeResultListener(listener) // listener为自定义的结果监听器
        val latlng = LatLng(latitude.toDouble(), longitude.toDouble())
        geoCoder.reverseGeoCode(ReverseGeoCodeOption().location(latlng))
    }
}
