package online.yllh.smartinspect.view.gallery.cloud

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.PopupMenu
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.databinding.FragmentPhotoAlbumBinding
import online.yllh.smartinspect.dialog.CreateGalleryDialog
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.network.PublicRequest
import online.yllh.smartinspect.network.model.response.CloudPhotoAlbumModel

class CloudPhotoAlbumFragment : BaseFragment() {
    private lateinit var binding: FragmentPhotoAlbumBinding
    private val albumId: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.AlbumId) ?: throw IllegalArgumentException("imageInfo is null")
    }
    private val albumName: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.AlbumName) ?: throw IllegalArgumentException("imageInfo is null")
    }
    private val isImportMode: Boolean by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getBoolean(FragmentArguments.IsImportMode) ?: false
    }
    private val importModeAlbumId: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.ImportModeAlbumId).orEmpty()
    }
    private val projectId: Int? by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getInt(FragmentArguments.ProjectId)
    }
    private val viewModel by lazy { CloudPhotoAlbumViewModel() }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPhotoAlbumBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        viewModel.getPhotosByAlbum(requireContext(), albumId, binding.root)

        binding.appBar.setTitle(albumName)
        binding.appBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }
        val adapter = PhotoAdapter(emptyList())
        binding.photoRecyclerView.adapter = adapter
        binding.photoRecyclerView.layoutManager = GridLayoutManager(requireContext(), 3)
        observeOn(viewModel.photos) {
            adapter.photoList = it
            Log.d("photoList", it.size.toString())
            adapter.notifyDataSetChanged()
        }
        binding.info.setOnClickListener {
            binding.root.findNavController().navigate(R.id.galleryInfoFragment, Bundle().apply {
                putSerializable(FragmentArguments.AlbumId, albumId)
            })
        }
        binding.addGallery.visibility = View.GONE
        binding.importPhoto.setOnClickListener {
            val popupMenu = PopupMenu(requireContext(), it)
            popupMenu.menuInflater.inflate(R.menu.popup_menu, popupMenu.menu)
            popupMenu.setOnMenuItemClickListener { menuItem ->
                when (menuItem.itemId) {
                    R.id.importImage -> {
                        binding.root.findNavController().navigate(R.id.galleryFragment, Bundle().apply {
                            putSerializable(FragmentArguments.IsImportMode, true)
                            putSerializable(FragmentArguments.ImportModeAlbumId, albumId)
                        })
                        true
                    }

                    R.id.addGallery -> {
                        val dialog = CreateGalleryDialog(requireContext())
                        dialog.setCancelable(false)
                        dialog.setOkButtonListener {
                            viewModel.addProjectGallery(requireContext(), projectId.toString(), albumId, it, binding.root)
                        }
                        dialog.show()
                        true
                    }
                    // 添加其他选项处理逻辑...
                    else -> false
                }
            }
            popupMenu.show()

        }
        if (isImportMode) {
            binding.info.visibility = View.GONE
            binding.importPhoto.visibility = View.GONE
        }
    }

    inner class PhotoAdapter(var photoList: List<CloudPhotoAlbumModel>) : RecyclerView.Adapter<PhotoAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_photo, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val photoUri = photoList[position]
            val context = holder.itemView.context
            val displayMetrics = context.resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val imageWidth = (screenWidth - 18.dp) / 3
            holder.photoImageView.layoutParams.width = imageWidth
            holder.photoImageView.layoutParams.height = imageWidth
            val requestOptions = RequestOptions()
                .format(DecodeFormat.PREFER_RGB_565) // 设置图片格式为 RGB_565，降低内存消耗
                .override(imageWidth, imageWidth)
            Glide.with(holder.itemView.context)
                .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                .load(photoUri.photoAddress)
                .placeholder(R.drawable.placeholder)
                .apply(requestOptions)
                .into(holder.photoImageView)
            holder.itemView.setOnClickListener {
                if (isImportMode) {
                    lifecycleScope.launch {
                        PublicRequest.uploadImage(requireContext(), photoUri.photoAddress.orEmpty(), importModeAlbumId.orEmpty(), binding.root)
                    }
                } else {
                    binding.root.findNavController().navigate(R.id.action_cloudPhotoAlbumFragment_to_cloudPhotoDetailFragment, Bundle().apply {
                        putSerializable(FragmentArguments.ImageUri, photoList[position].photoAddress)
                        putSerializable(FragmentArguments.ImageId, photoList[position].id)
                        putSerializable(FragmentArguments.AlbumId, albumId)
                        putInt("currentPhotoIndex", position)
                    })
                }
            }
        }

        override fun getItemCount(): Int {
            return photoList.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val photoImageView: ImageView = itemView.findViewById(R.id.photoImageView)
        }
    }
}
