package online.yllh.smartinspect.view.gallery

import androidx.lifecycle.ViewModel
import kotlinx.coroutines.flow.MutableStateFlow

enum class AlbumType {
    LOCAL,
    REMOTE,
    UN_UPLOAD,
    RECYCLE_BIN,
}

class GalleryViewModel : ViewModel() {
    val albumType = MutableStateFlow(AlbumType.LOCAL)
    var photoScrollY = MutableStateFlow(0)
    var albumScrollY = MutableStateFlow(0)

    fun setAlbumType(albumType: AlbumType) {
        if (this.albumType.value == albumType) return
        this.albumType.value = albumType
    }
}
