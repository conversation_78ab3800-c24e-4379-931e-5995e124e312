package online.yllh.smartinspect.view.message

import android.Manifest
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Color
import android.graphics.Point
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.*
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.edit
import androidx.core.content.getSystemService
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.audio.Mp3Recorder
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.contents.PermissionCode
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.BubbleLayoutBinding
import online.yllh.smartinspect.databinding.FragmentPhotoMessageBinding
import online.yllh.smartinspect.dialog.AddContentDialog
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.hideKeyboard
import online.yllh.smartinspect.extension.isHttpUrl
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.media.AudioDurationCalculator
import online.yllh.smartinspect.media.AudioPlayer
import online.yllh.smartinspect.model.MessageUIStatus
import online.yllh.smartinspect.network.PublicRequest
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.ImageMessage
import online.yllh.smartinspect.network.model.request.MessageUpdate
import online.yllh.smartinspect.network.model.request.UploadImageToGallery
import online.yllh.smartinspect.network.model.response.Message
import online.yllh.smartinspect.provider.ApplicationContextProvider
import online.yllh.smartinspect.utils.DownloadUtils
import pub.devrel.easypermissions.EasyPermissions
import pub.devrel.easypermissions.PermissionRequest
import retrofit2.HttpException
import retrofit2.awaitResponse
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID

class PhotoMessageFragment : BaseFragment() {
    private lateinit var binding: FragmentPhotoMessageBinding
    private val messageListJson: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.MessageList) ?: throw IllegalArgumentException("imageInfo is null")
    }
    private val imageId: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.ImageId) ?: throw IllegalArgumentException("imageInfo is null")
    }
    private val isInDialog: Boolean by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getBoolean("isInDialog", false) ?: false
    }
    private var messageList = mutableListOf<MessageUIStatus>()
    private lateinit var mediaRecorder: Mp3Recorder
    private var isRecording = false
    private var filePath = ""
    private var maskView: View? = null
    private lateinit var adapter: MessageAdapter
    private var backCallback: (() -> Unit)? = null
    
    fun setBackCallback(callback: () -> Unit) {
        backCallback = callback
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = FragmentPhotoMessageBinding.inflate(inflater, container, false)
        return binding.root
    }

    suspend fun getImageDetail(context: Context, imageId: String, root: View) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.getPhotoById(imageId)
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            val list = (body.data.voiceMessages.orEmpty()) + (body.data.customMessages.orEmpty())
                            messageList = list.map { MessageUIStatus(data = it) }.toMutableList()
                            messageList.sortBy { it.data.inputTime }
                            adapter.messageList = messageList
                            adapter.notifyDataSetChanged()
                        } else {
                            Toast.makeText(context, "获取详情失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (!isInDialog) {
            ImmersionBar.with(this)
                .fullScreen(true)
                .titleBarMarginTop(view)
                .hideBar(BarHide.FLAG_SHOW_BAR)
                .init()
        } else {
            // 在dialog模式下，设置根布局背景为透明，让圆角背景显示出来
//            binding.parentConstraintLayout.setBackgroundColor(android.graphics.Color.TRANSPARENT)
        }
        
        binding.appBar.setBackButtonClickListener {
            if (isInDialog) {
                backCallback?.invoke()
            } else {
                binding.root.findNavController().popBackStack()
            }
        }
        val gson = Gson()
        val type = object : TypeToken<List<Message>>() {}.type
        val list = gson.fromJson<List<Message>>(messageListJson, type)
        messageList = list.map { MessageUIStatus(data = it) }.toMutableList()
        messageList.sortBy { it.data.inputTime }
        adapter = MessageAdapter(requireContext(), messageList)
        binding.inputPanel.onRecordListenerClick = {
            view.hideKeyboard()
        }
        binding.messageList.clipToPadding = false
        binding.messageList.setPadding(0, 0, 0, 98.dp)
        binding.messageList.layoutManager = LinearLayoutManager(requireContext())
        binding.messageList.adapter = adapter
        binding.addMessage.setOnClickListener {
            binding.inputPanel.focus()
            binding.inputPanel.showPanel()
            binding.addMessage.visibility = View.INVISIBLE
            maskView = View(requireContext())
            val index: Int = binding.parentConstraintLayout.indexOfChild(binding.inputPanel)
            binding.parentConstraintLayout.addView(maskView, index)
            val layoutParams = maskView?.layoutParams as ConstraintLayout.LayoutParams
            layoutParams.width = ConstraintLayout.LayoutParams.MATCH_PARENT
            layoutParams.height = ConstraintLayout.LayoutParams.MATCH_PARENT
            maskView?.layoutParams = layoutParams
            maskView?.setOnClickListener {
                binding.inputPanel.hidePanel()
                view.hideKeyboard()
                binding.addMessage.visibility = View.VISIBLE
                binding.parentConstraintLayout.removeViewAt(index)
                maskView = null
            }
        }
        binding.appBar.setMenuButtonClickListener {
            val uploadList = mutableListOf<ImageMessage>()
            viewLifecycleScope.launch {
                val dialog = LoadingDialog(requireContext())
                dialog.setTitle("正在上传")
                dialog.setButtonVisibility(View.GONE)
                dialog.setCancelable(false)
                dialog.show()
                for ((i, message) in messageList.withIndex()) {
                    when (val type = message.data.type) {
                        0 -> {
                            message.isLocal = false
                            uploadList.add(
                                ImageMessage(
                                    id = message.data.id,
                                    type = type,
                                    value = message.data.value.orEmpty(),
                                    inputTime = message.data.inputTime.orEmpty(),
                                    lockStatus = message.data.lockStatus ?: "0"
                                )
                            )
                        }

                        1 -> {
                            withContext(Dispatchers.IO) {
                                if (message.data.value.orEmpty().isNotEmpty()) {
                                    val second = if (message.data.value.orEmpty().isHttpUrl()) {
                                        message.data.second
                                    } else {
                                        AudioDurationCalculator.getDurationInSeconds(message.data.value.orEmpty())
                                    }
                                    val url = if (message.data.value.orEmpty().isHttpUrl()) {
                                        message.data.value.orEmpty()
                                    } else {
                                        PublicRequest.uploadFile(File(message.data.value.orEmpty()))
                                    }
                                    Log.e("Upload", url.orEmpty())
                                    messageList[i] = MessageUIStatus(
                                        data = Message(
                                            id = message.data.id,
                                            type = type,
                                            value = url.orEmpty(),
                                            inputTime = message.data.inputTime,
                                            second = second,
                                            lockStatus = "0"
                                        )
                                    )
                                    uploadList.add(
                                        ImageMessage(
                                            id = message.data.id,
                                            type = type,
                                            value = url.orEmpty(),
                                            inputTime = message.data.inputTime.orEmpty(),
                                            second = second ?: 0,
                                            lockStatus = message.data.lockStatus ?: "0"
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
                dialog.dismiss()
                uploadMessage(requireContext(), UploadImageToGallery(id = imageId, messages = uploadList), binding.root)
            }
        }
        binding.inputPanel.onTextDone = {
            view.hideKeyboard()
            binding.addMessage.visibility = View.VISIBLE
            binding.parentConstraintLayout.removeView(maskView)
            if (it.isNotEmpty()) {
                messageList.add(
                    MessageUIStatus(
                        data = Message(
                            type = 0,
                            value = it,
                            inputTime = getCurrentDateTime(),
                            lockStatus = "0"
                        ),
                        isLocal = true
                    )
                )
                messageList.sortBy { it.data.inputTime }
                adapter.notifyDataSetChanged()
            }
        }
        binding.inputPanel.startRecordListener = {
            if (checkPermission()) {
                startRecord()
                startTimer()
            }
        }
        binding.inputPanel.onFullScreenEditClick = {
            binding.fullEdit.visibility = View.VISIBLE
            binding.fullEdit.setText(binding.inputPanel.getText())
            binding.fullEdit.requestFocus()
            binding.fullEdit.setOnEditorActionListener { _, actionId, _ ->
                if (actionId == EditorInfo.IME_ACTION_DONE) {
                    binding.fullEdit.visibility = View.GONE
                    binding.inputPanel.setText(binding.fullEdit.text.toString())
                    binding.inputPanel.messageInputRequestFocus()
                    true // 返回 true 表示已经处理了该事件
                } else {
                    false // 返回 false 表示未处理该事件，让系统继续处理
                }
            }
        }
        binding.inputPanel.stopRecordListener = {
            stopTimer()
            stopRecord()
            if (!it) {
                val second = AudioDurationCalculator.getDurationInSeconds(filePath)
                messageList.add(
                    MessageUIStatus(
                        data = Message(
                            type = 1,
                            value = filePath,
                            second = second,
                            inputTime = getCurrentDateTime(),
                            lockStatus = "0"
                        ),
                        isLocal = true
                    )
                )
                messageList.sortBy { it.data.inputTime }
                adapter.notifyDataSetChanged()
            }
        }
        ViewCompat.setOnApplyWindowInsetsListener(view) { _, insets ->
            val imeVisible = insets.isVisible(WindowInsetsCompat.Type.ime())
            val imeHeight = insets.getInsets(WindowInsetsCompat.Type.ime()).bottom
            Log.e("IME", "${imeVisible} ${imeHeight}")
            if (imeVisible) {
                binding.inputPanel.setImeHeight(imeHeight)
            } else {
                binding.inputPanel.setImeHeight(0)
            }
            insets
        }
    }

    private fun startTimer() {
        binding.inputPanel.startTimer()
    }

    private fun stopTimer() {
        binding.inputPanel.stopTimer()

    }

    override fun onDestroy() {
        super.onDestroy()
    }


    private fun startRecord() {
        if (!isRecording) {

            filePath = getOutputFile().absolutePath
            mediaRecorder = Mp3Recorder()
            try {
                mediaRecorder.startRecording(File(filePath))
                isRecording = true
            } catch (e: Exception) {
                filePath = ""
                e.printStackTrace()
                showToast("录音失败")
            }
        }
    }

    private fun getCurrentDateTime(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return dateFormat.format(Date())
    }

    fun checkPermission(): Boolean {
        if (EasyPermissions.hasPermissions(ApplicationContextProvider.context, Manifest.permission.RECORD_AUDIO)) {
            return true
        } else {
            EasyPermissions.requestPermissions(
                PermissionRequest.Builder(requireActivity(), PermissionCode.RECORD_AUDIO, Manifest.permission.RECORD_AUDIO)
                    .setRationale("未开启录音权限，无法使用此功能")
                    .setPositiveButtonText("申请权限")
                    .setNegativeButtonText("取消")
                    .build()
            )
            return false
        }
    }

    private fun stopRecord() {
        if (isRecording) {
            mediaRecorder.stopRecording()
            isRecording = false
        }
    }


    private fun getOutputFile(): File {
        val dir = File(requireContext().cacheDir, "recordings")
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return File(dir, "recording_${System.currentTimeMillis()}.mp3")
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }


    inner class MessageAdapter(val context: Context, var messageList: MutableList<MessageUIStatus>) : RecyclerView.Adapter<MessageAdapter.ViewHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_new_message, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val msg = messageList[position]
            val bubbleLayout = BubbleLayoutBinding.inflate(holder.itemView.context.layoutInflater)
            val bubbleWindow = PopupWindow(
                bubbleLayout.root,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                true
            )
            holder.dateTime.text = msg.data.inputTime
            if (msg.data.type == 0) {
                holder.textItem.visibility = View.VISIBLE
                holder.voiceItem.visibility = View.GONE
                holder.textItem.text = msg.data.value.orEmpty()
                if (msg.data.lockStatus == "0") {
                    bubbleLayout.copy.visibility = View.VISIBLE
                    bubbleLayout.edit.visibility = View.VISIBLE
                    bubbleLayout.delete.visibility = View.VISIBLE
                    bubbleLayout.editPadding.visibility = View.VISIBLE
                    bubbleLayout.deletePadding.visibility = View.VISIBLE
                } else {
                    bubbleLayout.copy.visibility = View.VISIBLE
                    bubbleLayout.edit.visibility = View.GONE
                    bubbleLayout.delete.visibility = View.GONE
                    bubbleLayout.editPadding.visibility = View.GONE
                    bubbleLayout.deletePadding.visibility = View.GONE
                }
            } else {
                holder.textItem.visibility = View.GONE
                holder.voiceItem.visibility = View.VISIBLE
                holder.second.text = "${msg.data.second}s"
                bubbleLayout.copy.visibility = View.GONE
                bubbleLayout.edit.visibility = View.GONE
                bubbleLayout.delete.visibility = View.VISIBLE
                bubbleLayout.editPadding.visibility = View.GONE
                bubbleLayout.deletePadding.visibility = View.GONE

            }
            if (msg.data.lockStatus == "0") {
                holder.lockStatus.setImageDrawable(AppCompatResources.getDrawable(requireContext(), R.drawable.ic_unlock))
            } else {
                holder.lockStatus.setImageDrawable(AppCompatResources.getDrawable(requireContext(), R.drawable.ic_lock))
            }
            holder.lockStatus.setOnClickListener {
                val lock = if (msg.data.lockStatus == "0") "1" else "0"
                viewLifecycleScope.launch(Dispatchers.IO) {
                    try {
                        val request = RetrofitClient.service.updateMessageLock(messageId = msg.data.id, lock = lock)
                        val response = request.awaitResponse()
                        launch(Dispatchers.Main) {
                            if (response.isSuccessful) {
                                if (response.body()?.data != null) {
                                    messageList[position] = messageList[position].copy(data = response.body()?.data!!)
                                    notifyDataSetChanged()
                                }
                            } else {
                                if (response.code() == 401) {
                                    val sp = ApplicationContextProvider.context
                                        .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                                    sp.edit { putString(SpKey.UserCookies, "") }
                                    binding.root.findNavController().navigate("loginFragment")
                                    Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                                }
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        launch(Dispatchers.Main) {
                            Toast.makeText(context, "异常错误", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            }
            bubbleLayout.edit.setOnClickListener {
                bubbleWindow.dismiss()
                val dialog = AddContentDialog(msg.data.value.orEmpty())
                dialog.setOkListener {
                    messageList[position] = MessageUIStatus(
                        data = Message(
                            id = messageList[position].data.id,
                            type = messageList[position].data.type,
                            value = it,
                            inputTime = messageList[position].data.inputTime,
                            second = 0
                        ),
                        isLocal = true
                    )
                    notifyDataSetChanged()
                }
                dialog.setCancelListener { }
                dialog.show(requireActivity().supportFragmentManager, "AddContentDialog")
            }

            bubbleLayout.copy.setOnClickListener {
                val clipboardManager = requireContext().getSystemService<ClipboardManager>()
                // 创建 ClipData 对象，并设置要复制的文本内容
                val clipData = ClipData.newPlainText("text", msg.data.value)
                // 将 ClipData 对象放入剪贴板
                clipboardManager?.setPrimaryClip(clipData)
                Toast.makeText(requireContext(), "已复制", Toast.LENGTH_SHORT).show()
                bubbleWindow.dismiss()
            }
            if (msg.isLocal) {
                bubbleLayout.deleteText.text = "删除"
                bubbleLayout.delete.setOnClickListener {
                    messageList.removeAt(position)
                    bubbleWindow.dismiss()
                    notifyDataSetChanged()
                }
                holder.lockStatus.visibility = View.GONE
                holder.second.setTextColor(Color.WHITE)
                holder.textItem.setTextColor(Color.WHITE)
                holder.messageItem.background = context.getDrawable(R.drawable.message_item_bg_grey)
            } else {
                bubbleLayout.deleteText.text = "撤回"
                bubbleLayout.delete.setOnClickListener {
                    lifecycleScope.launch {
                        deleteMessage(context, messageList[position].data.id.orEmpty(), binding.root)
                    }
                    bubbleWindow.dismiss()
                    messageList.removeAt(position)
                    notifyDataSetChanged()
                }
                holder.lockStatus.visibility = View.VISIBLE
                holder.second.setTextColor(context.getColor(R.color.text33))
                holder.textItem.setTextColor(context.getColor(R.color.text33))
                holder.messageItem.background = context.getDrawable(R.drawable.message_item_bg_green)
            }
            when (msg.status) {
                0 -> {
                    holder.stop.visibility = View.GONE
                    holder.play.visibility = View.VISIBLE
                    holder.progress.visibility = View.GONE
                }

                1 -> {
                    holder.stop.visibility = View.GONE
                    holder.play.visibility = View.GONE
                    holder.progress.visibility = View.VISIBLE
                }

                2 -> {
                    holder.stop.visibility = View.VISIBLE
                    holder.play.visibility = View.GONE
                    holder.progress.visibility = View.GONE
                }
            }
            holder.play.setOnClickListener {
                if (msg.data.type == 1) {
                    if (AudioPlayer.getInstance(requireContext()).isPlaying()) {
                        AudioPlayer.getInstance(requireContext()).stop()
                        messageList.forEach { it.status = 0 }
                        notifyDataSetChanged()
                    }
                    msg.status = 1
                    notifyDataSetChanged()
                    if (msg.data.value.orEmpty().isHttpUrl()) {
                        lifecycleScope.launch(Dispatchers.IO) {
                            val bytes = msg.data.value?.let { it1 -> DownloadUtils.downloadFile(it1) }
                            if (bytes != null) {
                                val file = saveBytesToCache(bytes, UUID.randomUUID().toString(), requireContext())
                                launch(Dispatchers.Main) {
                                    msg.status = 2
                                    notifyDataSetChanged()
                                    AudioPlayer.getInstance(requireContext()).play(file?.path.orEmpty()) {
                                        msg.status = 0
                                        notifyDataSetChanged()
                                    }
                                }
                            }
                        }
                    } else {
                        msg.status = 2
                        notifyDataSetChanged()
                        AudioPlayer.getInstance(requireContext()).play(msg.data.value.orEmpty()) {
                            msg.status = 0
                            notifyDataSetChanged()
                        }
                    }
                }
            }
            holder.stop.setOnClickListener {
                AudioPlayer.getInstance(requireContext()).stop()
                msg.status = 0
                notifyDataSetChanged()
            }
            holder.itemView.setOnLongClickListener {
                if (msg.data.type == 1 && msg.data.lockStatus == "1") {
                    return@setOnLongClickListener false
                }
                // 创建气泡布局并显示
                val screenHeight = Point().apply {
                    requireActivity().windowManager.defaultDisplay.getSize(this)
                }.y

                val itemHeight = binding.messageList.getChildAt(0)?.height ?: 0
                val itemY = itemHeight * position

                val contentView = bubbleWindow.contentView
                val bubbleHeight = contentView.height

                val bubbleY: Int
                if (itemY < screenHeight / 2) {
                    // 如果条目在屏幕上半部分，气泡显示在条目下方
                    bubbleY = 4.dp
                    bubbleLayout.constraintLayout.background = AppCompatResources.getDrawable(requireContext(), R.drawable.bubble)
                    bubbleLayout.constraintLayout.setPadding(20.dp, 24.dp, 20.dp, 20.dp)
                } else {
                    // 如果条目在屏幕下半部分，气泡显示在条目上方
                    bubbleY = -(itemHeight + 44.dp)
                    bubbleLayout.constraintLayout.background = AppCompatResources.getDrawable(requireContext(), R.drawable.bubble_down)
                    bubbleLayout.constraintLayout.setPadding(20.dp, 20.dp, 20.dp, 24.dp)
                }

                bubbleWindow.showAsDropDown(holder.itemView, 0, bubbleY)
                // 返回 true 表示已处理长按事件
                true
            }
        }

        override fun getItemCount(): Int {
            return messageList.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val messageItem: LinearLayout = itemView.findViewById(R.id.messageItem)
            val voiceItem: LinearLayout = itemView.findViewById(R.id.voiceItem)
            val textItem: TextView = itemView.findViewById(R.id.textItem)
            val play: ImageView = itemView.findViewById(R.id.play)
            val stop: ImageView = itemView.findViewById(R.id.stop)
            val progress: ProgressBar = itemView.findViewById(R.id.progress)
            val second: TextView = itemView.findViewById(R.id.second)
            val dateTime: TextView = itemView.findViewById(R.id.dateTime)
            val lockStatus: ImageView = itemView.findViewById(R.id.lockStatus)
        }
    }

    fun saveBytesToCache(bytes: ByteArray, fileName: String, context: Context): File? {
        val cacheDir = context.cacheDir
        val file = File(cacheDir, fileName)

        try {
            FileOutputStream(file).use { outputStream ->
                outputStream.write(bytes)
            }
            return file
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
    }

    override fun onPause() {
        super.onPause()
        AudioPlayer.getInstance(requireContext()).stop()

    }

    override fun onResume() {
        super.onResume()
    }

    fun uploadMessage(context: Context, data: UploadImageToGallery, root: View) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在同步云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                for (message in data.messages) {
                    val request = if (message.id != null && message.lockStatus != "1") {
                        RetrofitClient.service.updateMessageInfo(
                            MessageUpdate(
                                id = message.id,
                                value = message.value,
                                type = message.type.toString(),
                                second = message.second,
                                lockStatus = message.lockStatus,
                                inputTime = message.inputTime,
                            )
                        )
                    } else if (message.id == null) {
                        RetrofitClient.service.insertPhotoMessageInfo(
                            MessageUpdate(
                                value = message.value,
                                type = message.type.toString(),
                                second = message.second,
                                primaryId = imageId,
                                inputTime = message.inputTime,
                            )
                        )
                    } else continue
                    val response = request.awaitResponse()
                    launch(Dispatchers.Main) {
                        if (response.isSuccessful) {
                            val body = response.body()
                            if (body?.data != null && body.ok) {

//                                Toast.makeText(context,"同步成功", Toast.LENGTH_SHORT).show()
                            } else {
                                Toast.makeText(context, body?.msg ?: "同步失败 请重新上传", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            if (response.code() == 401) {
                                throw HttpException(response)
                            }
                        }
                    }
                }
                getImageDetail(requireContext(), imageId, binding.root)
            } catch (e: Exception) {
                if (e is HttpException) {
                    val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                    sp.edit { putString(SpKey.UserCookies, "") }
                    root.findNavController().navigate("loginFragment")
                    Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                } else {
                    e.printStackTrace()
                    launch(Dispatchers.Main) {
                        Toast.makeText(context, "网络连接失败 请重新上传", Toast.LENGTH_SHORT).show()
                    }
                }
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }

    fun deleteMessage(context: Context, id: String, root: View) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在同步云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.deleteMessageInfo(id)
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {

//                                Toast.makeText(context,"同步成功", Toast.LENGTH_SHORT).show()
                        } else {
                            Toast.makeText(context, "同步失败 请重新上传", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                launch(Dispatchers.Main) {
                    Toast.makeText(context, "网络连接失败 请重新上传", Toast.LENGTH_SHORT).show()
                }
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }
}
