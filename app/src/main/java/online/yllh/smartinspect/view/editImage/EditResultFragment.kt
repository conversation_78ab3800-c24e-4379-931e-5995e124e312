package online.yllh.smartinspect.view.editImage

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.Toast
import androidx.core.content.edit
import androidx.core.net.toUri
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.google.gson.Gson
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import online.yllh.smartinspect.AppDatabase
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentEditResultBinding
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.dialog.ProjectUploadPhotoDialog
import online.yllh.smartinspect.dialog.UploadPhotoDialog
import online.yllh.smartinspect.entity.Photo
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.isNetworkAvailable
import online.yllh.smartinspect.network.PublicRequest
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.UploadImageToGallery
import online.yllh.smartinspect.network.model.response.CloudAlbumModel
import online.yllh.smartinspect.network.model.response.Project
import online.yllh.smartinspect.provider.ApplicationContextProvider
import retrofit2.awaitResponse
import java.io.File
import java.io.IOException
import java.io.InputStream
import kotlin.math.roundToInt


class EditResultFragment : BaseFragment() {
    private val imageUri: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.ImageUri)
            ?: throw IllegalArgumentException("imageInfo is null")
    }
    private val imagePath: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.ImageCachePath)
            ?: throw IllegalArgumentException("imageInfo is null")
    }
    private lateinit var binding: FragmentEditResultBinding
    private var isUploading = false // 标记是否正在上传中
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentEditResultBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_HIDE_BAR)
            .init()

        binding.upload.visibility =
            if (requireContext().isNetworkAvailable()) View.VISIBLE else View.GONE
        var bitmap: Bitmap?
        var inputStream: InputStream? = null
        try {
            inputStream = requireContext().contentResolver.openInputStream(imageUri.toUri())
            bitmap = BitmapFactory.decodeStream(inputStream)
        } catch (e: IOException) {
            bitmap = null
            e.printStackTrace()
        } finally {
            inputStream?.close()
        }
        loadImageWithRotation(requireContext(), bitmap!!, binding.mainImage, 0f)

        binding.back.setOnClickListener {
            binding.root.findNavController().popBackStack()
        }
        binding.upload.setOnClickListener {
            if (!isUploading) {
                isUploading = true
                binding.upload.isEnabled = false
                getCloudGallery(requireContext())
            }
        }
        lifecycleScope.launch(Dispatchers.IO) {
            AppDatabase.get().photoDao().insert(
                Photo(
                    uri = imageUri,
                    cachePath = imagePath,
                    upload = false,
                    shotTime = System.currentTimeMillis(),
                    longitude = 0.0,
                    latitude = 0.0,
                )
            )
        }
    }

    fun getCloudGallery(context: Context) {
        if (!context.isNetworkAvailable()) {
            binding.upload.visibility = View.GONE
            return
        }
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在同步云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.getAlbumInfoByUserInfo()
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            showUploadDialog(body.data)
                        } else {
                            Toast.makeText(context, "同步失败 ${body?.msg}", Toast.LENGTH_SHORT)
                                .show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            binding.root.findNavController().navigate("loginFragment")
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                launch(Dispatchers.Main) {
                    resetUploadButton()
                    dialog.dismiss()
                }
            }
        }
    }

    private fun resetUploadButton() {
        isUploading = false
        binding.upload.isEnabled = true
    }

    private fun showUploadDialog(data: List<CloudAlbumModel>) {
        val dialog = LoadingDialog(requireContext())
        dialog.setTitle("上传中")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        val actionProjectJson = sp.getString(SpKey.ActionProject, "").orEmpty()
        if (actionProjectJson.isNotEmpty()) {
            val actionProject = Gson().fromJson(actionProjectJson, Project::class.java)
            if (actionProject.albumVOS == null) {
                Toast.makeText(requireContext(), "该项目没有相册", Toast.LENGTH_SHORT).show()
                return
            }
            ProjectUploadPhotoDialog(
                requireContext(),
                actionProject.albumVOS.first().palbumVOS!!
            ).apply {
                setOkButtonListener {
                    dialog.show()
                    lifecycleScope.launch(Dispatchers.IO) {
                        val url = PublicRequest.uploadFile(File(imagePath))
                        launch(Dispatchers.Main) {
                            if (url != null && it != null) {
                                uploadImage(url, it)
                            } else {
                                Toast.makeText(context, "上传失败", Toast.LENGTH_SHORT).show()
                            }
                            dialog.dismiss()
                        }
                    }
                }
                show()
            }
        } else {
            UploadPhotoDialog(requireContext(), data).apply {
                setOkButtonListener {
                    dialog.show()
                    lifecycleScope.launch(Dispatchers.IO) {
                        val url = PublicRequest.uploadFile(File(imagePath))
                        launch(Dispatchers.Main) {
                            if (url != null && it != null) {
                                uploadImage(url, it)
                            } else {
                                Toast.makeText(context, "上传失败", Toast.LENGTH_SHORT).show()
                            }
                            dialog.dismiss()
                        }
                    }
                }
                show()
            }
        }
    }

    fun uploadImage(imageUrl: String, albumId: String) {
        val dialog = LoadingDialog(requireContext())
        dialog.setTitle("正在同步云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.saveImageToCloudAlbum(
                    UploadImageToGallery(photoAddress = imageUrl, albumId = albumId)
                )
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            withContext(Dispatchers.IO) {
                                val photoDao = AppDatabase.get().photoDao()
                                val photo = photoDao.getPhotoByUri(imageUri)
                                if (photo != null) {
                                    photo.upload = true
                                    photoDao.update(photo)
                                }
                            }
                            Toast.makeText(context, "同步成功", Toast.LENGTH_SHORT).show()
                            delay(500)
                            binding.root.findNavController().popBackStack()
                        } else {
                            Toast.makeText(
                                context,
                                body?.msg ?: "同步失败 请重新上传",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            binding.root.findNavController().navigate("loginFragment")
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                launch(Dispatchers.Main) {
                    Toast.makeText(context, "网络连接失败 请重新上传", Toast.LENGTH_SHORT).show()
                }
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }

    fun loadImageWithRotation(
        context: Context,
        bitmap: Bitmap,
        targetImageView: ImageView,
        degrees: Float
    ) {
        val matrix = Matrix()
        matrix.postRotate(degrees)
        val newBitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
        if (degrees == 0f || degrees == 180f) {
            binding.mainImage.layoutParams.width = 283.dp
            binding.mainImage.layoutParams.height =
                ((bitmap.height / bitmap.width.toFloat()) * binding.mainImage.layoutParams.width).roundToInt()
            binding.mainImage.requestLayout()
        } else {
            binding.mainImage.layoutParams.width = 283.dp
            binding.mainImage.layoutParams.height =
                ((bitmap.width / bitmap.height.toFloat()) * binding.mainImage.layoutParams.width).roundToInt()
            binding.mainImage.requestLayout()
        }
        Glide.with(context)
            .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
            .load(newBitmap)
            .placeholder(R.drawable.placeholder)
            .into(targetImageView)
    }
}
