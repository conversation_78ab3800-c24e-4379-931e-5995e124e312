package online.yllh.smartinspect.view.snapshotResult

import android.content.Context
import android.graphics.Bitmap
import android.graphics.SurfaceTexture
import androidx.lifecycle.ViewModel
import com.tapapk.edit_engine.core.EditEngineCore
import com.tapapk.edit_engine.`interface`.GLCreateListener
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.model.Location
import online.yllh.smartinspect.model.WatermarkState
import online.yllh.smartinspect.provider.ApplicationContextProvider
import online.yllh.smartinspect.sticker.EmptySticker
import online.yllh.smartinspect.uiwidget.sticker.StickerGalleryFragment
import online.yllh.smartinspect.uiwidget.sticker.StickerPanel
import online.yllh.smartinspect.utils.LocationHelper
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class SnapshotViewModel : ViewModel() {
    val editEngineCore = EditEngineCore()
    val previewTextureIsReady = MutableStateFlow(false)
    var editEnvironmentIsInit = false
    val isShowWatermark = MutableStateFlow(false)
    val currentSticker = MutableStateFlow("")
    var workStickerAdapter: StickerGalleryFragment.StickerAdapter? =
        null //= StickerGalleryFragment.StickerAdapter(StickerManager.stickerList,selectListener,currentSticker)
    var recentStickerAdapter: StickerGalleryFragment.StickerAdapter? = null //= StickerGalleryFragment.StickerAdapter(StickerManager,selectListener,currentSticker)
    var stickerPanelAdapter: StickerPanel.StickerPanelAdapter? = null
    val selectListener: (String) -> Unit = {
        currentSticker.value = it
    }

    var poiName: String = ""
    val location = LocationHelper.location
    val watermarkState = MutableStateFlow(WatermarkState())

    fun setCurrentSticker(sticker: String) {
        workStickerAdapter?.currentSticker = sticker
        recentStickerAdapter?.currentSticker = sticker
        workStickerAdapter?.notifyDataSetChanged()
        recentStickerAdapter?.notifyDataSetChanged()
    }

    suspend fun initEditEnvironment(context: Context, surface: SurfaceTexture, width: Int, height: Int, byteArray: ByteArray): Boolean {
        if (editEnvironmentIsInit) {
            return true
        }
        return suspendCoroutine {
            editEngineCore.create(
                context = context,
                surface = surface,
                width = width,
                height = height,
                glCreateListener = object : GLCreateListener {
                    override fun success() {
                        editEnvironmentIsInit = true
                        setMainTexture(byteArray)
                        setSticker(EmptySticker().createBitmap(width, height))
                        it.resume(true)
                    }

                    override fun error() {
                        editEnvironmentIsInit = false
                        it.resume(false)
                    }
                }
            )
        }
    }

    fun setMainTexture(byteArray: ByteArray) {
        editEngineCore.setMainTexture(byteArray)
    }

    fun surfaceSizeChange(width: Int, height: Int) {
//        editEngineCore.changeCameraViewSize(width,height)
    }

    fun setSurfaceTextureAvailable(available: Boolean) {
        previewTextureIsReady.update { available }
    }

    fun setSticker(bitmap: Bitmap) {
        editEngineCore.setSticker(bitmap)
    }

    fun release() {
        editEngineCore.release()
        editEnvironmentIsInit = false
    }

    fun getWatermarkAddress(poiName: String, location: Location = this.location.value): String {
        val sp = ApplicationContextProvider.context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        var address = if (sp.getBoolean(SpKey.EnableLevel, true)) {
            val addressLevel = getAddressLevel(ApplicationContextProvider.context, location)
            "${addressLevel}·${poiName}"
        } else poiName
        address = address.trim().let { if (it == "·") "" else it }
        return address
    }

    fun updateWatermarkAddress(poiName: String) {
        this.poiName = poiName
        watermarkState.value = watermarkState.value.copy(address = getWatermarkAddress(poiName))
    }

    fun refreshLocationLevel() {
        updateWatermarkAddress(poiName)
    }

    private fun getAddressLevel(context: Context, location: Location = this.location.value): String {
        val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        return when (sp.getString(SpKey.LocationLevel, "city")) {
            "country" -> location.country
            "province" -> location.province
            "city" -> location.city
            "district" -> location.district
            "street" -> location.street
            "town" -> location.town
            "none" -> ""
            else -> ""
        }
    }
}
