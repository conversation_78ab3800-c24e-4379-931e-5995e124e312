package online.yllh.smartinspect.view.gallery.cloud

import android.content.Context
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.core.content.edit
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.findNavController
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.CreateProjectAlbumModel
import online.yllh.smartinspect.network.model.request.DeletePhotos
import online.yllh.smartinspect.network.model.request.MovePhotos
import online.yllh.smartinspect.network.model.response.CloudAlbumModel
import online.yllh.smartinspect.network.model.response.CloudPhotoAlbumModel
import online.yllh.smartinspect.provider.ApplicationContextProvider
import retrofit2.awaitResponse

class CloudPhotoAlbumViewModel : ViewModel() {
    val photos = MutableStateFlow<List<CloudPhotoAlbumModel>>(emptyList())
    val albums = MutableStateFlow<List<CloudAlbumModel>>(emptyList())
    fun getPhotosByAlbum(context: Context, albumId: String, root: View) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.getPhotosByAlbum(albumId)
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        val data = body?.data
                        if (data != null && body.ok) {
                            photos.value = data
                        } else {
                            Toast.makeText(context, "同步失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                launch(Dispatchers.Main) {}
            }
        }
    }

    fun addProjectGallery(context: Context, projectId: String, parentAlbumId: String, albumName: String, root: View) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在同步云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.savePAlbum(CreateProjectAlbumModel(projectId, parentAlbumId, albumName))
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            root.findNavController().popBackStack()
                            root.findNavController().navigate(
                                R.id.cloudAlbumFragment,
                                Bundle().apply {
                                    this.putSerializable(FragmentArguments.ParentAlbumId, parentAlbumId)
                                    this.putSerializable(FragmentArguments.AlbumName, albumName.orEmpty())
                                    this.putSerializable(FragmentArguments.IsImportMode, false)
                                    this.putSerializable(FragmentArguments.ProjectId, projectId)
                                })
                            Toast.makeText(context, "同步成功", Toast.LENGTH_SHORT).show()
                        } else {
                            Toast.makeText(context, "同步失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }

    fun deletePhoto(context: Context, albumId: String, photoId: String, root: View, onSuccess: () -> Unit) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在删除照片")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val deletePhotos = DeletePhotos(albumId, listOf(photoId))
                val request = RetrofitClient.service.albumDelPhoto(deletePhotos)
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            // 从本地列表中移除已删除的照片
                            val currentPhotos = photos.value.toMutableList()
                            currentPhotos.removeAll { it.id == photoId }
                            photos.value = currentPhotos
                            Toast.makeText(context, "删除成功", Toast.LENGTH_SHORT).show()
                            onSuccess()
                        } else {
                            Toast.makeText(context, "删除失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        } else {
                            Toast.makeText(context, "删除失败", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                launch(Dispatchers.Main) {
                    Toast.makeText(context, "删除失败", Toast.LENGTH_SHORT).show()
                }
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }

    fun movePhoto(context: Context, oldAlbumId: String, newAlbumId: String, photoId: String, root: View, onSuccess: () -> Unit) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在移动照片")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val movePhotos = MovePhotos(oldAlbumId.toInt(), newAlbumId.toInt(), listOf(photoId.toInt()))
                val request = RetrofitClient.service.albumMovePhoto(movePhotos)
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            // 从本地列表中移除已移动的照片
                            val currentPhotos = photos.value.toMutableList()
                            currentPhotos.removeAll { it.id == photoId }
                            photos.value = currentPhotos
                            Toast.makeText(context, "移动成功", Toast.LENGTH_SHORT).show()
                            onSuccess()
                        } else {
                            Toast.makeText(context, "移动失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        } else {
                            Toast.makeText(context, "移动失败", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                launch(Dispatchers.Main) {
                    Toast.makeText(context, "移动失败", Toast.LENGTH_SHORT).show()
                }
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }

    fun getAllAlbums(context: Context, root: View) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在获取相册列表")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.getAlbumInfos()
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            albums.value = body.data
                        } else {
                            Toast.makeText(context, "获取相册列表失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        } else {
                            Toast.makeText(context, "获取相册列表失败", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                launch(Dispatchers.Main) {
                    Toast.makeText(context, "获取相册列表失败", Toast.LENGTH_SHORT).show()
                }
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }

}
