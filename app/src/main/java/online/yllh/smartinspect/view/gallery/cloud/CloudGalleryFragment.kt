package online.yllh.smartinspect.view.gallery.cloud

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.navigation.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.databinding.FragmentCloudGalleryBinding
import online.yllh.smartinspect.dialog.CreateGalleryDialog
import online.yllh.smartinspect.dialog.TipsDialog
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.model.AlbumSectionItem
import online.yllh.smartinspect.network.model.response.CloudAlbumModel

class CloudGalleryFragment(
    private val isImportModel: Boolean = false,
    private val importModeAlbumId: String? = null,
) : BaseFragment() {
    private lateinit var binding: FragmentCloudGalleryBinding
    private val viewModel by lazy { CloudGalleryViewModel() }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCloudGalleryBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.getCloudGallery(requireContext(), binding.root)
        binding.addGallery.setOnClickListener {
            val dialog = CreateGalleryDialog(requireContext())
            dialog.setCancelable(false)
            dialog.setOkButtonListener {
                viewModel.createGallery(requireContext(), it, binding.root)
            }
            dialog.show()
        }
        val adapter = AlbumAdapter(emptyList())
        binding.photoRecyclerView.adapter = adapter
        val layoutManager = GridLayoutManager(requireContext(), 3)
        layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return when (adapter.getItemViewType(position)) {
                    adapter.VIEW_TYPE_HEADER -> 3 // 标题占满整行
                    else -> 1 // 相册项占1/3行
                }
            }
        }
        binding.photoRecyclerView.layoutManager = layoutManager
        observeOn(viewModel.albums) {
            adapter.sectionItems = viewModel.createSectionItems(it)
            adapter.notifyDataSetChanged()
        }
    }


    inner class AlbumAdapter(var sectionItems: List<AlbumSectionItem>) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

        val VIEW_TYPE_HEADER = 0
        val VIEW_TYPE_ALBUM = 1

        override fun getItemViewType(position: Int): Int {
            return when (sectionItems[position]) {
                is AlbumSectionItem.Header -> VIEW_TYPE_HEADER
                is AlbumSectionItem.Album -> VIEW_TYPE_ALBUM
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            return when (viewType) {
                VIEW_TYPE_HEADER -> {
                    val view = parent.context.layoutInflater.inflate(R.layout.item_album_section_header, parent, false)
                    HeaderViewHolder(view)
                }
                VIEW_TYPE_ALBUM -> {
                    val view = parent.context.layoutInflater.inflate(R.layout.item_album, parent, false)
                    AlbumViewHolder(view)
                }
                else -> throw IllegalArgumentException("Unknown view type: $viewType")
            }
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            when (val item = sectionItems[position]) {
                is AlbumSectionItem.Header -> {
                    (holder as HeaderViewHolder).bind(item)
                }
                is AlbumSectionItem.Album -> {
                    (holder as AlbumViewHolder).bind(item.album)
                }
            }
        }

        override fun getItemCount(): Int {
            return sectionItems.size
        }

        inner class HeaderViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            private val sectionTitle: TextView = itemView.findViewById(R.id.sectionTitle)

            fun bind(header: AlbumSectionItem.Header) {
                sectionTitle.text = header.title
            }
        }

        inner class AlbumViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val photoImageView: ImageView = itemView.findViewById(R.id.imageView)
            val title: TextView = itemView.findViewById(R.id.itemTitle)
            val subTitle: TextView = itemView.findViewById(R.id.subTitle)
            val delete: FrameLayout = itemView.findViewById(R.id.delete)

            fun bind(photoUri: CloudAlbumModel) {
                val context = itemView.context
                val displayMetrics = context.resources.displayMetrics
                val screenWidth = displayMetrics.widthPixels
                val imageWidth = (screenWidth - 18.dp) / 3
                photoImageView.layoutParams.width = imageWidth
                photoImageView.layoutParams.height = imageWidth
                val requestOptions = RequestOptions()
                    .format(DecodeFormat.PREFER_RGB_565)
                    .override(imageWidth, imageWidth)
                Glide.with(itemView.context)
                    .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                    .load(photoUri.albumCover)
                    .placeholder(R.drawable.placeholder)
                    .apply(requestOptions)
                    .into(photoImageView)
                title.text = photoUri.albumName
                subTitle.text = ""

                itemView.setOnClickListener {
                    if (photoUri.palbumVOS == null) {
                        binding.root.findNavController().navigate(R.id.action_galleryFragment_to_cloudPhotoAlbumFragment, Bundle().apply {
                            this.putSerializable(FragmentArguments.AlbumId, photoUri.id)
                            this.putSerializable(FragmentArguments.AlbumName, photoUri.albumName)
                            this.putSerializable(FragmentArguments.IsImportMode, isImportModel)
                            this.putSerializable(FragmentArguments.ImportModeAlbumId, importModeAlbumId)
                            if (photoUri.isProjectAlbum == "1") {
                                this.putSerializable(FragmentArguments.ProjectId, photoUri.projectId)
                            }
                        })
                    } else {
                        binding.root.findNavController().navigate(R.id.cloudAlbumFragment, Bundle().apply {
                            this.putSerializable(FragmentArguments.ParentAlbumId, photoUri.id)
                            this.putSerializable(FragmentArguments.AlbumName, photoUri.albumName)
                            this.putSerializable(FragmentArguments.IsImportMode, isImportModel)
                            this.putSerializable(FragmentArguments.ImportModeAlbumId, importModeAlbumId)
                            if (photoUri.isProjectAlbum == "1") {
                                this.putSerializable(FragmentArguments.ProjectId, photoUri.projectId)
                            }
                        })
                    }
                }

                if (photoUri.isProjectAlbum == "1") {
                    delete.visibility = View.GONE
                } else if (photoUri.isProjectAlbum == "0") {
                    delete.visibility = View.VISIBLE
                }

                delete.setOnClickListener {
                    val dialog = TipsDialog(requireContext())
                    dialog.setTitle("确定要删除此相册吗？删除操作不可还原")
                    dialog.setCancelListener { }
                    dialog.setOkListener {
                        viewModel.deleteGallery(requireContext(), photoUri.id.orEmpty(), binding.root)
                        viewModel.getCloudGallery(requireContext(), binding.root)
                    }
                    dialog.show()
                }
            }
        }
    }
}
