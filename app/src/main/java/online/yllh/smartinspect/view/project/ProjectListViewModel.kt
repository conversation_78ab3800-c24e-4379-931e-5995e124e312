package online.yllh.smartinspect.view.project

import android.content.Context
import android.view.View
import android.widget.Toast
import androidx.core.content.edit
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.findNavController
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.extension.isNetworkAvailable
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.response.Project
import online.yllh.smartinspect.provider.ApplicationContextProvider
import retrofit2.awaitResponse

class ProjectListViewModel : ViewModel() {
    val projectList = MutableStateFlow<List<Project>>(emptyList())

    fun getProjectList(
        context: Context,
        root: View,
        status: String? = null,
        initiatorName: String? = null,
        name: String? = null,
        createEndTime: String? = null,
        createBeginTime: String? = null,
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)

            // 检查网络状态
            if (context.isNetworkAvailable()) {
                // 有网络时，尝试从网络获取数据
                try {
                    val request = RetrofitClient.service.getProjectList(
                        status = status,
                        initiatorName = initiatorName,
                        name = name,
                        createEndTime = createEndTime,
                        createBeginTime = createBeginTime
                    )
                    val response = request.awaitResponse()
                    launch(Dispatchers.Main) {
                        if (response.isSuccessful) {
                            val body = response.body()
                            if (body?.data != null && body.ok) {
                                val projects = body.data
                                projectList.value = projects
                                // 缓存数据到本地
                                saveProjectListToCache(sp, projects)
                            } else {
                                // 网络请求失败，尝试读取缓存
                                loadProjectListFromCache(sp, context)
                                Toast.makeText(context, "获取列表失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            if (response.code() == 401) {
                                sp.edit { putString(SpKey.UserCookies, "") }
                                root.findNavController().navigate("loginFragment")
                                Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                            } else {
                                // 网络请求失败，尝试读取缓存
                                loadProjectListFromCache(sp, context)
                            }
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    // 网络异常，尝试读取缓存
                    launch(Dispatchers.Main) {
                        loadProjectListFromCache(sp, context)
                    }
                }
            } else {
                // 无网络时，直接读取缓存
                launch(Dispatchers.Main) {
                    loadProjectListFromCache(sp, context)
                }
            }
        }
    }

    /**
     * 保存项目列表到缓存
     */
    private fun saveProjectListToCache(sp: android.content.SharedPreferences, projects: List<Project>) {
        try {
            val gson = Gson()
            val projectsJson = gson.toJson(projects)
            val currentTime = System.currentTimeMillis()

            sp.edit {
                putString(SpKey.ProjectListCache, projectsJson)
                putLong(SpKey.ProjectListCacheTime, currentTime)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 从缓存读取项目列表
     */
    private fun loadProjectListFromCache(sp: android.content.SharedPreferences, context: Context) {
        try {
            val cachedJson = sp.getString(SpKey.ProjectListCache, null)
            if (!cachedJson.isNullOrEmpty()) {
                val gson = Gson()
                val type = object : TypeToken<List<Project>>() {}.type
                val cachedProjects: List<Project> = gson.fromJson(cachedJson, type)
                projectList.value = cachedProjects
                if (!context.isNetworkAvailable()) {
                    Toast.makeText(context, "网络不可用，显示离线数据", Toast.LENGTH_SHORT).show()
                }
            } else {
                projectList.value = emptyList()
                if (!context.isNetworkAvailable()) {
                    Toast.makeText(context, "网络不可用且无离线数据", Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            projectList.value = emptyList()
            if (!context.isNetworkAvailable()) {
                Toast.makeText(context, "读取离线数据失败", Toast.LENGTH_SHORT).show()
            }
        }
    }
}
