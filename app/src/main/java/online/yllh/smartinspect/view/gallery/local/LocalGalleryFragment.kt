package online.yllh.smartinspect.view.gallery.local

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.databinding.FragmentLocalGalleryBinding

class LocalGalleryFragment(
    private val initPhotoScrollY: Int,
    private val initAlbumScrollY: Int,
    private val albumScrollListener: (Int) -> Unit,
    private val photoScrollListener: (Int) -> Unit,
    private val isImportModel: Boolean = false,
    private val importModeAlbumId: String? = null,
) : BaseFragment() {
    private lateinit var binding: FragmentLocalGalleryBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentLocalGalleryBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.viewPager.adapter = LocalGalleryAdapter(childFragmentManager)
        binding.tabLayout.setupWithViewPager(binding.viewPager)
        binding.tabLayout.getTabAt(0)?.text = "相册"
        binding.tabLayout.getTabAt(1)?.text = "相册夹"
    }

    inner class LocalGalleryAdapter(fragmentManager: FragmentManager) : FragmentStatePagerAdapter(fragmentManager, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {
        override fun getCount() = 2
        override fun getItem(position: Int) = when (position) {
            0 -> PhotoFragment(initPhotoScrollY, photoScrollListener, isImportModel, importModeAlbumId)
            1 -> AlbumFragment(initAlbumScrollY, albumScrollListener, isImportModel, importModeAlbumId)
            else -> PhotoFragment(initPhotoScrollY, photoScrollListener, isImportModel, importModeAlbumId)
        }
    }
}
