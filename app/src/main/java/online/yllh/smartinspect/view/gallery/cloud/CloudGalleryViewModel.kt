package online.yllh.smartinspect.view.gallery.cloud

import android.content.Context
import android.view.View
import android.widget.Toast
import androidx.core.content.edit
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.findNavController
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.CreateCloudAlbumModel
import online.yllh.smartinspect.model.AlbumSectionItem
import online.yllh.smartinspect.network.model.response.CloudAlbumModel
import online.yllh.smartinspect.provider.ApplicationContextProvider
import retrofit2.awaitResponse

class CloudGalleryViewModel : ViewModel() {
    val albums = MutableStateFlow<List<CloudAlbumModel>>(emptyList())

    fun createSectionItems(albums: List<CloudAlbumModel>): List<AlbumSectionItem> {
        val sectionItems = mutableListOf<AlbumSectionItem>()

        // 分离项目相册和非项目相册
        val projectAlbums = albums.filter { it.isProjectAlbum == "1" }
        val nonProjectAlbums = albums.filter { it.isProjectAlbum != "1" }

        // 添加项目相册部分
        if (projectAlbums.isNotEmpty()) {
            sectionItems.add(AlbumSectionItem.Header("项目相册"))
            projectAlbums.forEach { album ->
                sectionItems.add(AlbumSectionItem.Album(album))
            }
        }

        // 添加非项目相册部分
        if (nonProjectAlbums.isNotEmpty()) {
            sectionItems.add(AlbumSectionItem.Header("非项目相册"))
            nonProjectAlbums.forEach { album ->
                sectionItems.add(AlbumSectionItem.Album(album))
            }
        }

        return sectionItems
    }
    fun createGallery(context: Context, albumName: String, root: View) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在创建云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.createCloudAlbum(CreateCloudAlbumModel(albumName))
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            getCloudGallery(context, root)
                        } else {
                            Toast.makeText(context, "创建失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                launch(Dispatchers.Main) { dialog.dismiss() }
            }
        }
    }

    fun getCloudGallery(context: Context, root: View) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在同步云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.getAlbumInfos()
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            albums.value = body.data
                        } else {
                            Toast.makeText(context, "用户信息获取失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }

    fun deleteGallery(context: Context, albumId: String, root: View) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("正在删除")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.deleteAlbum(albumId)
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            getCloudGallery(context, root)
                        } else {
                            Toast.makeText(context, "删除失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                launch(Dispatchers.Main) { dialog.dismiss() }
            }
        }
    }

}
