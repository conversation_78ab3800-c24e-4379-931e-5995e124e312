package online.yllh.smartinspect.view.setting

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.databinding.FragmentCacheBinding
import online.yllh.smartinspect.dialog.ClearCacheDialog
import online.yllh.smartinspect.dialog.ClearDoneDialog
import java.io.File
import kotlin.math.roundToInt

class CacheFragment : BaseFragment() {
    private lateinit var binding: FragmentCacheBinding


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCacheBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        binding.cacheSize.visibility = View.GONE
        binding.progress.visibility = View.VISIBLE
        binding.clearCache.setOnClickListener {
            val clearDialog = ClearCacheDialog(requireContext())
            clearDialog.setCancelListener { }
            clearDialog.setOkListener {
                lifecycleScope.launch(Dispatchers.IO) {
                    clearCache()
                    launch(Dispatchers.Main) {
                        val doneDialog = ClearDoneDialog(requireContext())
                        doneDialog.setCacheText("共释放了${binding.cacheSize.text}空间")
                        doneDialog.setOkListener {
                            binding.root.findNavController().popBackStack()
                        }
                        doneDialog.show()
                    }
                }
            }
            clearDialog.show()

        }
        binding.appTitleBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }

        lifecycleScope.launch(Dispatchers.IO) {
            val cacheSize = getCacheSize()
            launch(Dispatchers.Main) {
                binding.cacheSize.text = "${bytesToMegabytes(cacheSize).roundToInt()}MB"
                binding.cacheSize.visibility = View.VISIBLE
                binding.progress.visibility = View.GONE
            }
        }
    }

    private fun bytesToMegabytes(bytes: Long): Double {
        val mb = bytes / (1024.0 * 1024.0)
        return mb
    }

    private fun getCacheSize() = try {
        getDirSize(requireContext().cacheDir)
    } catch (e: Exception) {
        e.printStackTrace()
        0
    }

    // 递归计算目录大小
    private fun getDirSize(dir: File): Long {
        var size: Long = 0
        val files = dir.listFiles()
        if (files != null) {
            for (file in files) {
                size += if (file.isDirectory) {
                    getDirSize(file)
                } else {
                    file.length()
                }
            }
        }
        return size
    }

    // 清理缓存目录
    private fun clearCache() = try {
        clearDir(requireContext().cacheDir)
    } catch (e: Exception) {
        e.printStackTrace()
    }

    // 递归清理目录
    private fun clearDir(dir: File) {
        val files = dir.listFiles()
        if (files != null) {
            for (file in files) {
                if (file.isDirectory) {
                    clearDir(file)
                }
                file.delete()
            }
        }
    }
}
