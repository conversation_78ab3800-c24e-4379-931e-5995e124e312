package online.yllh.smartinspect.view.editImage

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Matrix
import android.graphics.SurfaceTexture
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.core.content.edit
import androidx.exifinterface.media.ExifInterface
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.google.gson.Gson
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.tapapk.camera_engine.utils.Exif
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.GlobalVals
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentWatermarkEditBinding
import online.yllh.smartinspect.dialog.TipsDialog
import online.yllh.smartinspect.extension.hideKeyboard
import online.yllh.smartinspect.extension.parcelable
import online.yllh.smartinspect.extension.toJpegByteArray
import online.yllh.smartinspect.media.saveImageToGallery
import online.yllh.smartinspect.model.AddressDataModel
import online.yllh.smartinspect.model.EditModel
import online.yllh.smartinspect.provider.ApplicationContextProvider
import online.yllh.smartinspect.sticker.EmptySticker
import online.yllh.smartinspect.sticker.PhotoExtraInfoSticker
import online.yllh.smartinspect.sticker.PhotoExtraInfoNewSticker
import online.yllh.smartinspect.sticker.StickerManager
import online.yllh.smartinspect.uiwidget.sticker.StickerGalleryFragment
import online.yllh.smartinspect.uiwidget.sticker.StickerPanel
import online.yllh.smartinspect.utils.CompassSensorHelper
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


class WatermarkEditFragment : BaseFragment() {
    private var previewSurface: SurfaceTexture? = null
    private var previewWidth: Int = 0
    private var previewHeight: Int = 0
    private lateinit var binding: FragmentWatermarkEditBinding
    private val viewModel by viewModels<WatermarkEditViewModel>()
    private val compassHelper by lazy { CompassSensorHelper(requireContext()) }
    private val imageInfo: EditModel by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.parcelable(FragmentArguments.EditModel) ?: throw IllegalArgumentException("imageInfo is null")
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ) = FragmentWatermarkEditBinding.inflate(inflater, container, false).also { binding = it }.root

    private val textureViewListener = object : TextureView.SurfaceTextureListener {
        override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
            previewWidth = width
            previewHeight = height
            previewSurface = surface
            viewModel.setSurfaceTextureAvailable(true)
        }

        override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {
            previewWidth = width
            previewHeight = height
            previewSurface = surface
            viewModel.surfaceSizeChange(width, height)
        }

        override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
            viewModel.release()
            viewModel.setSurfaceTextureAvailable(false)
            return true
        }

        override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
        }
    }

    private val backPressedCallback = object : OnBackPressedCallback(false) {
        override fun handleOnBackPressed() {
            if (binding.stickerPanel.showing)
                binding.stickerPanel.hide()
            if (binding.addressLevelPanel.showing) {
                binding.addressLevelPanel.hide()
            } else if (binding.addressSelectPanel.showing) {
                binding.addressSelectPanel.hide()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_HIDE_BAR)
            .init()
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, backPressedCallback)
        binding.mainImage.surfaceTextureListener = textureViewListener
        binding.mainImage.layoutParams.width = imageInfo.viewWidth
        binding.mainImage.layoutParams.height = imageInfo.viewHeight
        binding.mainImage.requestLayout()

        observeOn(viewModel.previewTextureIsReady) {
            if (it) {
                lifecycleScope.launch(Dispatchers.IO) {
                    var inputStream: FileInputStream? = null
                    try {
                        inputStream = FileInputStream(File(imageInfo.imagePath))
                        val bytes = inputStream.readBytes()
                        viewModel.initEditEnvironment(requireContext(), previewSurface!!, previewWidth, previewHeight, bytes)
                        setSticker()
                    } finally {
                        inputStream?.close()
                    }
                }
            }
        }
        initStickerPanel()
        binding.cancelButton.setOnClickListener {
            binding.root.findNavController().popBackStack()
        }
        binding.okButton.setOnClickListener {
            binding.locationContainer.visibility = View.GONE
            binding.cancelButton.visibility = View.GONE
            binding.okButton.visibility = View.GONE
            binding.linearLayout4.visibility = View.GONE
            viewModel.editEngineCore.outBitmap(imageInfo.imageWidth, imageInfo.imageHeight) {
                viewLifecycleScope.launch(Dispatchers.IO) {
                    val width: Int = it.width
                    val height: Int = it.height
                    val matrix = Matrix()
                    matrix.postScale(-1f, 1f)
                    matrix.postRotate(180f)
                    // 创建一个旋转后的Bitmap对象
                    val rotatedBitmap = Bitmap.createBitmap(it, 0, 0, width, height, matrix, true)
                    val cacheDir = File(ApplicationContextProvider.context.cacheDir.path + "/cameraTmp")
                    if (!cacheDir.exists()) {
                        cacheDir.mkdir()
                    }
                    val imageFile = File(cacheDir.path + "/" + System.currentTimeMillis() + ".jpg")
                    if (!imageFile.exists()) {
                        cacheDir.createNewFile()
                    }
                    val exifData = Exif.getExifData(imageInfo.imagePath)
                    val fileByte = Exif.exifToJpegData(rotatedBitmap.toJpegByteArray(100), exifData)
                    val outputStream = FileOutputStream(imageFile)
                    outputStream.write(fileByte)
                    outputStream.close()
                    val exifInterface = ExifInterface(imageFile)

                    // 格式化时间字符串
                    val sdf = SimpleDateFormat("yyyy:MM:dd HH:mm:ss", Locale.getDefault())
                    val currentDateTime = sdf.format(Date())


                    // 修改创建时间和修改时间
                    exifInterface.setAttribute(ExifInterface.TAG_DATETIME, currentDateTime)
                    exifInterface.setAttribute(ExifInterface.TAG_DATETIME_DIGITIZED, currentDateTime)
                    exifInterface.setAttribute(ExifInterface.TAG_DATETIME_ORIGINAL, currentDateTime)

                    // 保存修改

                    // 保存修改
                    exifInterface.saveAttributes()
                    val uri = saveImageToGallery(requireContext(), imageFile)
                    launch(Dispatchers.Main) {
                        binding.root.findNavController().navigate(R.id.action_watermarkEditFragment_to_editResultFragment, Bundle().apply {
                            this.putSerializable(FragmentArguments.ImageUri, uri.toString())
                            this.putSerializable(FragmentArguments.ImageCachePath, imageFile.path)
                        })
                    }
                }
            }
        }

        binding.location.setOnClickListener {
            if (!viewModel.isShowWatermark.value) {
                val dialog = TipsDialog(requireContext())
                dialog.setTitle("如需要添加地理位置信息, 请开启水印")
                dialog.setCancelListener { }
                dialog.setOkListener {}
                dialog.show()
            } else {
                binding.addressSelectPanel.show(
                    AddressDataModel(
                        country = viewModel.location.value.country,
                        province = viewModel.location.value.province,
                        city = viewModel.location.value.city,
                        district = viewModel.location.value.district,
                        street = viewModel.location.value.street,
                        town = viewModel.location.value.town,
                        poiList = viewModel.location.value.poiList,
                    )
                )
                backPressedCallback.isEnabled = true
            }
        }
        binding.watermark.setOnClickListener {
            binding.stickerPanel.show()
            backPressedCallback.isEnabled = true
        }
        binding.addressSelectPanel.lifecycleOwner = viewLifecycleOwner
        binding.addressSelectPanel.hideListener = {
            backPressedCallback.isEnabled = false
        }
        binding.addressSelectPanel.showLevelListener = {
            view.hideKeyboard()
            binding.addressLevelPanel.show(
                AddressDataModel(
                    country = viewModel.location.value.country,
                    province = viewModel.location.value.province,
                    city = viewModel.location.value.city,
                    district = viewModel.location.value.district,
                    street = viewModel.location.value.street,
                    town = viewModel.location.value.town,
                )
            )
        }
        binding.addressSelectPanel.selectAddressListener = {
            requireActivity().supportFragmentManager.setFragmentResult(
                "AddressFragment", Bundle().apply { putString("address", it) }
            )
        }
        binding.addressLevelPanel.onLocationLevelChanged = {
            binding.addressSelectPanel.updateAddressType()
            requireActivity().supportFragmentManager.setFragmentResult(
                "AddressFragment", Bundle().apply { putString("addressLevel", it) }
            )
        }
        binding.addressLevelPanel.onEnableLevelChanged = {
            binding.addressSelectPanel.onEnableLevelChanged(it)
            requireActivity().supportFragmentManager.setFragmentResult(
                "AddressFragment", Bundle().apply { putInt("enableLevel", if (it) 1 else 0) }
            )
        }
        observeOn(viewModel.currentSticker) {
            setSticker()
            viewModel.recentStickerAdapter?.stickerList = StickerManager.getRecentlyStickerList(requireContext())
            viewModel.setCurrentSticker(it)
        }

        binding.stickerPanel.hideListener = {
            backPressedCallback.isEnabled = false
        }
        observeOn(viewModel.located, state = Lifecycle.State.CREATED) {
        }
        observeOn(compassHelper.azimuth) {
            viewModel.watermarkState.value = viewModel.watermarkState.value.copy(azimuth = it)
        }
        observeOn(viewModel.watermarkState) {
            setSticker()
        }
        requireActivity().supportFragmentManager.setFragmentResultListener(
            "AddressFragment", viewLifecycleOwner
        ) { _, bundle ->
            val result = bundle.getString("address", "")
            if (result.isNotEmpty()) {
                viewModel.updateWatermarkAddress(result)
            }
            val enableLevel = bundle.getInt("enableLevel", -1)
            val addressLevel = bundle.getString("addressLevel", "")
            if (enableLevel != -1 || addressLevel.isNotEmpty()) {
                viewModel.refreshLocationLevel()
            }
        }

        lifecycleScope.launch {
            GlobalVals.networkStateEvent.collect { isNetworkAvailable ->
                Log.d("kofua", "onNetworkStateEvent: $isNetworkAvailable")
                delay(500)
                setSticker()
                if (isNetworkAvailable && !viewModel.located.value) {
                    viewModel.stopLocating()
                    delay(500)
                    viewModel.startLocating()
                }
            }
        }
    }

    private fun initStickerPanel() {
        if (viewModel.workStickerAdapter == null) {
            viewModel.workStickerAdapter = StickerGalleryFragment.StickerAdapter(StickerManager.stickerList, {
                binding.stickerPanel.hide()
                viewModel.selectListener.invoke(it)
            }, viewModel.currentSticker.value)
        }
        if (viewModel.recentStickerAdapter == null) {
            viewModel.recentStickerAdapter = StickerGalleryFragment.StickerAdapter(StickerManager.getRecentlyStickerList(requireContext()), {
                binding.stickerPanel.hide()
                viewModel.selectListener.invoke(it)
            }, viewModel.currentSticker.value)
        }
        if (viewModel.stickerPanelAdapter == null) {
            viewModel.stickerPanelAdapter = StickerPanel.StickerPanelAdapter(
                childFragmentManager, viewModel.workStickerAdapter!!, viewModel.recentStickerAdapter!!
            )
        }
        binding.stickerPanel.initAdapter(viewModel.stickerPanelAdapter!!) {
            viewModel.selectListener.invoke(it)
        }
    }

    private fun setSticker() {
        if (previewWidth == 0 || previewHeight == 0) {
            return
        }
        val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        val recentlySticker = sp.getString(SpKey.RecentlySticker, "").orEmpty()
        val gson = Gson()
        if (recentlySticker.isNotEmpty()) {
            var list = gson.fromJson<List<String>>(recentlySticker, List::class.java)
            if (!list.contains(viewModel.currentSticker.value)) {
                list = list.plus(viewModel.currentSticker.value)
            }
            sp.edit { putString(SpKey.RecentlySticker, gson.toJson(list)) }
        } else {
            val list = listOf(viewModel.currentSticker.value)
            sp.edit { putString(SpKey.RecentlySticker, gson.toJson(list)) }
        }

        if (viewModel.currentSticker.value == "dateAndLocationSticker") {
            viewModel.isShowWatermark.value = true
            viewModel.setSticker(
                PhotoExtraInfoSticker(
                    showWatermark = true,
                    showProject = false,
                    projectName = "", projectRemark = "",
                    watermarkState = viewModel.watermarkState.value
                ).createBitmap(previewWidth, previewHeight)
            )
        } else if (viewModel.currentSticker.value == "dateAndLocationNewSticker") {
            viewModel.isShowWatermark.value = true
            viewModel.setSticker(
                PhotoExtraInfoNewSticker(
                    showWatermark = true,
                    showProject = false,
                    projectName = "", projectRemark = "",
                    watermarkState = viewModel.watermarkState.value
                ).createBitmap(previewWidth, previewHeight)
            )
        } else {
            viewModel.isShowWatermark.value = false
            viewModel.setSticker(EmptySticker().createBitmap(previewWidth, previewHeight))
        }
    }

    override fun onResume() {
        super.onResume()
        compassHelper.start()
    }

    override fun onPause() {
        super.onPause()
        compassHelper.stop()
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.editEngineCore.release()
    }
}
