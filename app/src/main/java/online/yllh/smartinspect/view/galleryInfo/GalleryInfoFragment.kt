package online.yllh.smartinspect.view.galleryInfo

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.databinding.FragmentGalleryInfoBinding
import online.yllh.smartinspect.databinding.ItemGalleryInfoBinding
import online.yllh.smartinspect.extension.isHttpUrl
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.media.AudioPlayer
import online.yllh.smartinspect.model.MessageUIStatus
import online.yllh.smartinspect.network.model.response.Message
import online.yllh.smartinspect.network.model.response.Photo
import online.yllh.smartinspect.utils.DownloadUtils
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.UUID

class GalleryInfoFragment : BaseFragment() {
    private lateinit var binding: FragmentGalleryInfoBinding
    private val viewModel by viewModels<GalleryInfoViewModel>()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ) = FragmentGalleryInfoBinding.inflate(inflater, container, false).also { binding = it }.root

    private val albumId: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.AlbumId) ?: throw IllegalArgumentException("imageInfo is null")
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        observeLatestOn(viewModel.photoList) {
            binding.messageList.layoutManager = LinearLayoutManager(requireContext())
            binding.messageList.adapter = ItemAdapter(viewModel.photoList.value)
        }
        viewModel.getPhotoListData(requireContext(), albumId, binding.root)
        binding.appBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }
    }

    inner class ItemAdapter(private val photoList: List<Photo>) : RecyclerView.Adapter<ItemAdapter.ViewHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val binding = ItemGalleryInfoBinding.inflate(parent.context.layoutInflater, parent, false)
            return ViewHolder(binding)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            Glide.with(requireContext())
                .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                .load(photoList[position].photoAddress)
                .placeholder(R.drawable.placeholder)
                .into(holder.imageCover)
            holder.time.text = photoList[position].createTime
            val messageList = photoList[position].customMessages?.map {
                MessageUIStatus(
                    data = Message(
                        id = it.id,
                        inputTime = it.inputTime,
                        createTime = it.createTime,
                        value = it.value,
                        type = it.type?.toInt(),
                        second = it.second
                    )
                )
            }?.toMutableList()
            messageList?.sortBy { it.data.inputTime }
            holder.messageList.layoutManager = LinearLayoutManager(requireContext())
            holder.messageList.adapter = MessageAdapter(requireContext(), messageList ?: (emptyList<MessageUIStatus>().toMutableList()))
        }

        override fun getItemCount(): Int {
            return photoList.size
        }

        inner class ViewHolder(binding: ItemGalleryInfoBinding) : RecyclerView.ViewHolder(binding.root) {
            val imageCover = binding.imageCover
            val time = binding.time
            val messageList = binding.messageList
        }
    }

    inner class MessageAdapter(val context: Context, var messageList: MutableList<MessageUIStatus>) : RecyclerView.Adapter<MessageAdapter.ViewHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_new_message, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val msg = messageList[position]
            holder.dateTime.text = msg.data.inputTime
            if (msg.data.type == 0) {
                holder.textItem.visibility = View.VISIBLE
                holder.voiceItem.visibility = View.GONE
                holder.textItem.text = msg.data.value.orEmpty()
            } else {
                holder.textItem.visibility = View.GONE
                holder.voiceItem.visibility = View.VISIBLE
                holder.second.text = "${msg.data.second}s"

            }
            when (msg.status) {
                0 -> {
                    holder.stop.visibility = View.GONE
                    holder.play.visibility = View.VISIBLE
                    holder.progress.visibility = View.GONE
                }

                1 -> {
                    holder.stop.visibility = View.GONE
                    holder.play.visibility = View.GONE
                    holder.progress.visibility = View.VISIBLE
                }

                2 -> {
                    holder.stop.visibility = View.VISIBLE
                    holder.play.visibility = View.GONE
                    holder.progress.visibility = View.GONE
                }
            }
            holder.lockStatus.visibility = View.GONE
            holder.play.setOnClickListener {
                if (msg.data.type == 1) {
                    if (AudioPlayer.getInstance(requireContext()).isPlaying()) {
                        AudioPlayer.getInstance(requireContext()).stop()
                        messageList.forEach {
                            it.status = 0
                        }
                        notifyDataSetChanged()
                    }
                    msg.status = 1
                    notifyDataSetChanged()
                    if ((msg.data.value.orEmpty()).isHttpUrl()) {
                        lifecycleScope.launch(Dispatchers.IO) {
                            val bytes = msg.data.value?.let { it1 -> DownloadUtils.downloadFile(it1) }
                            if (bytes != null) {
                                val file = saveBytesToCache(bytes, UUID.randomUUID().toString(), requireContext())
                                launch(Dispatchers.Main) {
                                    msg.status = 2
                                    notifyDataSetChanged()
                                    AudioPlayer.getInstance(requireContext()).play(file?.path.orEmpty()) {
                                        msg.status = 0
                                        notifyDataSetChanged()
                                    }
                                }
                            }
                        }
                    } else {
                        msg.status = 2
                        notifyDataSetChanged()
                        AudioPlayer.getInstance(requireContext()).play(msg.data.value.orEmpty()) {
                            msg.status = 0
                            notifyDataSetChanged()
                        }
                    }
                }
            }
            holder.stop.setOnClickListener {
                AudioPlayer.getInstance(requireContext()).stop()
                msg.status = 0
                notifyDataSetChanged()
            }
        }

        override fun getItemCount(): Int {
            return messageList.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val messageItem: LinearLayout = itemView.findViewById(R.id.messageItem)
            val voiceItem: LinearLayout = itemView.findViewById(R.id.voiceItem)
            val textItem: TextView = itemView.findViewById(R.id.textItem)
            val play: ImageView = itemView.findViewById(R.id.play)
            val stop: ImageView = itemView.findViewById(R.id.stop)
            val progress: ProgressBar = itemView.findViewById(R.id.progress)
            val second: TextView = itemView.findViewById(R.id.second)
            val dateTime: TextView = itemView.findViewById(R.id.dateTime)
            val lockStatus: ImageView = itemView.findViewById(R.id.lockStatus)
        }
    }

    fun saveBytesToCache(bytes: ByteArray, fileName: String, context: Context): File? {
        val cacheDir = context.cacheDir
        val file = File(cacheDir, fileName)

        try {
            FileOutputStream(file).use { outputStream ->
                outputStream.write(bytes)
            }
            return file
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
    }
}
