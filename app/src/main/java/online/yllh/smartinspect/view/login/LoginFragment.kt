package online.yllh.smartinspect.view.login

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.edit
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentLoginBinding
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.LoginUser
import retrofit2.awaitResponse

class LoginFragment : BaseFragment() {
    private lateinit var binding: FragmentLoginBinding
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentLoginBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        var isRememberPass = sp.getBoolean(SpKey.IsRememberPass, true)
        binding.rememberPass.isChecked = isRememberPass

        // 始终从 SharedPreferences 中读取用户名和密码
        binding.phoneNumEditText.setText(sp.getString(SpKey.UserName, "").orEmpty())
        binding.passNumEditText.setText(sp.getString(SpKey.Password, "").orEmpty())

        binding.rememberPass.setOnCheckedChangeListener { buttonView, isChecked ->
            isRememberPass = isChecked
        }

        if (binding.rememberPass.isChecked) {
            binding.phoneNumEditText.setText(sp.getString(SpKey.UserName, "").orEmpty())
            binding.passNumEditText.setText(sp.getString(SpKey.Password, "").orEmpty())
        }

        binding.register.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_loginFragment_to_signupFragment)
        }
        binding.forgetPass.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_loginFragment_to_forgetPassFragment)
        }
        binding.appBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }

        binding.login.setOnClickListener {
            val phone = binding.phoneNumEditText.text
            val pass = binding.passNumEditText.text
            if (phone.toString().isNotEmpty() && pass.toString().isNotEmpty()) {
                val dialog = LoadingDialog(requireContext())
                dialog.setTitle("登录中")
                dialog.setButtonVisibility(View.GONE)
                dialog.setCancelable(false)
                dialog.show()
                lifecycleScope.launch(Dispatchers.IO) {
                    try {
                        val request = RetrofitClient.service.appLogin(
                            LoginUser(
                                phone.toString(),
                                pass.toString(),
                            )
                        )
                        val response = request.awaitResponse()
                        launch(Dispatchers.Main) {
                            if (response.isSuccessful) {
                                val body = response.body()
                                if (body?.data != null && body.ok) {
                                    val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                                    sp.edit {
                                        putString(SpKey.UserCookies, body.data.token)
                                        if (isRememberPass) {
                                            putString(
                                                SpKey.UserName,
                                                binding.phoneNumEditText.text.toString()
                                            )
                                            putString(
                                                SpKey.Password,
                                                binding.passNumEditText.text.toString()
                                            )
                                            putBoolean(SpKey.IsRememberPass, true)
                                        } else {
                                            putString(SpKey.UserName, "")
                                            putString(SpKey.Password, "")
                                            putBoolean(SpKey.IsRememberPass, true)
                                        }
                                    }
                                    Toast.makeText(requireContext(), "登录成功", Toast.LENGTH_SHORT).show()
                                    binding.root.findNavController().popBackStack(R.id.cameraFragment, false)
                                } else {
                                    Toast.makeText(requireContext(), "登录失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                                }
                            } else {
                                Toast.makeText(requireContext(), "网络连接失败", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        launch(Dispatchers.Main) {
                            Toast.makeText(requireContext(), "网络连接失败", Toast.LENGTH_SHORT).show()
                        }
                    } finally {
                        launch(Dispatchers.Main) {
                            dialog.dismiss()
                        }
                    }
                }
            } else {
                Toast.makeText(requireContext(), "请输入正确的手机号和密码", Toast.LENGTH_SHORT).show()
            }
        }
    }
}
