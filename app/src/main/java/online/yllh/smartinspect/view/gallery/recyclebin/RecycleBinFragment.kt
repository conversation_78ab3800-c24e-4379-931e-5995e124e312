package online.yllh.smartinspect.view.gallery.recyclebin

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import online.yllh.smartinspect.AppDatabase
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.FragmentRecycleBinBinding
import online.yllh.smartinspect.entity.Photo
import online.yllh.smartinspect.entity.RecycledPhoto
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.dialog.TipsDialog
import java.io.File
import java.util.*

class RecycleBinFragment : Fragment() {

    private var _binding: FragmentRecycleBinBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var adapter: RecycledPhotoAdapter
    private val selectedPhotos = mutableSetOf<RecycledPhoto>()
    private var isSelectionMode = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentRecycleBinBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupRecyclerView()
        setupButtons()
        loadRecycledPhotos()
    }

    override fun onResume() {
        super.onResume()
        loadRecycledPhotos()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun setupRecyclerView() {
        adapter = RecycledPhotoAdapter(
            context = requireContext(),
            onPhotoClick = { photo -> togglePhotoSelection(photo) },
            onPhotoLongClick = { photo -> startSelectionMode(photo) }
        )
        
        binding.photoRecyclerView.apply {
            layoutManager = GridLayoutManager(requireContext(), 3)
            adapter = <EMAIL>
        }
    }

    private fun setupButtons() {
        binding.toggleSelectionButton.setOnClickListener {
            if (!isSelectionMode) {
                // 进入选择模式，但不选择任何照片
                isSelectionMode = true
                adapter.setSelectionMode(true)
                updateSelectionUI()
            } else {
                exitSelectionMode()
            }
        }

        binding.clearAllButton.setOnClickListener {
            showClearAllConfirmDialog()
        }
        
        binding.selectAllButton.setOnClickListener {
            adapter?.let { adapter ->
                if (selectedPhotos.size == adapter.photos.size) {
                    // 取消全选
                    selectedPhotos.clear()
                } else {
                    // 全选
                    selectedPhotos.clear()
                    selectedPhotos.addAll(adapter.photos)
                }
                adapter.updateSelection(selectedPhotos)
                updateSelectionUI()
            }
        }
        
        // 添加恢复按钮点击事件
        binding.restoreButton.setOnClickListener {
            if (selectedPhotos.isNotEmpty()) {
                showRestoreConfirmDialog()
            }
        }
        
        // 添加彻底删除按钮点击事件
        binding.deleteButton.setOnClickListener {
            if (selectedPhotos.isNotEmpty()) {
                showDeleteConfirmDialog()
            }
        }
    }

    private fun loadRecycledPhotos() {
        lifecycleScope.launch {
            try {
                val recycledPhotos = withContext(Dispatchers.IO) {
                    val allRecycledPhotos = AppDatabase.get().recycledPhotoDao().getAllRecycledPhotos()
                    
                    // For recycled photos, check if the file exists on disk
                    // Don't use MediaStore check as the photo might not be in MediaStore anymore
                    allRecycledPhotos.filter { recycledPhoto ->
                        File(recycledPhoto.cachePath).exists()
                    }
                }
                
                adapter.updatePhotos(recycledPhotos)
                updatePhotoCount(recycledPhotos.size)
                binding.emptyStateLayout.isVisible = recycledPhotos.isEmpty()
                binding.photoRecyclerView.isVisible = recycledPhotos.isNotEmpty()
                
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "加载回收站照片失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun togglePhotoSelection(photo: RecycledPhoto) {
        if (!isSelectionMode) {
            // 单击照片时显示操作菜单
            showPhotoActionDialog(photo)
        } else {
            if (selectedPhotos.contains(photo)) {
                selectedPhotos.remove(photo)
            } else {
                selectedPhotos.add(photo)
            }
            adapter.updateSelection(selectedPhotos)
            updateSelectionUI()
            
            if (selectedPhotos.isEmpty()) {
                exitSelectionMode()
            }
        }
    }

    private fun showPhotoActionDialog(photo: RecycledPhoto) {
        val options = arrayOf("恢复照片", "彻底删除")
        AlertDialog.Builder(requireContext())
            .setTitle("选择操作")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> {
                        // 恢复单张照片
                        selectedPhotos.clear()
                        selectedPhotos.add(photo)
                        restoreSelectedPhotos()
                    }
                    1 -> {
                        // 删除单张照片
                        selectedPhotos.clear()
                        selectedPhotos.add(photo)
                        showDeleteConfirmDialog()
                    }
                }
            }
            .show()
    }

    private fun startSelectionMode(photo: RecycledPhoto) {
        isSelectionMode = true
        selectedPhotos.clear()
        selectedPhotos.add(photo)
        adapter.setSelectionMode(true)
        adapter.updateSelection(selectedPhotos)
        updateSelectionUI()
    }

    private fun exitSelectionMode() {
        isSelectionMode = false
        selectedPhotos.clear()
        adapter.setSelectionMode(false)
        adapter.updateSelection(selectedPhotos)
        updateSelectionUI()
    }

    private fun updateSelectionUI() {
        if (isSelectionMode) {
            binding.toggleSelectionButton.setText("取消")
            binding.selectAllButton.visibility = View.VISIBLE
            binding.selectedCountText.visibility = View.VISIBLE
            
            val selectedCount = selectedPhotos.size
            val totalCount = adapter?.photos?.size ?: 0
            binding.selectedCountText.text = "已选择 $selectedCount/$totalCount"
            
            // 如果有选中的照片，显示操作按钮
            if (selectedPhotos.isNotEmpty()) {
                binding.selectionToolbar.visibility = View.VISIBLE
                binding.bottomActionBar.visibility = View.GONE
            } else {
                binding.selectionToolbar.visibility = View.GONE
                binding.bottomActionBar.visibility = View.VISIBLE
            }
        } else {
            binding.toggleSelectionButton.setText("选择")
            binding.selectAllButton.visibility = View.GONE
            binding.selectedCountText.visibility = View.GONE
            binding.selectionToolbar.visibility = View.GONE
            binding.bottomActionBar.visibility = View.VISIBLE
        }
    }

    private fun updatePhotoCount(count: Int) {
        binding.photoCountText.text = "回收站 ($count)"
    }

    private fun showRestoreConfirmDialog() {
        val dialog = TipsDialog(requireContext())
        dialog.setTitle("确定要恢复选中的 ${selectedPhotos.size} 张照片吗？")
        dialog.setCancelListener { }
        dialog.setOkListener {
            restoreSelectedPhotos()
        }
        dialog.show()
    }

    private fun restoreSelectedPhotos() {
        lifecycleScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    val database = AppDatabase.get()
                    val photoDao = database.photoDao()
                    val recycledPhotoDao = database.recycledPhotoDao()
                    
                    selectedPhotos.forEach { recycledPhoto ->
                        // 恢复到Photo表
                        val photo = Photo(
                            uri = recycledPhoto.uri,
                            cachePath = recycledPhoto.cachePath,
                            upload = false, // 恢复为未上传状态
                            shotTime = recycledPhoto.shotTime,
                            longitude = recycledPhoto.longitude,
                            latitude = recycledPhoto.latitude
                        )
                        photoDao.insert(photo)
                        
                        // 从回收站删除
                        recycledPhotoDao.delete(recycledPhoto)
                    }
                }
                
                Toast.makeText(requireContext(), "成功恢复 ${selectedPhotos.size} 张照片", Toast.LENGTH_SHORT).show()
                exitSelectionMode()
                loadRecycledPhotos()
                
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "恢复照片失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun showDeleteConfirmDialog() {
        val dialog = TipsDialog(requireContext())
        dialog.setTitle("确定要彻底删除选中的 ${selectedPhotos.size} 张照片吗？此操作不可恢复！")
        dialog.setCancelListener { }
        dialog.setOkListener {
            deleteSelectedPhotos()
        }
        dialog.show()
    }

    private fun deleteSelectedPhotos() {
        lifecycleScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    val recycledPhotoDao = AppDatabase.get().recycledPhotoDao()
                    
                    selectedPhotos.forEach { recycledPhoto ->
                        // 删除本地文件
                        try {
                            val file = File(recycledPhoto.cachePath)
                            if (file.exists()) {
                                file.delete()
                            }
                        } catch (e: Exception) {
                            // 忽略文件删除错误，继续删除数据库记录
                        }
                        
                        // 从数据库删除
                        recycledPhotoDao.delete(recycledPhoto)
                    }
                }
                
                Toast.makeText(requireContext(), "成功删除 ${selectedPhotos.size} 张照片", Toast.LENGTH_SHORT).show()
                exitSelectionMode()
                loadRecycledPhotos()
                
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "删除照片失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun showClearAllConfirmDialog() {
        lifecycleScope.launch {
            val count = withContext(Dispatchers.IO) {
                AppDatabase.get().recycledPhotoDao().getRecycledPhotoCount()
            }
            
            if (count == 0) {
                Toast.makeText(requireContext(), "回收站已经是空的", Toast.LENGTH_SHORT).show()
                return@launch
            }
            
            val dialog = TipsDialog(requireContext())
            dialog.setTitle("确定要清空回收站中的所有 $count 张照片吗？此操作不可恢复！")
            dialog.setCancelListener { }
            dialog.setOkListener {
                clearAllPhotos()
            }
            dialog.show()
        }
    }

    private fun clearAllPhotos() {
        lifecycleScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    val recycledPhotoDao = AppDatabase.get().recycledPhotoDao()
                    val allPhotos = recycledPhotoDao.getAllRecycledPhotos()
                    
                    allPhotos.forEach { recycledPhoto ->
                        // 删除本地文件
                        try {
                            val file = File(recycledPhoto.cachePath)
                            if (file.exists()) {
                                file.delete()
                            }
                        } catch (e: Exception) {
                            // 忽略文件删除错误
                        }
                    }
                    
                    // 清空数据库
                    recycledPhotoDao.deleteAll()
                }
                
                Toast.makeText(requireContext(), "回收站已清空", Toast.LENGTH_SHORT).show()
                loadRecycledPhotos()
                
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "清空回收站失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    inner class RecycledPhotoAdapter(
        private val context: Context,
        private val onPhotoClick: (RecycledPhoto) -> Unit,
        private val onPhotoLongClick: (RecycledPhoto) -> Unit
    ) : RecyclerView.Adapter<RecycledPhotoAdapter.ViewHolder>() {

        var photos = listOf<RecycledPhoto>()
        private var selectedPhotos = setOf<RecycledPhoto>()
        private var isSelectionMode = false

        fun updatePhotos(newPhotos: List<RecycledPhoto>) {
            photos = newPhotos
            notifyDataSetChanged()
        }

        fun updateSelection(selected: Set<RecycledPhoto>) {
            selectedPhotos = selected
            notifyDataSetChanged()
        }

        fun setSelectionMode(enabled: Boolean) {
            isSelectionMode = enabled
            notifyDataSetChanged()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(context).inflate(R.layout.item_recycled_photo, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val photo = photos[position]
            
            // 设置图片尺寸
            val displayMetrics = context.resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val imageWidth = (screenWidth - 18.dp) / 3
            holder.imageView.layoutParams.width = imageWidth
            holder.imageView.layoutParams.height = imageWidth
            
            // 加载图片
            Glide.with(context)
                .load(File(photo.cachePath))
                .apply(
                    RequestOptions()
                        .format(DecodeFormat.PREFER_RGB_565)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                        .skipMemoryCache(true)
                )
                .placeholder(R.drawable.placeholder)
                .error(R.drawable.empty_album_placeholder)
                .into(holder.imageView)
            
            // 显示剩余天数
            val remainingDays = photo.getRemainingDays()
            holder.remainingDaysText.text = "${remainingDays}天"
            
            // 选择状态显示
            holder.checkBox.visibility = if (isSelectionMode) View.VISIBLE else View.GONE
            holder.checkBox.isChecked = selectedPhotos.contains(photo)
            holder.selectionOverlay.visibility = if (isSelectionMode && selectedPhotos.contains(photo)) View.VISIBLE else View.GONE
            
            // 点击事件
            holder.itemView.setOnClickListener {
                onPhotoClick(photo)
            }
            
            holder.itemView.setOnLongClickListener {
                onPhotoLongClick(photo)
                true
            }
        }

        override fun getItemCount(): Int {
            return photos.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val imageView: ImageView = itemView.findViewById(R.id.photoImageView)
            val remainingDaysText: TextView = itemView.findViewById(R.id.remainingDaysText)
            val checkBox: android.widget.CheckBox = itemView.findViewById(R.id.photoCheckBox)
            val selectionOverlay: View = itemView.findViewById(R.id.selectionOverlay)
        }
    }
}
