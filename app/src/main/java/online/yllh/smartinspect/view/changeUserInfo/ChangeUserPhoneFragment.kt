package online.yllh.smartinspect.view.changeUserInfo

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.edit
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentChangeUserPhoneBinding
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.extension.serializable
import online.yllh.smartinspect.network.PublicRequest
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.ChangeUserModel
import retrofit2.awaitResponse
import java.io.File
import java.io.FileOutputStream

class ChangeUserPhoneFragment : BaseFragment() {
    private lateinit var binding: FragmentChangeUserPhoneBinding
    private lateinit var countDownTimer: CountDownTimer
    private var headImg: String = ""
    private val phone: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.serializable("phone") ?: throw IllegalArgumentException("imageInfo is null")
    }
    private val userId: Int by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.serializable("id") ?: throw IllegalArgumentException("imageInfo is null")
    }
    private val avatar: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.serializable("avatar") ?: throw IllegalArgumentException("imageInfo is null")
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ) = FragmentChangeUserPhoneBinding.inflate(inflater, container, false).also { binding = it }.root

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        binding.appBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }
        binding.cancelButton.setOnClickListener {
            binding.root.findNavController().popBackStack()
        }
        binding.orgPhoneNum.text = phone

        binding.sendSmsCode.setOnClickListener {
            sendSmsCode()
        }
        binding.okButton.setOnClickListener {
            changeUserInfo(requireContext(), binding.root)
        }

        Glide.with(requireContext())
            .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
            .load(avatar)
            .placeholder(R.drawable.ic_defult_avatar)
            .centerCrop() // 添加centerCrop以实现类似CSS object-fit: cover的效果
            .into(binding.avatar)
    }

    fun changeUserInfo(context: Context, root: View) {
        val dialog = LoadingDialog(context)
        dialog.setTitle("请稍候")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                val request = RetrofitClient.service.changeUserInfo(
                    ChangeUserModel(
                        nickName = null,
                        newPhone = binding.phoneNum.text.toString().ifEmpty { null },
                        newPhoneVerificationCode = binding.verifyCode.text.toString().ifEmpty { null },
                        headImg = headImg,
                        id = userId
                    )
                )
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            if (binding.phoneNum.text.toString().isNotEmpty()) {
                                sp.edit { remove(SpKey.UserCookies) }
                                binding.root.findNavController().navigate("loginFragment")
                                Toast.makeText(context, "修改用户信息成功 请重新登录", Toast.LENGTH_SHORT).show()
                            } else {
                                Toast.makeText(context, "修改用户信息成功", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            Toast.makeText(context, "修改失败 ${body?.msg}", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        if (response.code() == 401) {
                            sp.edit { putString(SpKey.UserCookies, "") }
                            root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            //Image Uri will not be null for RESULT_OK
            lifecycleScope.launch(Dispatchers.IO) {
                val uri: Uri = data?.data!!

                saveUriToCache(requireContext(), uri)?.let {
                    headImg = PublicRequest.uploadFile(it).orEmpty()
                    launch(Dispatchers.Main) {
                        Glide.with(requireContext())
                            .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                            .load(it)
                            .placeholder(R.drawable.ic_defult_avatar)
                            .centerCrop() // 添加centerCrop以实现类似CSS object-fit: cover的效果
                            .into(binding.avatar)
                    }
                }
            }
        }
    }

    fun saveUriToCache(context: Context, uri: Uri): File? {
        val inputStream = context.contentResolver.openInputStream(uri)
        val cacheDir = context.cacheDir
        val fileName = "cached_file"
        val file = File(cacheDir, fileName)

        inputStream?.use { input ->
            FileOutputStream(file).use { output ->
                input.copyTo(output)
            }
        }

        return file
    }

    fun sendSmsCode() {
        countDownTimer = object : CountDownTimer(60000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                // 在计时器每隔一秒触发时更新文本
                val secondsLeft = millisUntilFinished / 1000
                binding.sendSmsCode.setText("$secondsLeft s")
                binding.sendSmsCode.isEnabled = false
            }

            override fun onFinish() {
                // 倒计时完成时执行的操作
                binding.sendSmsCode.setText("发送")
                binding.sendSmsCode.isEnabled = true
                // 这里可以添加倒计时结束后的逻辑
            }
        }
        countDownTimer.start()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = RetrofitClient.service.smsByUpdatePhone(binding.phoneNum.text.toString())
                if (result.awaitResponse().isSuccessful) {
                    launch(Dispatchers.Main) {
                        Toast.makeText(requireContext(), "发送成功", Toast.LENGTH_SHORT).show()
                    }
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    Toast.makeText(requireContext(), "网络连接失败", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
}
