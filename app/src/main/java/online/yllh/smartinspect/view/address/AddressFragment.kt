package online.yllh.smartinspect.view.address

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentAddressBinding
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.extension.parcelable
import online.yllh.smartinspect.model.AddressDataModel
import online.yllh.smartinspect.model.PoiInfo

class AddressFragment : BaseFragment() {

    private lateinit var binding: FragmentAddressBinding
    private val viewModel by viewModels<AddressModel>()
    private val addressData: AddressDataModel by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.parcelable(FragmentArguments.Address) ?: throw IllegalArgumentException("imageInfo is null")
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentAddressBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(this, backPressedCallback)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        viewModel.poiList.value = addressData.poiList.orEmpty()
        val adapter = PoiAdapter(addressData.poiList.orEmpty())
        binding.addressList.layoutManager = LinearLayoutManager(requireContext())
        binding.addressList.adapter = adapter
        updateAddressType()
        observeLatestOn(viewModel.addressStr) {
            binding.area.text = getAddress()
        }
        observeLatestOn(viewModel.poiList) {
            adapter.poiList = it
            adapter.notifyDataSetChanged()
        }
        binding.changeAddressLevel.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_addressFragment_to_addressLevelFragment, Bundle().apply {
                this.putParcelable(
                    FragmentArguments.Address,
                    AddressDataModel(
                        country = addressData.country,
                        province = addressData.province,
                        city = addressData.city,
                        district = addressData.district,
                        street = addressData.street,
                        town = addressData.town,
                    )
                )
            })
        }
        binding.appBar.setOnClickListener {
//            putData(viewModel.getAddress())
            binding.root.findNavController().popBackStack()
        }

        binding.searchInput.addTextChangedListener {
            lifecycleScope.launch {
                if (it.toString().isNotEmpty()) {
                    viewModel.searchPoi(addressData.city, it.toString(), getAddress())
                }
            }
        }
        binding.clean.setOnClickListener {
            binding.searchInput.text.clear()
            viewModel.poiList.value = addressData.poiList.orEmpty()
        }
    }

    private fun updateAddressType() {
        val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        when (sp.getString(SpKey.LocationLevel, "city")) {
            "province" -> {
                viewModel.addressType.value = AddressType.PROVINCE
            }

            "city" -> {
                viewModel.addressType.value = AddressType.CITY
            }

            "district" -> {
                viewModel.addressType.value = AddressType.DISTRICT
            }

            "street" -> {
                viewModel.addressType.value = AddressType.STREET
            }

            "town" -> {
                viewModel.addressType.value = AddressType.TOWN
            }

            "country" -> {
                viewModel.addressType.value = AddressType.COUNTRY
            }

            else -> {
                viewModel.addressType.value = AddressType.NONE
            }
        }
    }

    private val backPressedCallback = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            binding.root.findNavController().popBackStack()
        }
    }

    private fun putData(currentAddress: String) {
        requireActivity().supportFragmentManager.setFragmentResult(
            "AddressFragment",
            Bundle().apply {
                putSerializable("address", currentAddress)
            }
        )
    }

    fun getAddress(): String {
        val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        val enableLevel = sp.getBoolean(SpKey.EnableLevel, true)
        if (!enableLevel) {
            return "已关闭地址级别"
        }
        updateAddressType()
        return when (viewModel.addressType.value) {
            AddressType.COUNTRY -> addressData.country
            AddressType.PROVINCE -> addressData.province
            AddressType.CITY -> addressData.city
            AddressType.DISTRICT -> addressData.district
            AddressType.STREET -> addressData.street
            AddressType.TOWN -> addressData.town
            AddressType.NONE -> "已关闭地址级别"
        }
    }

    inner class PoiAdapter(var poiList: List<PoiInfo>) : RecyclerView.Adapter<PoiAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_poi, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.poiName.text = poiList[position].poiName
            holder.poiAddress.text = poiList[position].poiAddress
            holder.itemView.setOnClickListener {
                putData(poiList[position].poiName)
                binding.root.findNavController().popBackStack()
            }
        }

        override fun getItemCount(): Int {
            return poiList.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val poiName: TextView = itemView.findViewById(R.id.poiName)
            val poiAddress: TextView = itemView.findViewById(R.id.poiAddress)
        }
    }
}
