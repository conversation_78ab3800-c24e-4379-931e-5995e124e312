package online.yllh.smartinspect.view.gallery.cloud

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import online.yllh.smartinspect.uiwidget.ZoomableImageView
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.google.gson.Gson
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.databinding.FragmentCloudPhotoDetailBinding
import online.yllh.smartinspect.dialog.MovePhotoDialog
import online.yllh.smartinspect.dialog.TipsDialog
import online.yllh.smartinspect.dialog.PhotoMessageDialog
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.network.model.response.CloudPhotoAlbumModel
import online.yllh.smartinspect.network.model.response.Message
import online.yllh.smartinspect.view.gallery.cloud.CloudPhotoAlbumViewModel
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class CloudPhotoDetailFragment : BaseFragment() {

    private lateinit var binding: FragmentCloudPhotoDetailBinding
    private val viewModel by lazy { CloudPhotoDetailViewModel() }
    private val albumViewModel by viewModels<CloudPhotoAlbumViewModel>()

    private val photoUrl: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.ImageUri) ?: throw IllegalArgumentException("imageInfo is null")
    }
    private val imageId: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.ImageId) ?: throw IllegalArgumentException("imageInfo is null")
    }
    private val albumId: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.AlbumId) ?: throw IllegalArgumentException("albumId is null")
    }
    private val initialPhotoIndex: Int by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getInt("currentPhotoIndex", 0) ?: 0
    }

    private var photoList: List<CloudPhotoAlbumModel> = emptyList()
    private lateinit var photoAdapter: CloudPhotoPagerAdapter
    private var currentPhotoIndex: Int = 0

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = FragmentCloudPhotoDetailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()

        setupPhotoList()
        setupButtons()
    }

    private fun setupPhotoList() {
        // 获取相册中的所有照片
        albumViewModel.getPhotosByAlbum(requireContext(), albumId, binding.root)

        // 观察照片列表变化
        lifecycleScope.launch {
            albumViewModel.photos.collect { photos ->
                if (photos.isNotEmpty()) {
                    photoList = photos
                    currentPhotoIndex = initialPhotoIndex.coerceIn(0, photos.size - 1)
                    setupViewPager()
                }
            }
        }
    }

    private fun setupViewPager() {
        photoAdapter = CloudPhotoPagerAdapter(photoList)
        binding.photoViewPager.adapter = photoAdapter
        binding.photoViewPager.setCurrentItem(currentPhotoIndex, false)

        updatePhotoIndicator()

        binding.photoViewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                currentPhotoIndex = position
                updatePhotoIndicator()
            }
        })
    }

    private fun updatePhotoIndicator() {
        binding.photoIndicator.text = "${currentPhotoIndex + 1}/${photoList.size}"
    }

    private fun getCurrentPhoto(): CloudPhotoAlbumModel? {
        return if (photoList.isNotEmpty() && currentPhotoIndex < photoList.size) {
            photoList[currentPhotoIndex]
        } else null
    }

    private fun setupButtons() {
        binding.titleBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }

        binding.showDetail.setOnClickListener {
            val currentPhoto = getCurrentPhoto()
            if (currentPhoto != null) {
                binding.root.findNavController().navigate(R.id.action_cloudPhotoDetailFragment_to_cloudPhotoExifDetailFragment, Bundle().apply {
                    putSerializable(FragmentArguments.ImageId, currentPhoto.id)
                })
            }
        }

        binding.addMessage.setOnClickListener {
            val currentPhoto = getCurrentPhoto()
            if (currentPhoto != null) {
                viewModel.getImageDetail(requireContext(), currentPhoto.id ?: "", binding.root) {
                    val mergedList: List<Message> = (viewModel.messageList.value.orEmpty()) + (viewModel.voiceList.value.orEmpty())
                    val dialog = PhotoMessageDialog(
                        messageListJson = Gson().toJson(mergedList),
                        imageId = currentPhoto.id ?: ""
                    )
                    dialog.show(childFragmentManager, "photo_message_dialog")
                }
            }
        }

        binding.deletePhoto.setOnClickListener {
            val currentPhoto = getCurrentPhoto()
            if (currentPhoto != null) {
                val dialog = TipsDialog(requireContext())
                dialog.setTitle("确定要删除此照片吗？删除操作不可还原")
                dialog.setCancelListener { }
                dialog.setOkListener {
                    albumViewModel.deletePhoto(requireContext(), albumId, currentPhoto.id ?: "", binding.root) {
                        if (photoList.size <= 1) {
                            binding.root.findNavController().popBackStack()
                        } else {
                            val updatedPhotos = albumViewModel.photos.value
                            if (updatedPhotos.isNotEmpty()) {
                                photoList = updatedPhotos
                                if (currentPhotoIndex >= photoList.size) {
                                    currentPhotoIndex = photoList.size - 1
                                }
                                // 重新设置适配器和当前项
                                photoAdapter = CloudPhotoPagerAdapter(photoList)
                                binding.photoViewPager.adapter = photoAdapter
                                binding.photoViewPager.setCurrentItem(currentPhotoIndex, false)
                                updatePhotoIndicator()
                            } else {
                                binding.root.findNavController().popBackStack()
                            }
                        }
                    }
                }
                dialog.show()
            }
        }

        binding.movePhoto.setOnClickListener {
            val currentPhoto = getCurrentPhoto()
            if (currentPhoto != null) {
                albumViewModel.getAllAlbums(requireContext(), binding.root)

                lifecycleScope.launch {
                    albumViewModel.albums.collect { albums ->
                        if (albums.isNotEmpty()) {
                            val dialog = MovePhotoDialog(requireContext(), albums, albumId)
                            dialog.setOkButtonListener { targetAlbumId ->
                                if (targetAlbumId != null) {
                                    albumViewModel.movePhoto(requireContext(), albumId, targetAlbumId, currentPhoto.id ?: "", binding.root) {
                                        if (photoList.size <= 1) {
                                            binding.root.findNavController().popBackStack()
                                        } else {
                                            val updatedPhotos = albumViewModel.photos.value
                                            if (updatedPhotos.isNotEmpty()) {
                                                photoList = updatedPhotos
                                                if (currentPhotoIndex >= photoList.size) {
                                                    currentPhotoIndex = photoList.size - 1
                                                }
                                                photoAdapter = CloudPhotoPagerAdapter(photoList)
                                                binding.photoViewPager.adapter = photoAdapter
                                                binding.photoViewPager.setCurrentItem(currentPhotoIndex, false)
                                                updatePhotoIndicator()
                                            } else {
                                                binding.root.findNavController().popBackStack()
                                            }
                                        }
                                    }
                                }
                            }
                            dialog.show()
                            return@collect
                        }
                    }
                }
            }
        }
    }

    inner class CloudPhotoPagerAdapter(private val photos: List<CloudPhotoAlbumModel>) : RecyclerView.Adapter<CloudPhotoPagerAdapter.PhotoViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PhotoViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_cloud_photo_detail, parent, false)
            return PhotoViewHolder(view)
        }

        override fun onBindViewHolder(holder: PhotoViewHolder, position: Int) {
            // 存储ViewHolder引用
            viewHolders[position] = holder

            val photo = photos[position]
            val displayMetrics = requireContext().resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val imageWidth = (screenWidth - 18.dp)

            holder.photoImageView.layoutParams.width = imageWidth
            holder.photoImageView.layoutParams.height = imageWidth

            // 使用ZoomableImageView的setImageSource方法
            holder.photoImageView.setImageSource(photo.photoAddress)
        }

        override fun getItemCount(): Int = photos.size

        private val viewHolders = mutableMapOf<Int, PhotoViewHolder>()
        inner class PhotoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val photoImageView: ZoomableImageView = itemView.findViewById(R.id.photoImageView)
        }
    }

}
