package online.yllh.smartinspect.view.gallery.cloud

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.navigation.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.databinding.FragmentPhotoAlbumBinding
import online.yllh.smartinspect.dialog.CreateGalleryDialog
import online.yllh.smartinspect.dialog.TipsDialog
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.network.model.response.CloudAlbumModel

class CloudAlbumFragment : BaseFragment() {
    private lateinit var binding: FragmentPhotoAlbumBinding
    private val albumName: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.AlbumName) ?: throw IllegalArgumentException("imageInfo is null")
    }
    private val parentAlbumId: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.ParentAlbumId) ?: throw IllegalArgumentException("imageInfo is null")
    }
    private val isImportMode: Boolean by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getBoolean(FragmentArguments.IsImportMode) ?: false
    }
    private val importModeAlbumId: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.ImportModeAlbumId).orEmpty()
    }
    private val projectId: Int? by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getInt(FragmentArguments.ProjectId)
    }
    private val viewModel by lazy { CloudAlbumViewModel() }
    private val adapter = AlbumAdapter(emptyList())

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPhotoAlbumBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        binding.appBar.setTitle(albumName)
        binding.appBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }
        if (projectId != null) {
            binding.addGallery.setOnClickListener {
                val dialog = CreateGalleryDialog(requireContext())
                dialog.setCancelable(false)
                dialog.setOkButtonListener {
                    viewModel.addProjectGallery(requireContext(), projectId?.toString()!!, parentAlbumId, it, binding.root)
                }
                dialog.show()
            }
        } else {
            binding.addGallery.visibility = View.GONE
        }

        binding.photoRecyclerView.adapter = adapter
        binding.photoRecyclerView.layoutManager = GridLayoutManager(requireContext(), 3)
        binding.importPhoto.visibility = View.GONE
        binding.info.visibility = View.GONE
        viewModel.getAlbumInfosByAlbumIds(requireContext(), parentAlbumId, binding.root)
        observeOn(viewModel.albums) {
            if (it.isNotEmpty()) {
                adapter.photoList = it
                adapter.notifyDataSetChanged()
            }
        }
    }

    inner class AlbumAdapter(var photoList: List<CloudAlbumModel>) : RecyclerView.Adapter<AlbumAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_album, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val photoUri = photoList[position]
            val context = holder.itemView.context
            val displayMetrics = context.resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val imageWidth = (screenWidth - 18.dp) / 3
            holder.photoImageView.layoutParams.width = imageWidth
            holder.photoImageView.layoutParams.height = imageWidth
            val requestOptions = RequestOptions()
                .format(DecodeFormat.PREFER_RGB_565) // 设置图片格式为 RGB_565，降低内存消耗
                .override(imageWidth, imageWidth)
            Glide.with(holder.itemView.context)
                .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                .load(photoUri.albumCover)
                .placeholder(R.drawable.placeholder)
                .apply(requestOptions)
                .into(holder.photoImageView)
            holder.title.text = photoUri.albumName
            holder.subTitle.text = ""
            holder.itemView.setOnClickListener {
                if (photoUri.palbumVOS == null) {
                    binding.root.findNavController().navigate(R.id.cloudPhotoAlbumFragment, Bundle().apply {
                        this.putSerializable(FragmentArguments.AlbumId, photoUri.id)
                        this.putSerializable(FragmentArguments.AlbumName, photoUri.albumName)
                        this.putSerializable(FragmentArguments.IsImportMode, isImportMode)
                        this.putSerializable(FragmentArguments.ImportModeAlbumId, importModeAlbumId)
                        this.putSerializable(FragmentArguments.ProjectId, projectId)

                    })
                } else {
                    binding.root.findNavController().navigate(R.id.cloudAlbumFragment, Bundle().apply {
                        this.putSerializable(FragmentArguments.ParentAlbumId, photoUri.id)
                        this.putSerializable(FragmentArguments.AlbumName, photoUri.albumName)
                        this.putSerializable(FragmentArguments.IsImportMode, isImportMode)
                        this.putSerializable(FragmentArguments.ImportModeAlbumId, importModeAlbumId)
                        this.putSerializable(FragmentArguments.ProjectId, photoUri.projectId)
                    })
                }
            }
            holder.delete.setOnClickListener {
                val dialog = TipsDialog(requireContext())
                dialog.setTitle("确定要删除此相册吗？删除操作不可还原")
                dialog.setCancelListener { }
                dialog.setOkListener {
                    viewModel.deleteGallery(requireContext(), photoUri.id.orEmpty(), parentAlbumId, binding.root)
                }
                dialog.show()
            }
        }

        override fun getItemCount(): Int {
            return photoList.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val photoImageView: ImageView = itemView.findViewById(R.id.imageView)
            val title: TextView = itemView.findViewById(R.id.itemTitle)
            val subTitle: TextView = itemView.findViewById(R.id.subTitle)
            val delete: FrameLayout = itemView.findViewById(R.id.delete)
        }
    }
}
