package online.yllh.smartinspect.view.project

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.edit
import androidx.fragment.app.viewModels
import androidx.navigation.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.google.gson.Gson
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentProjectListBinding
import online.yllh.smartinspect.dialog.FilterDialog
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.network.model.response.Project
import online.yllh.smartinspect.uiwidget.GradientButton

class ProjectListFragment : BaseFragment() {
    private lateinit var binding: FragmentProjectListBinding
    private val viewModel by viewModels<ProjectListViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ) = FragmentProjectListBinding.inflate(inflater, container, false).also { binding = it }.root

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        viewModel.getProjectList(requireContext(), binding.root)
        binding.appBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }
        setActionProject()
        val adapter = ProjectAdapter(emptyList())
        binding.projectList.adapter = adapter
        binding.projectList.layoutManager = LinearLayoutManager(requireContext())
        observeOn(viewModel.projectList) {
            if (it.isNotEmpty()) {
                binding.empty.visibility = View.INVISIBLE
                binding.projectList.visibility = View.VISIBLE
                adapter.setProjectList(it)
                adapter.notifyDataSetChanged()
            } else {
                adapter.setProjectList(it)
                binding.empty.visibility = View.VISIBLE
                binding.projectList.visibility = View.INVISIBLE
            }
        }
        val dialog = FilterDialog(requireContext())
        binding.filterButton.setOnClickListener {
            dialog.show()
        }
        binding.cleanFilter.setOnClickListener {
            viewModel.getProjectList(
                requireContext(),
                binding.root,
            )
        }
        dialog.okButtonClick = { name, onwer, status, startTime, endTime ->
            viewModel.getProjectList(
                requireContext(),
                binding.root,
                status = status.ifEmpty { null },
                initiatorName = onwer.ifEmpty { null },
                name = name.ifEmpty { null },
                createBeginTime = startTime.ifEmpty { null },
                createEndTime = endTime.ifEmpty { null }
            )
        }
    }

    fun setActionProject() {
        val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        val projectJson = sp.getString(SpKey.ActionProject, "").orEmpty()
        if (projectJson.isEmpty()) {
            binding.currentProject.text = "当前无开启任务"
        } else {
            val actionProject = Gson().fromJson(projectJson, Project::class.java)
            binding.currentProject.text = "当前开启任务: ${actionProject.name}"
        }

    }

    inner class ProjectAdapter(
        private var projectList: List<Project>
    ) : RecyclerView.Adapter<ViewHolder>() {
        fun setProjectList(projectList: List<Project>) {
            this.projectList = projectList
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
            val projectJson = sp.getString(SpKey.ActionProject, "").orEmpty()
            if (projectJson.isNotEmpty()) {
                val actionProject = Gson().fromJson(projectJson, Project::class.java)
                if (actionProject.id == projectList[position].id) {
                    holder.itemView.isSelected = true
                    holder.startProject.setOnClickListener {
                        sp.edit { putString(SpKey.ActionProject, "") }
                        setActionProject()
                        notifyDataSetChanged()
                    }
                    holder.startProject.setText("停止任务")
                } else {
                    holder.itemView.isSelected = false
                    holder.startProject.setText("开启任务")
                    holder.startProject.setOnClickListener {
                        sp.edit { putString(SpKey.ActionProject, Gson().toJson(projectList[position])) }
                        setActionProject()
                        notifyDataSetChanged()
                    }
                }
            } else {
                holder.itemView.isSelected = false
                holder.startProject.setText("开启任务")
                holder.startProject.setOnClickListener {
                    sp.edit { putString(SpKey.ActionProject, Gson().toJson(projectList[position])) }
                    setActionProject()
                    notifyDataSetChanged()
                }
            }
            holder.projectName.text = projectList[position].name
            holder.projectRequire.text = projectList[position].description
            holder.projectOnwer.text = projectList[position].initiatorName
            holder.projectStartTime.text = projectList[position].startTime
            holder.projectEndTime.text = projectList[position].endTime
            Glide.with(requireContext())
                .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                .load(projectList[position].cover)
                .placeholder(R.drawable.placeholder)
                .into(holder.coverImage)
            when (projectList[position].status) {
                "0" -> holder.projectStatus.text = "未开始"
                "1" -> holder.projectStatus.text = "进行中"
                "2" -> holder.projectStatus.text = "已结束"
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_project_layout, parent, false)
            return ViewHolder(view)
        }

        override fun getItemCount(): Int {
            return projectList.size
        }
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val projectName: TextView = itemView.findViewById(R.id.projectName)
        val projectRequire: TextView = itemView.findViewById(R.id.projectRequire)
        val projectOnwer: TextView = itemView.findViewById(R.id.projectOnwer)
        val projectStartTime: TextView = itemView.findViewById(R.id.projectStartTime)
        val projectEndTime: TextView = itemView.findViewById(R.id.projectEndTime)
        val projectStatus: TextView = itemView.findViewById(R.id.projectStatus)
        val coverImage: ImageView = itemView.findViewById(R.id.coverImage)
        val startProject: GradientButton = itemView.findViewById(R.id.startProject)
    }
}
