package online.yllh.smartinspect.view.gallery.cloud

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.exifinterface.media.ExifInterface
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.google.gson.Gson
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.FragmentArguments
import online.yllh.smartinspect.databinding.FragmentCloudPhotoExifDetailBinding
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.media.AudioPlayer
import online.yllh.smartinspect.model.MessageUIStatus
import online.yllh.smartinspect.network.model.response.Message
import online.yllh.smartinspect.uiwidget.ExitItem
import online.yllh.smartinspect.utils.DownloadUtils
import java.io.ByteArrayInputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID
import kotlin.math.pow


class CloudPhotoExifDetailFragment : BaseFragment() {
    private val viewModel by lazy { CloudPhotoExifDetailViewModel() }

    private lateinit var binding: FragmentCloudPhotoExifDetailBinding
    private val imageId: String by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(FragmentArguments.ImageId) ?: throw IllegalArgumentException("imageInfo is null")
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCloudPhotoExifDetailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(view)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .init()
        viewModel.getImageDetail(requireContext(), imageId, binding.root)

        binding.appTitleBar.setBackButtonClickListener {
            binding.root.findNavController().popBackStack()
        }
        observeOn(viewModel.imageByte) {
            Glide.with(requireContext())
                .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                .load(it)
                .placeholder(R.drawable.placeholder)
                .into(binding.mainImage)
            if (it != null) {
                getExifInfo(it)
            }
        }
        observeOn(viewModel.address) {
            binding.shutAddress.setDescription(it.orEmpty())
        }
        observeOn(viewModel.voiceList) {
            if (it != null) {
                val adapter = MessageAdapter(it.map { message ->
                    MessageUIStatus(message)
                })
                binding.voiceList.adapter = adapter
                binding.voiceList.layoutManager = LinearLayoutManager(requireContext())
            }
        }
        observeOn(viewModel.messageList) {
            if (it != null) {
                val adapter = MessageAdapter(it.map { message ->
                    MessageUIStatus(message)
                })
                binding.msgList.adapter = adapter
                binding.msgList.layoutManager = LinearLayoutManager(requireContext())
            }
        }
    }

    fun getExifInfo(byte: ByteArray) {
        val exif = ExifInterface(ByteArrayInputStream(byte))

        // 拍摄时间
        val dateTime = exif.getAttribute(ExifInterface.TAG_DATETIME)
        binding.shutTime.setDescription(convertDateTime(dateTime))
        Log.e("Exif", "拍摄时间：$dateTime")

        // 拍摄经度
        val latitude = exif.getAttribute(ExifInterface.TAG_GPS_LATITUDE)
        Log.e("Exif", "拍摄经度：${convertToDegrees(latitude)}")
        // 拍摄纬度
        val longitude = exif.getAttribute(ExifInterface.TAG_GPS_LONGITUDE)
        Log.e("Exif", "拍摄纬度：${convertToDegrees(longitude)}")
        viewModel.getAddress(convertToDegrees(latitude), convertToDegrees(longitude))

        val cameraMake = exif.getAttribute(ExifInterface.TAG_MAKE).orEmpty()
        binding.exifDevice.setDescription(cameraMake)
        val cameraModel = exif.getAttribute(ExifInterface.TAG_MODEL).orEmpty()
        binding.exifDeviceModel.setDescription(cameraModel)

        // 光圈大小
        val aperture = exif.getAttribute(ExifInterface.TAG_APERTURE_VALUE).orEmpty()
        binding.exifAperture.setDescription("ƒ ${String.format("%.1f", 1.4142.pow(convertFraction(aperture).toDouble()))}")
        Log.e("Exif", "光圈大小：ƒ ${convertFraction(aperture)}")

        // 焦距
        val focalLength = exif.getAttribute(ExifInterface.TAG_FOCAL_LENGTH_IN_35MM_FILM).orEmpty()
        binding.exiffocalLength.setDescription("$focalLength mm")
        Log.e("Exif", "焦距：${focalLength} mm")
        // 曝光补偿
        val exposureBias = exif.getAttribute(ExifInterface.TAG_EXPOSURE_BIAS_VALUE).orEmpty()
        binding.exifExposure.setDescription("${convertFraction(exposureBias)} EV")
        Log.e("Exif", "曝光补偿：${convertFraction(exposureBias)} EV")

        // 快门时间
        val shutterSpeed = exif.getAttribute(ExifInterface.TAG_SHUTTER_SPEED_VALUE).orEmpty()
        binding.exitShutterTime.setDescription(
            "1/${
                2.0.pow(convertFraction(shutterSpeed).toDouble()).toInt()
            } s"
        )
        Log.e("Exif", "快门时间：1/${2.0.pow(convertFraction(shutterSpeed).toDouble()).toInt()} s")

        // 感光度
        val iso = exif.getAttribute(ExifInterface.TAG_ISO_SPEED_RATINGS).orEmpty()
        binding.exifSensitivity.setDescription("ISO $iso")
        Log.e("Exif", "感光度：$iso")
        binding.addMessage.setOnClickListener {
            val mergedList: List<Message> = (viewModel.messageList.value.orEmpty()) + (viewModel.voiceList.value.orEmpty())
            binding.root.findNavController().navigate(R.id.action_cloudPhotoExifDetailFragment_to_photoMessageFragment, Bundle().apply {
                putSerializable(FragmentArguments.MessageList, Gson().toJson(mergedList))
                putSerializable(FragmentArguments.ImageId, imageId)
            })
//            val dialog = AddMessageDialog(requireContext(),mergedList.sortedByDescending { it.inputTime }.toMutableList())
//            dialog.setOkListener { it ->
//                viewModel.uploadImage(
//                    requireContext(),
//                    UploadImageToGallery(
//                        id = viewModel.imageId,
//                        photoAddress = viewModel.photoAddress,
//                        messages = it.map {message->
//                            ImageMessage(
//                                inputTime= message.inputTime?:"",
//                                type = message.type?:0,
//                                value = message.value?:""
//                            )
//                        }
//                    ),binding.root
//                )
//            }
//            dialog.setVoiceListener{messageList->
//                val recordingDialogFragment = AddVoiceMessageDialog(requireContext())
//                recordingDialogFragment.setOkListener {path, second->
//                    lifecycleScope.launch(Dispatchers.IO) {
//                        val url = PublicRequest.upLoadFile(File(path))
//                        Log.e("Upload",url?:"")
//                        messageList.add(Message(
//                            inputTime = getCurrentDateTime(),
//                            type = 1,
//                            value = url?:"",
//                            second = second))
//                        launch(Dispatchers.Main) {
//                            viewModel.uploadImage(
//                                requireContext(),
//                                UploadImageToGallery(
//                                    id = viewModel.imageId,
//                                    photoAddress = viewModel.photoAddress,
//                                    messages = messageList.map {message->
//                                        ImageMessage(
//                                            inputTime= message.inputTime?:"",
//                                            type = message.type?:0,
//                                            value = message.value?:"",
//                                            second = message.second?:0
//                                        )
//                                    }
//                                ),binding.root
//                            )
//                        }
//                    }
//                }
//                recordingDialogFragment.show(childFragmentManager,"AddVoiceMessageDialog")
//            }
//            dialog.setCancelListener {
//
//            }
//            dialog.show(childFragmentManager,"AddMessageDialog")
        }

    }

    private fun getCurrentDateTime(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return dateFormat.format(Date())
    }

    fun convertToDegrees(dmsString: String?): Float {
        if (dmsString.isNullOrEmpty()) return 0f
        val dms = dmsString.split(",").map { it.trim() }.toTypedArray()
        val degrees = dms[0].split("/").let { it[0].toFloat() / it[1].toFloat() }
        val minutes = dms[1].split("/").let { it[0].toFloat() / it[1].toFloat() / 60 }
        val seconds = dms[2].split("/").let { it[0].toFloat() / it[1].toFloat() / 3600 }
        return degrees + minutes + seconds
    }

    // 将分数表示的值转换为浮点数
    fun convertFraction(fractionString: String?): Float {
        if (fractionString.isNullOrEmpty()) return 0f
        val fraction = fractionString.split("/").map { it.toFloat() }
        return fraction[0] / fraction[1]
    }

    fun convertDateTime(dateTimeString: String?): String {
        if (dateTimeString.isNullOrEmpty()) return ""

        val inputFormat = SimpleDateFormat("yyyy:MM:dd HH:mm:ss", Locale.getDefault())
        val outputFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

        return try {
            val date = inputFormat.parse(dateTimeString)
            outputFormat.format(date)
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    inner class MessageAdapter(
        private val message: List<MessageUIStatus>
    ) : RecyclerView.Adapter<MessageAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_message, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val msg = message[position]
            if (msg.data.type == 0) {
                holder.messageItem.visibility = View.VISIBLE
                holder.voiceItem.visibility = View.GONE
                holder.messageItem.setDescription(msg.data.value.orEmpty())

                holder.messageItemDate.visibility = View.VISIBLE
                holder.voiceItem.visibility = View.GONE
                holder.messageItemDate.setDescription(msg.data.createTime.orEmpty())
            } else {
                holder.messageItem.visibility = View.GONE
                holder.voiceItem.visibility = View.VISIBLE
                holder.messageItemDate.setDescription(msg.data.createTime.orEmpty())
            }
            holder.second.text = "${msg.data.second}s"
            when (msg.status) {
                0 -> {
                    holder.stop.visibility = View.GONE
                    holder.play.visibility = View.VISIBLE
                    holder.progress.visibility = View.GONE
                }

                1 -> {
                    holder.stop.visibility = View.GONE
                    holder.play.visibility = View.GONE
                    holder.progress.visibility = View.VISIBLE
                }

                2 -> {
                    holder.stop.visibility = View.VISIBLE
                    holder.play.visibility = View.GONE
                    holder.progress.visibility = View.GONE
                }
            }
            holder.play.setOnClickListener {
                if (msg.data.type == 1) {
                    if (AudioPlayer.getInstance(requireContext()).isPlaying()) {
                        AudioPlayer.getInstance(requireContext()).stop()
                        message.forEach {
                            it.status = 0
                        }
                        notifyDataSetChanged()
                    }
                    msg.status = 1
                    notifyDataSetChanged()
                    lifecycleScope.launch(Dispatchers.IO) {
                        val bytes = msg.data.value?.let { it1 -> DownloadUtils.downloadFile(it1) }
                        if (bytes != null) {
                            val file = saveBytesToCache(bytes, "${UUID.randomUUID().toString()}", requireContext())
                            launch(Dispatchers.Main) {
                                msg.status = 2
                                notifyDataSetChanged()
                                AudioPlayer.getInstance(requireContext()).play(file?.path.orEmpty()) {
                                    msg.status = 0
                                    notifyDataSetChanged()
                                }
                            }
                        }
                    }
                }
            }
            holder.stop.setOnClickListener {
                AudioPlayer.getInstance(requireContext()).stop()
                msg.status = 0
                notifyDataSetChanged()
            }
        }

        override fun getItemCount(): Int {
            return message.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val play: ImageView = itemView.findViewById(R.id.play)
            val stop: ImageView = itemView.findViewById(R.id.stop)
            val progress: ProgressBar = itemView.findViewById(R.id.progress)
            val messageItem: ExitItem = itemView.findViewById(R.id.messageItem)
            val messageItemDate: ExitItem = itemView.findViewById(R.id.messageItemDate)
            val voiceItem: LinearLayout = itemView.findViewById(R.id.voiceItem)
            val second: TextView = itemView.findViewById(R.id.second)
        }
    }

    fun saveBytesToCache(bytes: ByteArray, fileName: String, context: Context): File? {
        val cacheDir = context.cacheDir
        val file = File(cacheDir, fileName)

        try {
            FileOutputStream(file).use { outputStream ->
                outputStream.write(bytes)
            }
            return file
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
    }

    override fun onPause() {
        super.onPause()
        AudioPlayer.getInstance(requireContext()).stop()
    }
}

