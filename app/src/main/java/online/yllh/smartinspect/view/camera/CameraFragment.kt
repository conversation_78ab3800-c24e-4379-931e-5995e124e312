package online.yllh.smartinspect.view.camera

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.SurfaceTexture
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.content.edit
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import com.bumptech.glide.Glide
import com.google.gson.Gson
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.tapapk.camera_engine.core.FlashMode
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.GlobalVals
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentCameraBinding
import online.yllh.smartinspect.dialog.AddContentDialog
import online.yllh.smartinspect.dialog.ChooseAlbumDialog
import online.yllh.smartinspect.dialog.LoadingDialog
import online.yllh.smartinspect.extension.dpF
import online.yllh.smartinspect.extension.hideKeyboard
import online.yllh.smartinspect.extension.isNetworkAvailable
import online.yllh.smartinspect.extension.screenWidth
import online.yllh.smartinspect.model.AddressDataModel
import online.yllh.smartinspect.model.AlbumTree
import online.yllh.smartinspect.model.SnapshotModel
import online.yllh.smartinspect.network.PublicRequest
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.request.UploadImageToGallery
import online.yllh.smartinspect.network.model.response.Project
import online.yllh.smartinspect.provider.ApplicationContextProvider
import online.yllh.smartinspect.sticker.EmptySticker
import online.yllh.smartinspect.sticker.PhotoExtraInfoSticker
import online.yllh.smartinspect.sticker.StickerManager
import online.yllh.smartinspect.uiwidget.sticker.StickerGalleryFragment
import online.yllh.smartinspect.uiwidget.sticker.StickerPanel
import online.yllh.smartinspect.utils.CompassSensorHelper
import pub.devrel.easypermissions.EasyPermissions
import retrofit2.awaitResponse
import java.io.File
import java.io.FileOutputStream

class CameraFragment : BaseFragment() {

    private lateinit var binding: FragmentCameraBinding
    private val viewModel by activityViewModels<CameraViewModel>()
    private var maskView: View? = null
    private var previewSurface: SurfaceTexture? = null
    private var previewWidth: Int = 0
    private var previewHeight: Int = 0
    private val compassHelper by lazy { CompassSensorHelper(requireContext()) }

    companion object {
        suspend fun albumTrees(projectId: String): List<AlbumTree> {
            val albums = withContext(Dispatchers.IO) {
                try {
                    val response = RetrofitClient.service.getProjAlbumsByProjId(projectId)
                    if (response.ok) {
                        response.data
                    } else {
                        withContext(Dispatchers.Main) {
                            Toast.makeText(ApplicationContextProvider.context, response.msg, Toast.LENGTH_SHORT).show()
                        }
                        listOf()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    withContext(Dispatchers.Main) {
                        Toast.makeText(ApplicationContextProvider.context, "相册获取失败", Toast.LENGTH_SHORT).show()
                    }
                    listOf()
                }
            }
            return albums
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? = FragmentCameraBinding.inflate(inflater, container, false).also { binding = it }.root

    private val textureViewListener = object : TextureView.SurfaceTextureListener {
        override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
            previewWidth = width
            previewHeight = height
            previewSurface = surface
            viewModel.setSurfaceTextureAvailable(true)
            viewModel.checkPermission(this@CameraFragment)
        }

        override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {
            viewModel.surfaceSizeChange(width, height)
        }

        override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
            viewModel.destroyCameraEnvironment()
            viewModel.setSurfaceTextureAvailable(false)
            return true
        }

        override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
        }
    }

    private val backPressedCallback = object : OnBackPressedCallback(false) {
        override fun handleOnBackPressed() {
            if (binding.stickerPanel.showing)
                binding.stickerPanel.hide()
            if (binding.addressLevelPanel.showing) {
                binding.addressLevelPanel.hide()
            } else if (binding.addressSelectPanel.showing) {
                binding.addressSelectPanel.hide()
            } else if (binding.editWatermarkPanel.showing) {
                binding.editWatermarkPanel.hide()
            }
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, viewModel)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ImmersionBar.with(this)
            .fullScreen(true)
            .titleBarMarginTop(binding.linearLayout)
            .hideBar(BarHide.FLAG_HIDE_BAR)
            .init()
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, backPressedCallback)
        binding.cameraPreviewView.surfaceTextureListener = textureViewListener
        viewModel.checkProjectList(requireContext())
        val layoutParams = binding.cameraPreviewView.layoutParams
        val previewWidth = requireContext().screenWidth
        val previewHeight = previewWidth * 4 / 3

        layoutParams.width = previewWidth
        layoutParams.height = previewHeight
        initStickerPanel()
        binding.cameraPreviewView.layoutParams = layoutParams
        binding.focalLength.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                val location = IntArray(2)
                binding.focalLength.getLocationOnScreen(location)
                val x = location[0]
                binding.focalLength.viewTreeObserver.removeOnGlobalLayoutListener(this)
                binding.bubbleView.offset = x.toFloat() + 6.dpF - 14.dpF
                binding.bubbleView.invalidate()
            }
        })

        binding.countDown.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                val location = IntArray(2)
                binding.countDown.getLocationOnScreen(location)
                val x = location[0]
                binding.countDown.viewTreeObserver.removeOnGlobalLayoutListener(this)
                binding.countDownBubbleView.offset = x.toFloat() + 6.dpF - 14.dpF
                binding.countDownBubbleView.invalidate()
            }
        })
        binding.countDown.setOnClickListener {
            binding.countDownModeSelectView.visibility = View.VISIBLE
            maskView = View(requireContext())
            val index: Int = binding.parentConstraintLayout.indexOfChild(binding.countDownModeSelectView)
            binding.parentConstraintLayout.addView(maskView, index)
            val layoutParams = maskView?.layoutParams as ConstraintLayout.LayoutParams
            layoutParams.width = ConstraintLayout.LayoutParams.MATCH_PARENT
            layoutParams.height = ConstraintLayout.LayoutParams.MATCH_PARENT
            maskView?.layoutParams = layoutParams
            maskView?.setOnClickListener {
                binding.countDownModeSelectView.visibility = View.GONE
                binding.parentConstraintLayout.removeViewAt(index)
                maskView = null
            }
        }
        binding.flash.setOnClickListener {
            viewModel.switchFlash()
        }

        binding.macro.setOnClickListener {
            viewModel.switchFocalMode(FocalMode.Macro)
        }

        binding.longFocal.setOnClickListener {
            viewModel.switchFocalMode(FocalMode.Long)
        }

        binding.standard.setOnClickListener {
            viewModel.switchFocalMode(FocalMode.Standard)
        }

        binding.wideAngle.setOnClickListener {
            viewModel.switchFocalMode(FocalMode.Wide)
        }

        binding.countDownOff.setOnClickListener {
            viewModel.switchCountDownMode(CountDownMode.OFF)
            binding.countDownModeSelectView.visibility = View.GONE
            maskView?.let {
                val index = binding.parentConstraintLayout.indexOfChild(it)
                binding.parentConstraintLayout.removeViewAt(index)
                maskView = null
            }
        }

        binding.countDown3s.setOnClickListener {
            viewModel.switchCountDownMode(CountDownMode.THREE_SECONDS)
            binding.countDownModeSelectView.visibility = View.GONE
            maskView?.let {
                val index = binding.parentConstraintLayout.indexOfChild(it)
                binding.parentConstraintLayout.removeViewAt(index)
                maskView = null
            }
        }

        binding.countDown5s.setOnClickListener {
            viewModel.switchCountDownMode(CountDownMode.FIVE_SECONDS)
            binding.countDownModeSelectView.visibility = View.GONE
            maskView?.let {
                val index = binding.parentConstraintLayout.indexOfChild(it)
                binding.parentConstraintLayout.removeViewAt(index)
                maskView = null
            }
        }

        binding.countDown10s.setOnClickListener {
            viewModel.switchCountDownMode(CountDownMode.TEN_SECONDS)
            binding.countDownModeSelectView.visibility = View.GONE
            maskView?.let {
                val index = binding.parentConstraintLayout.indexOfChild(it)
                binding.parentConstraintLayout.removeViewAt(index)
                maskView = null
            }
        }

        binding.switchCamera.setOnClickListener {
            viewModel.switchCamera()
        }

        binding.location.setOnClickListener {
            viewModel.checkLocation(this)
            // 点击地点，默认添加一个水印
            val stickerList = viewModel.recentStickerAdapter?.stickerList
            if (!viewModel.isShowWatermark.value && !stickerList.isNullOrEmpty()) {
                val sticker = stickerList.first()
                viewModel.selectListener.invoke(sticker.stickerId)
            }
            binding.addressSelectPanel.show(
                AddressDataModel(
                    country = viewModel.location.value.country,
                    province = viewModel.location.value.province,
                    city = viewModel.location.value.city,
                    district = viewModel.location.value.district,
                    street = viewModel.location.value.street,
                    town = viewModel.location.value.town,
                    poiList = viewModel.location.value.poiList,
                )
            )
            backPressedCallback.isEnabled = true
        }

        binding.snapshotButton.setOnClickListener {
            if (viewModel.isSnapshotInProgress.value ||
                (viewModel.cameraCountDownEnable.value && viewModel.cameraCountDown.value != 0)
            ) {
                return@setOnClickListener
            }

            viewModel.snapshot(requireContext()) { path, width, height ->
                if (it != null) {
                    val networkAvailable = requireContext().isNetworkAvailable()
                    val showProject = viewModel.isShowProjectView.value && viewModel.projectId.value.isNotEmpty()
                    if (showProject && networkAvailable) {
                        showAlbumSelectDialog(viewModel.projectId.value) {
                            showUploadDialog(it, path)
                        }
                    } else {
                        if (showProject) {
                            Toast.makeText(requireContext(), "离线模式下无法自动同步照片！", Toast.LENGTH_LONG).show()
                        }
                        viewModel.snapshot = SnapshotModel(path, width, height, viewModel.watermarkState.value.copy())
                        binding.root.findNavController().navigate(R.id.action_cameraFragment_to_snapshotResultFragment)
                    }
                }
            }
        }

        binding.focalLength.setOnClickListener {
            binding.focalModeSelectView.visibility = View.VISIBLE
            maskView = View(requireContext())
            val index: Int = binding.parentConstraintLayout.indexOfChild(binding.focalModeSelectView)
            binding.parentConstraintLayout.addView(maskView, index)
            val layoutParams = maskView?.layoutParams as ConstraintLayout.LayoutParams
            layoutParams.width = ConstraintLayout.LayoutParams.MATCH_PARENT
            layoutParams.height = ConstraintLayout.LayoutParams.MATCH_PARENT
            maskView?.layoutParams = layoutParams
            maskView?.setOnClickListener {
                binding.focalModeSelectView.visibility = View.GONE
                binding.parentConstraintLayout.removeViewAt(index)
                maskView = null
            }
        }

        binding.my.setOnClickListener {
            val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
            val userCookies = sp.getString(SpKey.UserCookies, "")
            if (userCookies?.isEmpty() == false) {
                binding.root.findNavController().navigate(R.id.action_cameraFragment_to_myFragment)
            } else {
                binding.root.findNavController().navigate(R.id.action_cameraFragment_to_loginFragment)
            }
        }

        binding.setting.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_cameraFragment_to_settingFragment)
        }

        binding.gallery.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_cameraFragment_to_galleryFragment)
        }

        binding.projectList.setOnClickListener {
            binding.root.findNavController().navigate(R.id.action_cameraFragment_to_projectListFragment)
        }

        binding.watermark.setOnClickListener {
            binding.stickerPanel.show()
            backPressedCallback.isEnabled = true
        }

        binding.projectNameLayout.setOnClickListener {
            viewModel.isShowProjectView.value = !binding.projectNameLayout.isSelected
        }
        binding.addressSelectPanel.lifecycleOwner = viewLifecycleOwner
        binding.addressSelectPanel.hideListener = {
            if (!binding.editWatermarkPanel.showing)
                backPressedCallback.isEnabled = false
        }
        binding.addressSelectPanel.showLevelListener = {
            view.hideKeyboard()
            binding.addressLevelPanel.show(
                AddressDataModel(
                    country = viewModel.location.value.country,
                    province = viewModel.location.value.province,
                    city = viewModel.location.value.city,
                    district = viewModel.location.value.district,
                    street = viewModel.location.value.street,
                    town = viewModel.location.value.town,
                )
            )
        }
        binding.addressSelectPanel.selectAddressListener = {
            requireActivity().supportFragmentManager.setFragmentResult(
                "AddressFragment", Bundle().apply { putString("address", it) }
            )
        }
        binding.addressLevelPanel.onLocationLevelChanged = {
            binding.addressSelectPanel.updateAddressType()
            requireActivity().supportFragmentManager.setFragmentResult(
                "AddressFragment", Bundle().apply { putString("addressLevel", it) }
            )
        }
        binding.addressLevelPanel.onEnableLevelChanged = {
            binding.addressSelectPanel.onEnableLevelChanged(it)
            requireActivity().supportFragmentManager.setFragmentResult(
                "AddressFragment", Bundle().apply { putInt("enableLevel", if (it) 1 else 0) }
            )
        }
        binding.editWatermarkPanel.init(viewLifecycleScope, viewModel)
        binding.editWatermarkPanel.onHide = { backPressedCallback.isEnabled = false }
        binding.editWatermarkPanel.onAddressClick = {
            binding.addressSelectPanel.show(
                AddressDataModel(
                    country = viewModel.location.value.country,
                    province = viewModel.location.value.province,
                    city = viewModel.location.value.city,
                    district = viewModel.location.value.district,
                    street = viewModel.location.value.street,
                    town = viewModel.location.value.town,
                    poiList = viewModel.location.value.poiList,
                )
            )
        }

        observeOn(viewModel.projectRemark) {
            setSticker()
        }

        observeOn(viewModel.projectName) {
            setSticker()
        }

        val sharedPreferences = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        val projectJson = sharedPreferences.getString(SpKey.ActionProject, "").orEmpty()
        if (projectJson.isNotEmpty()) {
            val actionProject = Gson().fromJson(projectJson, Project::class.java)
            binding.projectNameLayout.visibility = View.VISIBLE
            val projectName = actionProject.name.orEmpty()
            binding.projectNameText.text = projectName
            viewModel.isShowProjectView.value = true
            viewModel.projectName.value = projectName
            viewModel.projectId.value = actionProject.id.orEmpty()
        } else {
            binding.projectNameLayout.visibility = View.INVISIBLE
            viewModel.isShowProjectView.value = false
            viewModel.projectName.value = ""
            viewModel.projectId.value = ""
        }
        observeOn(viewModel.actionProjectJsonUiStatus) {
            if (it.isNotEmpty()) {
                val actionProject = Gson().fromJson(it, Project::class.java)
                val projectName = actionProject.name.orEmpty()
                binding.projectNameLayout.visibility = View.VISIBLE
                binding.projectNameText.text = projectName
                viewModel.isShowProjectView.value = true
                viewModel.projectName.value = projectName
                viewModel.projectId.value = actionProject.id.orEmpty()
            } else {
                binding.projectNameLayout.visibility = View.INVISIBLE
                viewModel.isShowProjectView.value = false
                viewModel.projectName.value = ""
                viewModel.projectId.value = ""
            }
        }

        observeOn(viewModel.isShowProjectView) {
            binding.projectNameLayout.isSelected = it
            setSticker()
        }

        observeOn(viewModel.currentSticker) {
            setSticker()
            viewModel.recentStickerAdapter?.stickerList = StickerManager.getRecentlyStickerList(requireContext())
            viewModel.setCurrentSticker(it)
        }
        observeOn(viewModel.currentStickerObj) {
            val stickerRect = it.getStickerRect()
            val projectRemarkRect = it.getProjectRemarkRect()
            binding.cameraPreviewView.setOnTouchListener { v, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        Log.e("Sticker", "点击事件触发 x: ${event.x} y:${event.y}")
                        Log.e("Sticker", "$stickerRect")
                        if (stickerRect.contains(event.x, event.y)) {
                            if (viewModel.currentSticker.value == "dateAndLocationSticker") {
                                binding.editWatermarkPanel.show()
                                backPressedCallback.isEnabled = true
                            }
                        } else if (projectRemarkRect.contains(event.x, event.y)) {
                            val dialog = AddContentDialog(viewModel.projectRemark.value)
                            dialog.setOkListener {
                                viewModel.projectRemark.value = if (it == "") "输入内容" else it
                            }
                            dialog.setCancelListener {}
                            dialog.show(childFragmentManager, "AddContentDialog")
                        }
                    }
                }
                v.performClick()
            }
        }

        binding.stickerPanel.hideListener = {
            backPressedCallback.isEnabled = false
        }

        observeOn(viewModel.watermarkState) {
            setSticker()
        }

        observeOn(viewModel.cameraIsReady, state = Lifecycle.State.CREATED) {
            Log.d("kofua", "cameraIsReady: $it")
            if (it) {
                val result = viewModel.initCameraEnvironment(requireContext(), previewSurface!!, previewWidth, previewHeight)
                if (result) {
                    viewModel.checkLocation(this)
                    setSticker()
                    viewModel.setCurrentSticker(viewModel.currentSticker.value)
                }
            }
        }

        observeOn(viewModel.cameraCountDownEnable) {
            if (it) {
                binding.countDown.setImageDrawable(ContextCompat.getDrawable(requireContext(), R.drawable.ic_count_down))
            } else {
                binding.countDown.setImageDrawable(ContextCompat.getDrawable(requireContext(), R.drawable.ic_count_down_off))
            }
        }

        observeOn(viewModel.countDownMode) {
            // 更新倒计时选择状态
            binding.countDownOff.isSelected = it == CountDownMode.OFF
            binding.countDown3s.isSelected = it == CountDownMode.THREE_SECONDS
            binding.countDown5s.isSelected = it == CountDownMode.FIVE_SECONDS
            binding.countDown10s.isSelected = it == CountDownMode.TEN_SECONDS

            // 更新倒计时按钮图标
            val iconRes = when (it) {
                CountDownMode.OFF -> R.drawable.ic_count_down_off
                CountDownMode.THREE_SECONDS -> R.drawable.ic_count_down_3s
                CountDownMode.FIVE_SECONDS -> R.drawable.ic_count_down_5s
                CountDownMode.TEN_SECONDS -> R.drawable.ic_count_down_10s
            }
            binding.countDown.setImageDrawable(ContextCompat.getDrawable(requireContext(), iconRes))
        }

        observeOn(viewModel.cameraFlashMode) {
            if (it == FlashMode.OFF) {
                binding.flash.setImageDrawable(ContextCompat.getDrawable(requireContext(), R.drawable.ic_flash_off))
            } else if (it == FlashMode.ON) {
                binding.flash.setImageDrawable(ContextCompat.getDrawable(requireContext(), R.drawable.ic_flash))
            }
        }

        observeOn(viewModel.focalMode) {
            if (it == FocalMode.Macro) {
                binding.macro.isSelected = true
                binding.longFocal.isSelected = false
                binding.standard.isSelected = false
                binding.wideAngle.isSelected = false
            } else if (it == FocalMode.Long) {
                binding.macro.isSelected = false
                binding.longFocal.isSelected = true
                binding.standard.isSelected = false
                binding.wideAngle.isSelected = false
            } else if (it == FocalMode.Standard) {
                binding.macro.isSelected = false
                binding.longFocal.isSelected = false
                binding.standard.isSelected = true
                binding.wideAngle.isSelected = false
            } else if (it == FocalMode.Wide) {
                binding.macro.isSelected = false
                binding.longFocal.isSelected = false
                binding.standard.isSelected = false
                binding.wideAngle.isSelected = true
            }
        }

        observeOn(viewModel.cameraCountDown) {
            if (viewModel.cameraCountDown.value < 1) {
                binding.countDownText.visibility = View.GONE
            } else {
                binding.countDownText.visibility = View.VISIBLE
            }
            binding.countDownText.text = it.toString()
        }

        val latestPhoto = ImageUtil.getLatestPhoto(requireContext())
        // 检查是否获取到了照片
        latestPhoto?.let { (_, path) ->
            // 使用Picasso加载图片
            Glide.with(this)
                .load(path)
                .into(binding.gallery.imageView)
        }

        observeOn(viewModel.cameraLocationPermissionIsReady, state = Lifecycle.State.CREATED) {
            if (it && !viewModel.located.value)
                viewModel.startLocating()
        }
        observeOn(viewModel.located, state = Lifecycle.State.CREATED) {
        }
        observeOn(compassHelper.azimuth) {
            viewModel.watermarkState.value = viewModel.watermarkState.value.copy(azimuth = it)
        }

        observeOn(viewModel.isSnapshotInProgress) { isInProgress ->
            binding.snapshotButton.isEnabled = !isInProgress
            binding.snapshotButton.alpha = if (isInProgress) 0.5f else 1.0f
        }
        requireActivity().supportFragmentManager.setFragmentResultListener(
            "AddressFragment", viewLifecycleOwner
        ) { _, bundle ->
            val result = bundle.getString("address", "")
            if (result.isNotEmpty()) {
                viewModel.updateWatermarkAddress(result)
            }
            val enableLevel = bundle.getInt("enableLevel", -1)
            val addressLevel = bundle.getString("addressLevel", "")
            if (enableLevel != -1 || addressLevel.isNotEmpty()) {
                viewModel.refreshLocationLevel()
            }
        }

        val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        val token = sp.getString(SpKey.UserCookies, "").orEmpty()
        if (token.isEmpty()) {
            binding.projectListView.visibility = View.INVISIBLE
        } else {
            binding.projectListView.visibility = View.VISIBLE
        }

        lifecycleScope.launch {
            GlobalVals.networkStateEvent.collect { isNetworkAvailable ->
                Log.d("kofua", "onNetworkStateEvent: $isNetworkAvailable")
                delay(500)
                setSticker()
                if (isNetworkAvailable && !viewModel.located.value) {
                    viewModel.stopLocating()
                    delay(500)
                    viewModel.startLocating()
                }
            }
        }
    }

    private fun showAlbumSelectDialog(projectId: String, onSelect: (String) -> Unit) = lifecycleScope.launch {
        val albums = albumTrees(projectId)
        if (albums.isEmpty()) {
            openCamera()
            return@launch
        }

        val sharedPreferences = requireContext().getSharedPreferences(SpKey.PREFS_NAME, Context.MODE_PRIVATE)
        val cachedAlbumId = sharedPreferences.getLong(ChooseAlbumDialog.KEY_SELECTED_ALBUM_SNAPSHOT_ID, -1)

        ChooseAlbumDialog(requireContext(), albums, cachedAlbumId) {
            onSelect(it.id.toString())
        }.apply {
            setOnCancelListener { openCamera() }
        }.show()
    }

    private fun showUploadDialog(albumId: String, path: String) {
        val dialog = LoadingDialog(requireContext())
        dialog.setTitle("上传中")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // 生成带水印的图片用于上传
                val watermarkedImagePath = createWatermarkedImage(path)
                val uploadPath = watermarkedImagePath ?: path // 如果生成失败，使用原图
                
                val url = PublicRequest.uploadFile(File(uploadPath))
                if (url.isNullOrEmpty()) {
                    withContext(Dispatchers.Main) {
                        Toast.makeText(context, "上传失败", Toast.LENGTH_SHORT).show()
                        dialog.dismiss()
                        openCamera()
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        dialog.dismiss()
                        uploadImage(url, albumId)
                    }
                }
                
                // 清理临时的水印图片文件
                if (watermarkedImagePath != null && watermarkedImagePath != path) {
                    try {
                        File(watermarkedImagePath).delete()
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "处理图片失败", Toast.LENGTH_SHORT).show()
                    dialog.dismiss()
                    openCamera()
                }
            }
        }
    }

    /**
     * 创建带水印的图片用于上传
     * @param originalImagePath 原始图片路径
     * @return 带水印图片的路径，如果创建失败返回null
     */
    private suspend fun createWatermarkedImage(originalImagePath: String): String? {
        return withContext(Dispatchers.IO) {
            try {
                // 读取原始图片
                val originalBitmap = BitmapFactory.decodeFile(originalImagePath) ?: return@withContext null
                
                // 检查是否需要添加水印
                val shouldAddWatermark = viewModel.isShowWatermark.value || 
                    (viewModel.currentSticker.value == "dateAndLocationSticker")
                
                if (!shouldAddWatermark) {
                    return@withContext null // 不需要水印，返回null使用原图
                }
                
                // 创建水印贴纸
                val watermarkSticker = PhotoExtraInfoSticker(
                    showWatermark = viewModel.isShowWatermark.value,
                    showProject = viewModel.isShowProjectView.value,
                    projectName = viewModel.projectName.value,
                    projectRemark = viewModel.projectRemark.value,
                    watermarkState = viewModel.watermarkState.value
                )
                
                // 生成水印bitmap
                val watermarkBitmap = watermarkSticker.createBitmap(
                    originalBitmap.width, 
                    originalBitmap.height
                )
                
                // 合成图片：将水印叠加到原图上
                val resultBitmap = Bitmap.createBitmap(
                    originalBitmap.width,
                    originalBitmap.height,
                    Bitmap.Config.ARGB_8888
                )
                
                val canvas = Canvas(resultBitmap)
                // 先绘制原图
                canvas.drawBitmap(originalBitmap, 0f, 0f, null)
                // 再绘制水印
                canvas.drawBitmap(watermarkBitmap, 0f, 0f, null)
                
                // 保存带水印的图片到临时文件
                val cacheDir = File(requireContext().cacheDir, "watermarked")
                if (!cacheDir.exists()) {
                    cacheDir.mkdirs()
                }
                
                val watermarkedFile = File(cacheDir, "watermarked_${System.currentTimeMillis()}.jpg")
                val outputStream = FileOutputStream(watermarkedFile)
                resultBitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                outputStream.close()
                
                // 清理bitmap资源
                originalBitmap.recycle()
                watermarkBitmap.recycle()
                resultBitmap.recycle()
                
                return@withContext watermarkedFile.absolutePath
                
            } catch (e: Exception) {
                e.printStackTrace()
                return@withContext null
            }
        }
    }

    fun uploadImage(imageUrl: String, albumId: String) {
        val dialog = LoadingDialog(requireContext())
        dialog.setTitle("正在同步云相册")
        dialog.setButtonVisibility(View.GONE)
        dialog.setCancelable(false)
        dialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val request = RetrofitClient.service.saveImageToCloudAlbum(
                    UploadImageToGallery(photoAddress = imageUrl, albumId = albumId)
                )
                val response = request.awaitResponse()
                launch(Dispatchers.Main) {
                    if (response.isSuccessful) {
                        val body = response.body()
                        if (body?.data != null && body.ok) {
                            Toast.makeText(context, "同步成功", Toast.LENGTH_SHORT).show()
                        } else {
                            Toast.makeText(context, body?.msg ?: "同步失败 请重新上传", Toast.LENGTH_SHORT).show()
                        }
                        openCamera()
                    } else {
                        if (response.code() == 401) {
                            val sp = ApplicationContextProvider.context
                                .getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                            sp.edit { putString(SpKey.UserCookies, "") }
                            binding.root.findNavController().navigate("loginFragment")
                            Toast.makeText(context, "登录过期，请重新登录", Toast.LENGTH_SHORT).show()
                        } else {
                            openCamera()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                launch(Dispatchers.Main) {
                    Toast.makeText(context, "网络连接失败 请重新上传", Toast.LENGTH_SHORT).show()
                    openCamera()
                }
            } finally {
                launch(Dispatchers.Main) {
                    dialog.dismiss()
                }
            }
        }
    }

    private fun setSticker() {
        if (previewWidth == 0 || previewHeight == 0) {
            return
        }
        val sp = requireContext().getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        val recentlySticker = sp.getString(SpKey.RecentlySticker, "").orEmpty()
        val gson = Gson()
        if (recentlySticker.isNotEmpty()) {
            var list = gson.fromJson<List<String>>(recentlySticker, List::class.java)
            if (!list.contains(viewModel.currentSticker.value)) {
                list = list.plus(viewModel.currentSticker.value)
            }
            sp.edit { putString(SpKey.RecentlySticker, gson.toJson(list)) }
        } else {
            val list = listOf(viewModel.currentSticker.value)
            sp.edit { putString(SpKey.RecentlySticker, gson.toJson(list)) }
        }

        if (viewModel.currentSticker.value == "dateAndLocationSticker") {
            viewModel.isShowWatermark.value = true
            viewModel.setSticker(
                PhotoExtraInfoSticker(
                    true,
                    viewModel.isShowProjectView.value,
                    viewModel.projectName.value,
                    viewModel.projectRemark.value,
                    viewModel.watermarkState.value,
                ), previewWidth, previewHeight
            )
        } else if (viewModel.isShowProjectView.value) {
            viewModel.isShowWatermark.value = false
            viewModel.setSticker(
                PhotoExtraInfoSticker(
                    showWatermark = false,
                    showProject = true,
                    projectName = viewModel.projectName.value,
                    projectRemark = viewModel.projectRemark.value,
                    watermarkState = viewModel.watermarkState.value,
                ), previewWidth, previewHeight
            )
        } else {
            viewModel.isShowWatermark.value = false
            viewModel.setSticker(EmptySticker(), previewWidth, previewHeight)
        }
    }

    private fun initStickerPanel() {
        if (viewModel.workStickerAdapter == null) {
            viewModel.workStickerAdapter = StickerGalleryFragment.StickerAdapter(StickerManager.stickerList, {
                binding.stickerPanel.hide()
                viewModel.selectListener.invoke(it)
            }, viewModel.currentSticker.value)
        }
        if (viewModel.recentStickerAdapter == null) {
            viewModel.recentStickerAdapter = StickerGalleryFragment.StickerAdapter(StickerManager.getRecentlyStickerList(requireContext()), {
                binding.stickerPanel.hide()
                viewModel.selectListener.invoke(it)
            }, viewModel.currentSticker.value)
        }
        if (viewModel.stickerPanelAdapter == null) {
            viewModel.stickerPanelAdapter = StickerPanel.StickerPanelAdapter(
                childFragmentManager, viewModel.workStickerAdapter!!, viewModel.recentStickerAdapter!!
            )
        }
        binding.stickerPanel.initAdapter(viewModel.stickerPanelAdapter!!) {
            viewModel.selectListener.invoke(it)
        }
    }

    private fun openCamera(resumed: Boolean = false) {
        if (viewModel.cameraIsReady.value && (resumed || lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED))) {
            viewModel.cameraEngineCore.openCamera()
            viewModel.switchFocalMode(viewModel.focalMode.value)
        }
    }

    override fun onResume() {
        super.onResume()
        Log.i("onResume", "onResume!!!")
        openCamera(true)
        compassHelper.start()
    }

    override fun onPause() {
        super.onPause()
        compassHelper.stop()
        Log.i("onPause", "onPause!!!")
        if (viewModel.cameraIsReady.value) {
            Log.i("onPause", "onPause!!! closeCamera")
            viewModel.cameraEngineCore.closeCamera()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.cameraEngineCore.destroy()
        viewModel.setSurfaceTextureAvailable(false)
    }
}
