package online.yllh.smartinspect.model


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

data class WeatherResults(
    @SerializedName("results")
    val results: List<Result> = listOf()
) {
    data class Result(
        @SerializedName("location")
        val location: Location = Location(),
        @SerializedName("now")
        val now: Now = Now(),
        @SerializedName("last_update")
        val lastUpdate: String = "" // 2025-07-03T22:40:42+08:00
    ) {
        data class Location(
            @SerializedName("id")
            val id: String = "", // WM649VRDWVKD
            @SerializedName("name")
            val name: String = "", // 仁寿
            @SerializedName("country")
            val country: String = "", // CN
            @SerializedName("path")
            val path: String = "", // 仁寿,眉山,四川,中国
            @SerializedName("timezone")
            val timezone: String = "", // Asia/Shanghai
            @SerializedName("timezone_offset")
            val timezoneOffset: String = "" // +08:00
        )

        @Parcelize
        data class Now(
            @SerializedName("text")
            val text: String = "", // 阴
            @SerializedName("code")
            val code: String = "", // 9
            @SerializedName("temperature")
            val temperature: String = "", // 24
            @SerializedName("feels_like")
            val feelsLike: String = "", // 28
            @SerializedName("pressure")
            val pressure: String = "", // 953
            @SerializedName("humidity")
            val humidity: String = "", // 96
            @SerializedName("visibility")
            val visibility: String = "", // 3.3
            @SerializedName("wind_direction")
            val windDirection: String = "", // 西北
            @SerializedName("wind_direction_degree")
            val windDirectionDegree: String = "", // 324
            @SerializedName("wind_speed")
            val windSpeed: String = "", // 10.0
            @SerializedName("wind_scale")
            val windScale: String = "", // 2
            @SerializedName("clouds")
            val clouds: String = "", // 50
            @SerializedName("dew_point")
            val dewPoint: String = ""
        ) : Parcelable
    }
}
