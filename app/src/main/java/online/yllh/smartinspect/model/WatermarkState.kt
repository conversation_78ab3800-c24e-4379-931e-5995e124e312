package online.yllh.smartinspect.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import online.yllh.smartinspect.GlobalVals
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Parcelize
data class WatermarkState(
    val title: String = "现场记录",
    val time: Long = System.currentTimeMillis(),
    val weather: WeatherResults.Result.Now? = GlobalVals.weather.value,
    val longitude: Double = 0.0,
    val latitude: Double = 0.0,
    val altitude: Double = -1.0,
    val azimuth: Int = -1,
    val address: String = "",
    val showTitle: Boolean = true,
    val showTime: Boolean = true,
    val showWeather: Boolean = true,
    val showLocation: Boolean = true,
    val showAltitude: Boolean = true,
    val showAzimuth: Boolean = true,
    val showAddress: Boolean = true,
    val timeFormat: String = "yyyy-MM-dd HH:mm:ss",
    val weatherStyle: Int = 0,
) : Parcelable {
    val readableTime: String
        get() = SimpleDateFormat(timeFormat, Locale.getDefault()).format(Date(time))
    val altitudeDesc: String
        get() = if (altitude > 0) String.format(Locale.ROOT, "%.1f", altitude) + "米" else ""
    val azimuthDesc: String
        get() = if (azimuth >= 0) "${azimuth}° " + when {
            azimuth < 22.5 || azimuth >= 337.5 -> "北"
            azimuth < 67.5 -> "东北"
            azimuth < 112.5 -> "东"
            azimuth < 157.5 -> "东南"
            azimuth < 202.5 -> "南"
            azimuth < 247.5 -> "西南"
            azimuth < 292.5 -> "西"
            else -> "西北"
        } else ""
    val locationDesc: String
        get() = "${longitude}°N, ${latitude}°E"
    val weatherDesc: String
        get() = weather.descByStyle(weatherStyle)

    companion object {
        fun WeatherResults.Result.Now?.descByStyle(style: Int) = when (style) {
            //@formatter:off
            0 -> this?.let { "${it.text} ${it.temperature}℃" }.orEmpty()
            1 -> this?.let { "${it.text} ${it.temperature}℃ ${it.windDirection}风${it.windScale}级" }.orEmpty()
            2 -> this?.let { "${it.text} ${it.temperature}℃ ${it.windDirection}风${it.windScale}级 湿度${it.humidity}%" }.orEmpty()
            3 -> this?.let { "${it.text} ${it.temperature}℃ ${it.windDirection}风${it.windScale}级 体感${it.feelsLike}℃" }.orEmpty()
            4 -> this?.let { "${it.text} ${it.temperature}℃ ${it.windDirection}风${it.windScale}级 气压${it.pressure}hPa" }.orEmpty()
            5 -> this?.let { "${it.text} ${it.temperature}℃ ${it.windDirection}风${it.windScale}级 湿度${it.humidity}% 体感${it.feelsLike}℃ 气压${it.pressure}hPa" }.orEmpty()
            else -> this?.let { "${it.text} ${it.temperature}℃" }.orEmpty()
            //@formatter:on
        }
    }
}
