package online.yllh.smartinspect.model

import android.os.Parcelable
import android.util.Log
import kotlinx.parcelize.Parcelize

@Parcelize
data class Location(
    val longitude: Double = 0.0,
    val latitude: Double = 0.0,
    val altitude: Double = -1.0,
    val azimuth: Float = -1.0f,
    val country: String = "",
    val province: String = "",
    val city: String = "",
    val district: String = "",
    val street: String = "",
    val town: String = "",
    val poiList: List<PoiInfo> = listOf(),
) : Parcelable {
    val altitudeStr: String get() = if (altitude > 0) altitude.toString() else ""
    val azimuthStr: String get() = if (azimuth >= 0) azimuth.toString() else ""
    val poiName: String get() = poiList.firstOrNull()?.poiName.orEmpty()

    val isValid: Boolean
        get() = (longitude > 0 && latitude > 0 && altitude > 0 && poiList.isNotEmpty()).also {
            Log.d("kofua", "isLocationValid: $it")
        }
}
