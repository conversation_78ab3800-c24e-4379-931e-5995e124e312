package online.yllh.smartinspect.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class AddressDataModel(
    val country: String,
    val province: String,
    val city: String,
    val district: String,
    val street: String,
    val town: String,
    val poiName: String? = null,
    val longitude: Double = 0.0,
    val latitude: Double = 0.0,
    val altitude: Double = 0.0,
    val azimuth: Float = 0.0f,
    val poiList: List<PoiInfo>? = null
) : Parcelable
