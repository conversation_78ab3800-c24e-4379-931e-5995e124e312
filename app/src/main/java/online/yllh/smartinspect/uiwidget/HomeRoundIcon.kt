package online.yllh.smartinspect.uiwidget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.RelativeLayout
import online.yllh.smartinspect.R
import online.yllh.smartinspect.extension.dpF


class HomeRoundIcon : RelativeLayout {
    var imageView: ImageView

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attr: AttributeSet?) : this(context, attr, 0)

    constructor(context: Context, attr: AttributeSet?, defStyle: Int) : super(context, attr, defStyle) {
        setWillNotDraw(false)
        val array = context.obtainStyledAttributes(attr, R.styleable.HomeRoundIcon)
        val customDrawable = array.getDrawable(R.styleable.HomeRoundIcon_src)
        val imageHeight = array.getDimensionPixelSize(R.styleable.HomeRoundIcon_imageHeight, LayoutParams.WRAP_CONTENT)
        val imageWidth = array.getDimensionPixelSize(R.styleable.HomeRoundIcon_imageWidth, LayoutParams.WRAP_CONTENT)
        array.recycle()
        imageView = ImageView(context)
        imageView.setImageDrawable(customDrawable)
        val params = RelativeLayout.LayoutParams(
            imageWidth,
            imageHeight
        )
        params.addRule(RelativeLayout.CENTER_IN_PARENT, RelativeLayout.TRUE)
        imageView.layoutParams = params
        addView(imageView)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 保存当前的图层
        canvas.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), null)
        // 绘制原始图片
        super.dispatchDraw(canvas)
        val borderColor = 0x4DFFFFFF.toInt() // #FFFFFF 30%
        val fillColor = 0x99000000.toInt() // #000000 60%
        val borderWidth = 2.dpF
        val centerX = width / 2.toFloat()
        val centerY = height / 2.toFloat()
        val radius = Math.min(centerX, centerY) - borderWidth / 2

        val borderPaint = Paint().apply {
            color = borderColor
            style = Paint.Style.STROKE
            strokeWidth = borderWidth
        }

        val fillPaint = Paint().apply {
            color = fillColor
            style = Paint.Style.FILL
        }

        canvas.drawCircle(centerX, centerY, radius, borderPaint)
        canvas.drawCircle(centerX, centerY, radius - borderWidth / 2, fillPaint)
        canvas.restore()
    }
}
