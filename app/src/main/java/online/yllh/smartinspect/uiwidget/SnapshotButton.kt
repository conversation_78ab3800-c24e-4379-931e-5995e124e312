package online.yllh.smartinspect.uiwidget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.Shader
import android.util.AttributeSet
import android.view.View
import online.yllh.smartinspect.extension.dpF

class SnapshotButton : View {
    private val borderWidth = 4.dpF // 边框宽度
    private val borderColor = Color.parseColor("#FFD02E") // 边框颜色
    private val startColor = Color.parseColor("#10F9B7") // 渐变起始颜色
    private val endColor = Color.parseColor("#04B987") // 渐变结束颜色

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val width = canvas.width.toFloat()
        val height = canvas.height.toFloat()
        val radius = Math.min(width, height) / 2 - borderWidth / 2

        // 画笔设置
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = borderWidth
        paint.color = borderColor

        // 绘制圆形边框
        val rectF = RectF(borderWidth / 2, borderWidth / 2, width - borderWidth / 2, height - borderWidth / 2)
        canvas.drawArc(rectF, 0f, 360f, false, paint)

        // 创建线性渐变
        val gradient = LinearGradient(
            width / 2, 0f, width / 2, height,
            startColor, endColor, Shader.TileMode.CLAMP
        )

        // 应用渐变
        val gradientPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        gradientPaint.shader = gradient

        // 绘制填充圆形
        val fillRectF = RectF(borderWidth, borderWidth, width - borderWidth, height - borderWidth)
        paint.style = Paint.Style.FILL
//        canvas.drawArc(fillRectF, 0f, 360f, false, gradientPaint)
    }
}
