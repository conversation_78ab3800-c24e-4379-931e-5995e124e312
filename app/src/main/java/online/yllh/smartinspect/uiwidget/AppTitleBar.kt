package online.yllh.smartinspect.uiwidget

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.LayoutAppTitleBarBinding
import online.yllh.smartinspect.extension.layoutInflater

class AppTitleBar : FrameLayout {
    private lateinit var binding: LayoutAppTitleBarBinding

    constructor(context: Context) : super(context) {
        initView(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        val array = context.obtainStyledAttributes(attrs, R.styleable.AppTitleBar)
        val title = array.getString(R.styleable.AppTitleBar_title)
        val src = array.getDrawable(R.styleable.AppTitleBar_subMenuSrc)
        array.recycle()
        initView(context, title = title, src)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        val array = context.obtainStyledAttributes(attrs, R.styleable.AppTitleBar)
        val title = array.getString(R.styleable.AppTitleBar_title)
        val src = array.getDrawable(R.styleable.AppTitleBar_subMenuSrc)
        array.recycle()
        initView(context, title = title, src)
    }

    private fun initView(context: Context, title: String? = "质检通", subMenu: Drawable? = null) {
        binding = LayoutAppTitleBarBinding.inflate(context.layoutInflater, this, true)
        binding.title.text = title
        binding.subMenu.setImageDrawable(subMenu)
    }

    fun setTitle(title: String) {
        binding.title.text = title
    }

    fun setBackButtonClickListener(listener: View.OnClickListener) {
        binding.imageView.setOnClickListener(listener)
    }

    fun setMenuButtonClickListener(listener: View.OnClickListener) {
        binding.subMenu.setOnClickListener(listener)
    }
}
