package online.yllh.smartinspect.uiwidget.maskImage

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Xfermode
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView


abstract class MaskedImageView : AppCompatImageView {
    private var mask: Bitmap? = null
    private var paint: Paint? = null

    constructor(paramContext: Context) : super(paramContext) {
        init()
    }
    constructor(paramContext: Context, paramAttributeSet: AttributeSet?) : super(
        paramContext,
        paramAttributeSet
    ) {
        init()
    }

    constructor(paramContext: Context, paramAttributeSet: AttributeSet?, paramInt: Int) : super(
        paramContext,
        paramAttributeSet,
        paramInt
    ) {
        init()
    }

    private fun init() {
        scaleType = ScaleType.CENTER_CROP
    }

    abstract fun createMask(): Bitmap

    override fun onDraw(paramCanvas: Canvas) {
        val localDrawable = drawable ?: return
        try {
            if (paint == null) {
                val localPaint1 = Paint()
                paint = localPaint1
                paint!!.isFilterBitmap = true // 启用过滤以获得更好的图像质量
                val localPaint2 = paint
                val localXfermode1 = MASK_XFERMODE
                localPaint2!!.setXfermode(localXfermode1)
            }

            val viewWidth = width.toFloat()
            val viewHeight = height.toFloat()
            val saveLayer = paramCanvas.saveLayer(0.0f, 0.0f, viewWidth, viewHeight, null, Canvas.ALL_SAVE_FLAG)

            drawImageWithCenterCrop(paramCanvas, localDrawable, viewWidth, viewHeight)

            if (mask == null || mask!!.isRecycled) {
                mask = createMask()
            }
            paramCanvas.drawBitmap(mask!!, 0.0f, 0.0f, paint)
            paramCanvas.restoreToCount(saveLayer)

        } catch (_: Exception) {
            val localStringBuilder = StringBuilder()
                .append("Attempting to draw with recycled bitmap. View ID = ")
            println("localStringBuilder==$localStringBuilder")
        }
    }

    private fun drawImageWithCenterCrop(canvas: Canvas, drawable: android.graphics.drawable.Drawable, viewWidth: Float, viewHeight: Float) {
        val drawableWidth = drawable.intrinsicWidth.toFloat()
        val drawableHeight = drawable.intrinsicHeight.toFloat()

        if (drawableWidth <= 0 || drawableHeight <= 0) {
            drawable.setBounds(0, 0, viewWidth.toInt(), viewHeight.toInt())
            drawable.draw(canvas)
            return
        }

        // 计算缩放比例，选择较大的比例以确保填满整个区域
        val scaleX = viewWidth / drawableWidth
        val scaleY = viewHeight / drawableHeight
        val scale = maxOf(scaleX, scaleY)

        // 计算缩放后的尺寸
        val scaledWidth = drawableWidth * scale
        val scaledHeight = drawableHeight * scale

        // 计算居中偏移
        val offsetX = (viewWidth - scaledWidth) / 2
        val offsetY = (viewHeight - scaledHeight) / 2

        // 保存当前canvas状态
        val saveCount = canvas.save()

        // 应用变换
        canvas.translate(offsetX, offsetY)
        canvas.scale(scale, scale)

        // 设置drawable的bounds并绘制
        drawable.setBounds(0, 0, drawableWidth.toInt(), drawableHeight.toInt())
        drawable.draw(canvas)

        // 恢复canvas状态
        canvas.restoreToCount(saveCount)
    }

    companion object {
        private var MASK_XFERMODE: Xfermode? = null

        init {
            val localMode = PorterDuff.Mode.DST_IN
            MASK_XFERMODE = PorterDuffXfermode(localMode)
        }
    }
}
