package online.yllh.smartinspect.uiwidget

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.view.View
import online.yllh.smartinspect.extension.dpF

class VerticalDividingLineView @JvmOverloads constructor(
    context: android.content.Context,
    attrs: android.util.AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val shadowColor = Color.parseColor("#80000000")

    init {
        paint.color = Color.WHITE
        paint.strokeWidth = 1.dpF // 宽度
        shadowPaint.setShadowLayer(2.dpF, 0f, 0f, shadowColor) // 设置阴影效果
        shadowPaint.color = shadowColor // 阴影颜色为指定的颜色
        shadowPaint.style = Paint.Style.FILL
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val centerX = width / 2f
        canvas.drawLine(centerX, 0f, centerX, height.toFloat(), shadowPaint)
        canvas.drawLine(centerX, 0f, centerX, height.toFloat(), paint)
    }
}
