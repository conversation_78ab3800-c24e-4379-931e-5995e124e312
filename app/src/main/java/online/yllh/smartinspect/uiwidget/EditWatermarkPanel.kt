package online.yllh.smartinspect.uiwidget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleCoroutineScope
import kotlinx.coroutines.launch
import online.yllh.smartinspect.databinding.DialogEditWatermarkBinding
import online.yllh.smartinspect.dialog.WatermarkTimeFormatSelectDialog
import online.yllh.smartinspect.dialog.WatermarkTitleEditDialog
import online.yllh.smartinspect.dialog.WatermarkWeatherStyleSelectDialog
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.view.camera.CameraViewModel

class EditWatermarkPanel(
    context: Context,
    attrs: AttributeSet? = null,
) : FrameLayout(context, attrs) {
    private val binding = DialogEditWatermarkBinding.inflate(context.layoutInflater, this, true)
    var onHide: () -> Unit = {}
    var onAddressClick: () -> Unit = {}
    var showing = false
        private set

    init {
        binding.mask.setOnClickListener { hide() }
        binding.content.setOnClickListener {}
    }

    fun show() {
        showing = true
        isVisible = true
    }

    fun hide() {
        showing = false
        isInvisible = true
        onHide()
    }

    @SuppressLint("SetTextI18n")
    fun init(lifecycleScope: LifecycleCoroutineScope, viewModel: CameraViewModel) {
        val watermarkState = viewModel.watermarkState.value
        binding.switchTitle.isChecked = watermarkState.showTitle
        binding.switchTime.isChecked = watermarkState.showTime
        binding.switchWeather.isChecked = watermarkState.showWeather
        binding.switchLocation.isChecked = watermarkState.showLocation
        binding.switchAltitude.isChecked = watermarkState.showAltitude
        binding.switchAzimuth.isChecked = watermarkState.showAzimuth
        binding.switchAddress.isChecked = watermarkState.showAddress
        binding.switchTitle.setOnCheckedChangeListener { _, checked ->
            viewModel.watermarkState.value = viewModel.watermarkState.value.copy(showTitle = checked)
        }
        binding.switchTime.setOnCheckedChangeListener { _, checked ->
            viewModel.watermarkState.value = viewModel.watermarkState.value.copy(showTime = checked)
        }
        binding.switchWeather.setOnCheckedChangeListener { _, checked ->
            viewModel.watermarkState.value = viewModel.watermarkState.value.copy(showWeather = checked)
        }
        binding.switchLocation.setOnCheckedChangeListener { _, checked ->
            viewModel.watermarkState.value = viewModel.watermarkState.value.copy(showLocation = checked)
        }
        binding.switchAltitude.setOnCheckedChangeListener { _, checked ->
            viewModel.watermarkState.value = viewModel.watermarkState.value.copy(showAltitude = checked)
        }
        binding.switchAzimuth.setOnCheckedChangeListener { _, checked ->
            viewModel.watermarkState.value = viewModel.watermarkState.value.copy(showAzimuth = checked)
        }
        binding.switchAddress.setOnCheckedChangeListener { _, checked ->
            viewModel.watermarkState.value = viewModel.watermarkState.value.copy(showAddress = checked)
        }
        binding.groupTitle.setOnClickListener {
            WatermarkTitleEditDialog(context, viewModel.watermarkState.value.title) {
                viewModel.watermarkState.value = viewModel.watermarkState.value.copy(title = it)
            }.show()
        }
        binding.groupTime.setOnClickListener {
            WatermarkTimeFormatSelectDialog(context, viewModel).show()
        }
        binding.groupWeather.setOnClickListener {
            WatermarkWeatherStyleSelectDialog(context, viewModel).show()
        }
        binding.groupAddress.setOnClickListener { onAddressClick() }
        binding.btnClose.setOnClickListener { hide() }
        lifecycleScope.launch {
            viewModel.watermarkState.collect {
                binding.tvTitle.text = it.title
                binding.tvTime.text = "拍摄时间: ${it.readableTime}"
                binding.tvWeather.text = "天气: ${it.weatherDesc}"
                binding.tvLocation.text = "定位: ${it.locationDesc}"
                binding.tvAltitude.text = "海拔: ${it.altitudeDesc}"
                binding.tvAzimuth.text = "角度: ${it.azimuthDesc}"
                binding.tvAddress.text = "地点: ${it.address}"
            }
        }
    }
}
