package online.yllh.smartinspect.uiwidget

import android.animation.Animator
import android.animation.ObjectAnimator
import android.content.Context
import android.content.SharedPreferences
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import androidx.core.content.edit
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentAddressLevelNewBinding
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.model.AddressDataModel

class AddressLevelPanel(
    context: Context,
    attrs: AttributeSet? = null,
) : ConstraintLayout(context, attrs) {
    private var binding = FragmentAddressLevelNewBinding.inflate(context.layoutInflater, this, true)
    var hideListener: (() -> Unit)? = null
    var onLocationLevelChanged: (String) -> Unit = {}
    var onEnableLevelChanged: (Boolean) -> Unit = {}
    private var addressLevelIsEnable = true
    private var level = ""
    private var addressData: AddressDataModel? = null
    private var animator: Animator? = null

    init {
        binding.mask.setOnClickListener { hide() }
        binding.content.setOnClickListener { }
        binding.closeAddressPanel.setOnClickListener { hide() }
    }

    var showing: Boolean = false
        private set

    fun setTitleText(sp: SharedPreferences) {
        binding.area.text = when (sp.getString(SpKey.LocationLevel, "city")) {
            "country" -> addressData?.country
            "province" -> addressData?.province
            "city" -> addressData?.city
            "district" -> addressData?.district
            "town" -> addressData?.town
            "street" -> addressData?.street
            else -> "已关闭地址级别"
        }
    }

    private fun initAdapter() {
        val adapter = AddressAdapter(
            arrayListOf(
                addressData!!.country,
                addressData!!.province,
                addressData!!.city,
                addressData!!.district,
                addressData!!.town,
                addressData!!.street,
            )
        )
        binding.addressList.adapter = adapter
        initBinding()
    }

    private fun initBinding() {
        val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)

        binding.addressList.layoutManager = LinearLayoutManager(context)

        binding.levelSwitch.isChecked = sp.getBoolean(SpKey.EnableLevel, true)
        if (!binding.levelSwitch.isChecked) {
            binding.area.text = "已关闭地址级别"
            binding.addressList.visibility = View.INVISIBLE
        } else {
            setTitleText(sp)
            binding.addressList.visibility = View.VISIBLE
        }
        binding.levelSwitch.setOnCheckedChangeListener { _, isChecked ->
            addressLevelIsEnable = isChecked
            if (!isChecked) {
                binding.area.text = "已关闭地址级别"
                binding.addressList.visibility = View.INVISIBLE
                sp.edit { putBoolean(SpKey.EnableLevel, false) }
            } else {
                setTitleText(sp)
                binding.addressList.visibility = View.VISIBLE
                sp.edit { putBoolean(SpKey.EnableLevel, true) }
            }
            onEnableLevelChanged(isChecked)
        }
    }

    // 弹出面板动画
    fun show(dataModel: AddressDataModel) {
        this.animator?.cancel()
        showing = true
        addressData = dataModel
        initAdapter()
        visibility = VISIBLE
        // 计算面板的目标 Y 坐标
        val targetY = height.toFloat()
        // 创建属性动画
        val animator = ObjectAnimator.ofFloat(this, View.TRANSLATION_Y, targetY, 0f)
        // 设置动画持续时间
        animator.duration = 100
        // 启动动画
        animator.start()
        this.animator = animator
    }

    // 隐藏面板动画
    fun hide() {
        this.animator?.cancel()
        showing = false
        // 计算面板的目标 Y 坐标
        val targetY = height.toFloat()
        // 创建属性动画
        val animator = ObjectAnimator.ofFloat(this, View.TRANSLATION_Y, 0f, targetY)
        // 设置动画持续时间
        animator.duration = 100
        // 启动动画
        animator.start()
        this.animator = animator
        animator.doOnEnd { visibility = INVISIBLE }
        hideListener?.invoke()
    }

    inner class AddressAdapter(private var addressData: List<String>) : RecyclerView.Adapter<AddressAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_poi, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.poiName.text = addressData[position]
            holder.poiAddress.visibility = View.GONE
            holder.itemView.setOnClickListener {
                level = when (position) {
                    0 -> "country"
                    1 -> "province"
                    2 -> "city"
                    3 -> "district"
                    4 -> "town"
                    5 -> "street"
                    else -> ""
                }
                val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
                sp.edit { putString(SpKey.LocationLevel, level) }
                onLocationLevelChanged(level)
                hide()
            }
        }

        override fun getItemCount(): Int {
            return addressData.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val poiName: TextView = itemView.findViewById(R.id.poiName)
            val poiAddress: TextView = itemView.findViewById(R.id.poiAddress)
        }
    }
}
