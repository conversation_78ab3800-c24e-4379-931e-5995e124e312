package online.yllh.smartinspect.uiwidget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import online.yllh.smartinspect.extension.dpF

class OvalBgTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    init {
        setWillNotDraw(false)
    }

    private val circlePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    private val shadowColor = Color.parseColor("#80000000")
    private fun initPaints() {
        circlePaint.color = Color.WHITE // 圆的颜色为白色
        shadowPaint.color = shadowColor // 阴影颜色为指定的颜色
        shadowPaint.style = Paint.Style.FILL
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        initPaints()
        shadowPaint.setShadowLayer(2.dpF, 0f, 0f, shadowColor) // 设置阴影效果
    }

    override fun onDraw(canvas: Canvas) {
        canvas.apply {
            drawRoundRect(
                RectF(2.dpF, 2.dpF, width.toFloat() - 2.dpF, height.toFloat() - 2.dpF), // X坐标
                20.dpF,  // Y坐标
                20.dpF,  // 圆的半径
                shadowPaint // 画笔
            )
            drawRoundRect(
                RectF(2.dpF, 2.dpF, width.toFloat() - 2.dpF, height.toFloat() - 2.dpF), // X坐标
                20.dpF, // Y坐标
                20.dpF,  // 圆的半径
                circlePaint // 画笔
            )
        }
        super.onDraw(canvas)
    }
}
