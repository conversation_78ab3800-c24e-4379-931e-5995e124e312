package online.yllh.smartinspect.uiwidget

// GradientButton.kt
import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.LayoutButtonBinding
import online.yllh.smartinspect.extension.layoutInflater

class GradientButton : FrameLayout {
    private lateinit var binding: LayoutButtonBinding

    constructor(context: Context) : super(context) {
        initView(context, null, null, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        val array = context.obtainStyledAttributes(attrs, R.styleable.MyButton)
        val title = array.getString(R.styleable.MyButton_text)
        val style = array.getString(R.styleable.MyButton_style)
        val textSize = array.getDimension(R.styleable.MyButton_textSize, 0f)
        array.recycle()
        initView(context, title = title, style, textSize)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        val array = context.obtainStyledAttributes(attrs, R.styleable.MyButton)
        val title = array.getString(R.styleable.MyButton_text)
        val style = array.getString(R.styleable.MyButton_style)
        val textSize = array.getDimension(R.styleable.MyButton_textSize, 0f)
        array.recycle()
        initView(context, title = title, style, textSize)
    }

    private fun initView(context: Context, title: String? = "古树通", style: String?, textSize: Float? = null) {
        binding = LayoutButtonBinding.inflate(context.layoutInflater, this, true)
        binding.text.text = title
        
        // Set text size if provided
        if (textSize != null && textSize > 0) {
            binding.text.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
        }
        
        binding.myButton.background = when (style) {
            "green" -> {
                ContextCompat.getDrawable(context, R.drawable.btn_bg_green)
            }

            "grey" -> {
                ContextCompat.getDrawable(context, R.drawable.btn_bg_grey)
            }

            "red" -> {
                ContextCompat.getDrawable(context, R.drawable.btn_bg_red)
            }

            else -> {
                ContextCompat.getDrawable(context, R.drawable.btn_bg_green)
            }
        }
    }

    fun setText(text: String) {
        binding.text.text = text
    }
}
