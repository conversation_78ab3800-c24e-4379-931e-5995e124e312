package online.yllh.smartinspect.uiwidget

import android.content.Context
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.widget.LinearLayout
import online.yllh.smartinspect.databinding.LayoutPhotoProjectBinding
import online.yllh.smartinspect.extension.isNumber
import online.yllh.smartinspect.extension.layoutInflater

class PhotoProjectView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private var binding: LayoutPhotoProjectBinding
    var currentValue: Int = 1
    private var gestureDetector: GestureDetector

    init {
        setWillNotDraw(false)
        clipChildren = false
        binding = LayoutPhotoProjectBinding.inflate(context.layoutInflater, this, true)
        gestureDetector = GestureDetector(context, MyGestureListener())
        binding.photoNum.text = currentValue.toString()
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        super.onTouchEvent(event)
        gestureDetector.onTouchEvent(event)
        return true
    }

    fun setProjectName(name: String) {
        binding.projectName.text = name
    }

    fun setProjectRemark(remark: String) {
        if (remark.isEmpty()) {
            binding.photoNum.text = currentValue.toString()
        } else {
            if (remark.isNumber()) {
                currentValue = remark.toInt()
            }
            binding.photoNum.text = remark
        }
    }

    fun getProjectRemark(): String {
        return binding.photoNum.text.toString()
    }

    inner class MyGestureListener : GestureDetector.SimpleOnGestureListener() {
        override fun onFling(
            e1: MotionEvent?,
            e2: MotionEvent,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            if (binding.photoNum.text.toString().isNumber()) {
                val deltaX = e2.x.minus(e1?.x ?: 0f) ?: 0f
                if (deltaX > 0 && currentValue > 1) {
                    currentValue--
                } else if (deltaX < 0) {
                    currentValue++
                }
                binding.photoNum.text = currentValue.toString()
                return true
            } else {
                return false
            }
        }
    }
}
