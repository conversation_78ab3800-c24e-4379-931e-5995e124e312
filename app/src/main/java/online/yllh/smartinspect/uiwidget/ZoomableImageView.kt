package online.yllh.smartinspect.uiwidget

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.github.chrisbanes.photoview.PhotoView
import com.github.chrisbanes.photoview.OnPhotoTapListener
import online.yllh.smartinspect.R

class ZoomableImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : PhotoView(context, attrs, defStyleAttr) {

    private var currentImageSource: Any? = null
    private var fullScreenDialog: Dialog? = null

    init {
        isZoomable = true
        maximumScale = 5.0f
        minimumScale = 1.0f
        mediumScale = 2.5f

        setOnPhotoTapListener(object : OnPhotoTapListener {
            override fun onPhotoTap(view: android.widget.ImageView, x: Float, y: Float) {
                if (scale <= minimumScale + 0.1f) {
                    showFullScreen()
                }
            }
        })
    }

    fun setImageSource(imageSource: Any?) {
        currentImageSource = imageSource

        // 使用Glide加载图片
        Glide.with(context)
            .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
            .load(imageSource)
            .placeholder(R.drawable.placeholder)
            .into(this)
    }
    private fun showFullScreen() {
        if (currentImageSource == null) return
        
        // 创建全屏对话框
        fullScreenDialog = Dialog(context, android.R.style.Theme_Black_NoTitleBar_Fullscreen).apply {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            
            window?.apply {
                setFlags(
                    WindowManager.LayoutParams.FLAG_FULLSCREEN,
                    WindowManager.LayoutParams.FLAG_FULLSCREEN
                )
                setLayout(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.MATCH_PARENT
                )
                setBackgroundDrawable(ColorDrawable(Color.BLACK))
                
                // 隐藏系统UI
                WindowCompat.setDecorFitsSystemWindows(this, false)
                val controller = WindowInsetsControllerCompat(this, decorView)
                controller.hide(WindowInsetsCompat.Type.systemBars())
                controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }

            // 创建全屏的PhotoView
            val fullScreenPhotoView = PhotoView(context).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                setBackgroundColor(Color.BLACK)
                isZoomable = true
                maximumScale = 10.0f
                minimumScale = 1.0f
                mediumScale = 3.0f
                
                // 设置点击监听器 - 点击关闭全屏
                setOnPhotoTapListener(object : OnPhotoTapListener {
                    override fun onPhotoTap(view: android.widget.ImageView, x: Float, y: Float) {
                        // 只有在未缩放状态下才能点击退出全屏
                        if (scale <= minimumScale + 0.1f) {
                            dismiss()
                        }
                    }
                })
            }

            setContentView(fullScreenPhotoView)

            // 加载图片到全屏PhotoView
            Glide.with(context)
                .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                .load(currentImageSource)
                .placeholder(R.drawable.placeholder)
                .into(fullScreenPhotoView)

            // 设置可取消
            setCancelable(true)
            setCanceledOnTouchOutside(false)
            
            setOnDismissListener {
                fullScreenDialog = null
            }
        }
        
        fullScreenDialog?.show()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        fullScreenDialog?.dismiss()
        fullScreenDialog = null
    }
}
