package online.yllh.smartinspect.uiwidget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.os.SystemClock
import android.util.AttributeSet
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import androidx.constraintlayout.widget.ConstraintLayout
import online.yllh.smartinspect.databinding.LayoutMessageEditViewBinding
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.dpF
import online.yllh.smartinspect.extension.layoutInflater


@SuppressLint("ClickableViewAccessibility")
class MessageEditView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private var binding = LayoutMessageEditViewBinding.inflate(context.layoutInflater, this, true)
    private var gestureDetector: GestureDetector
    private var initY = 0f
    private var isCancel = false
    var startRecordListener: (() -> Unit)? = null
    var stopRecordListener: ((Boolean) -> Unit)? = null
    var onRecordListenerClick: (() -> Unit)? = null
    var onFullScreenEditClick: (() -> Unit)? = null
    var onTextDone: ((String) -> Unit)? = null

    init {
        gestureDetector = GestureDetector(context, MyGestureListener())
        binding.timerText.format = "%s"
        binding.voice.setOnClickListener {
            onRecordListenerClick?.invoke()
        }
        binding.fullScreen.setOnClickListener {
            onFullScreenEditClick?.invoke()
        }
        binding.messageInput.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_DONE) {
                onTextDone?.invoke(binding.messageInput.text.toString())
                hidePanel()
                binding.messageInput.setText("")
                true // 返回 true 表示已经处理了该事件
            } else {
                false // 返回 false 表示未处理该事件，让系统继续处理
            }
        }
        binding.holdSpeaker.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    binding.recordVoice.visibility = VISIBLE
                    binding.holdSpeakerText.visibility = GONE
                    binding.inputLayout.visibility = INVISIBLE
                    initY = event.y
                    startRecordListener?.invoke()
                    isCancel = false
                }

                MotionEvent.ACTION_UP -> {
                    binding.recordVoice.visibility = GONE
                    binding.cancelRecord.visibility = GONE
                    binding.holdSpeakerText.visibility = VISIBLE
                    binding.inputLayout.visibility = VISIBLE
                    stopRecordListener?.invoke(isCancel)
                }

                MotionEvent.ACTION_MOVE -> {
                    Log.e("ACTION_MOVE", "${initY - event.y}")
                    if (initY - event.y > 30.dp) { // 向上滑动距离超过 100 像素
                        binding.recordVoice.visibility = GONE
                        binding.cancelRecord.visibility = VISIBLE
                        isCancel = true
                    } else {
                        binding.recordVoice.visibility = VISIBLE
                        binding.cancelRecord.visibility = GONE
                        isCancel = false
                    }
                }
            }
//            gestureDetector.onTouchEvent(event)
            true
        }
    }

    fun startTimer() {
        binding.timerText.base = SystemClock.elapsedRealtime()
        binding.timerText.start()
    }

    fun stopTimer() {
        binding.timerText.stop()
    }

    fun setText(text: String) {
        binding.messageInput.setText(text)
    }

    fun getText(): String {
        return binding.messageInput.text.toString()
    }

    fun messageInputRequestFocus(): Boolean {
        return binding.messageInput.requestFocus()
    }

    inner class MyGestureListener : GestureDetector.SimpleOnGestureListener() {
        override fun onScroll(
            e1: MotionEvent?,
            e2: MotionEvent,
            velocityX: Float,
            velocityY: Float
        ): Boolean {

            return true
        }
    }

    fun showPanel(onlyInput: Boolean = false) {
        if (onlyInput) {
            binding.voice.visibility = GONE
        } else {
            binding.voice.visibility = VISIBLE
        }
        val animator: ObjectAnimator = ObjectAnimator.ofFloat(this, "translationY", 300.dpF, 0f)
        animator.start()
        <EMAIL> = VISIBLE
    }

    fun focus() {
        binding.messageInput.requestFocus()
        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(binding.messageInput, InputMethodManager.SHOW_IMPLICIT)
    }

    fun setImeHeight(height: Int) {
        val voiceHeight = 300.dp - 66.dp
        val padding = height - voiceHeight
        val params = binding.inputLayout.layoutParams as LayoutParams
        if (padding > 0) {
            params.bottomMargin = height
        } else {
            params.bottomMargin = 0
        }
        binding.inputLayout.layoutParams = params
    }

    fun hidePanel() {
        val animator: ObjectAnimator = ObjectAnimator.ofFloat(this, "translationY", 0f, 300.dpF)
        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                <EMAIL> = GONE
            }
        })
        animator.start()
    }
}
