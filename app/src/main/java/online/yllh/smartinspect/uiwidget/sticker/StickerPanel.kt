package online.yllh.smartinspect.uiwidget.sticker

import android.animation.Animator
import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import online.yllh.smartinspect.databinding.LayoutStickerPanelBinding
import online.yllh.smartinspect.extension.layoutInflater

class StickerPanel(
    context: Context,
    attrs: AttributeSet? = null,
) : ConstraintLayout(context, attrs) {
    private val binding = LayoutStickerPanelBinding.inflate(context.layoutInflater, this, true)
    var hideListener: (() -> Unit)? = null
    private var animator: Animator? = null

    var showing: Boolean = false
        private set

    init {
        binding.mask.setOnClickListener { hide() }
        binding.closePanel.setOnClickListener { hide() }
    }

    fun initAdapter(adapter: StickerPanelAdapter, selectListener: ((String) -> Unit)) {
        binding.viewPager.adapter = adapter
        binding.tabLayout.setupWithViewPager(binding.viewPager)
        binding.tabLayout.getTabAt(0)?.text = "最近"
        binding.tabLayout.getTabAt(1)?.text = "工作"
        binding.emptySticker.setOnClickListener {
            hide()
            selectListener.invoke("emptySticker")
        }
    }

    class StickerPanelAdapter(
        fragmentManager: FragmentManager,
        private val workStickerAdapter: StickerGalleryFragment.StickerAdapter,
        private val recentStickerAdapter: StickerGalleryFragment.StickerAdapter,
    ) : FragmentPagerAdapter(fragmentManager) {
        override fun getCount() = 2
        override fun getItem(position: Int) = when (position) {
            0 -> StickerGalleryFragment(recentStickerAdapter)
            1 -> StickerGalleryFragment(workStickerAdapter)
            else -> StickerGalleryFragment(workStickerAdapter)
        }
    }

    class StickerInfo(
        val stickerName: String,
        val stickerId: String,
        val stickerImage: String,
    )

    // 弹出面板动画
    fun show() {
        this.animator?.cancel()
        showing = true
        visibility = VISIBLE
        // 计算面板的目标 Y 坐标
        val targetY = height.toFloat()
        // 创建属性动画
        val animator = ObjectAnimator.ofFloat(this, View.TRANSLATION_Y, targetY, 0f)
        // 设置动画持续时间
        animator.duration = 100
        // 启动动画
        animator.start()
        this.animator = animator
    }

    // 隐藏面板动画
    fun hide() {
        this.animator?.cancel()
        showing = false
        // 计算面板的目标 Y 坐标
        val targetY = height.toFloat()
        // 创建属性动画
        val animator = ObjectAnimator.ofFloat(this, View.TRANSLATION_Y, 0f, targetY)
        // 设置动画持续时间
        animator.duration = 100
        // 启动动画
        animator.start()
        this.animator = animator
        animator.doOnEnd { visibility = INVISIBLE }
        hideListener?.invoke()
    }
}
