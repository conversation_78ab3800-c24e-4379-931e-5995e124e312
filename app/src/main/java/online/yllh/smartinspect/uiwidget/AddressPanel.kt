package online.yllh.smartinspect.uiwidget

import android.animation.Animator
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import online.yllh.smartinspect.R
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.databinding.FragmentAddressNewBinding
import online.yllh.smartinspect.extension.hideKeyboard
import online.yllh.smartinspect.extension.layoutInflater
import online.yllh.smartinspect.model.AddressDataModel
import online.yllh.smartinspect.model.PoiInfo
import online.yllh.smartinspect.view.address.AddressModel
import online.yllh.smartinspect.view.address.AddressType

@SuppressLint("ViewConstructor")
class AddressPanel(
    context: Context,
    attrs: AttributeSet? = null,
) : ConstraintLayout(context, attrs) {
    private var binding = FragmentAddressNewBinding.inflate(context.layoutInflater, this, true)

    var hideListener: (() -> Unit)? = null
    var showLevelListener: (() -> Unit)? = null
    var selectAddressListener: ((address: String) -> Unit)? = null

    private val viewModel = AddressModel()
    private var addressData: AddressDataModel? = null
    private var animator: Animator? = null

    lateinit var lifecycleOwner: LifecycleOwner

    var showing = false
        private set

    private val isLevelEnabled: Boolean
        get() = run {
            val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
            sp.getBoolean(SpKey.EnableLevel, true)
        }

    init {
        binding.addressMaskView.setOnClickListener { hide() }
        binding.content.setOnClickListener {}
        binding.changeAddressLevel.setOnClickListener {
            showLevelListener?.invoke()
        }
        binding.searchInput.addTextChangedListener { editable ->
            editable?.toString().takeIf { !it.isNullOrEmpty() }?.let {
                search(it)
            } ?: run {
                viewModel.cancelLastSearch()
                viewModel.poiList.value = addressData?.poiList.orEmpty()
            }
        }
        binding.clean.setOnClickListener {
            binding.searchInput.text.clear()
        }
    }

    private fun initAdapter() {
        viewModel.poiList.value = addressData?.poiList.orEmpty()
        val adapter = PoiAdapter(addressData?.poiList.orEmpty())
        binding.addressList.layoutManager = LinearLayoutManager(context)
        binding.addressList.adapter = adapter
        updateAddressType()
        observeLatestOn(viewModel.addressStr) {
            binding.area.text = getAddress()
        }
        observeLatestOn(viewModel.poiList) {
            adapter.poiList = it
            adapter.notifyDataSetChanged()
        }
        observeLatestOn(viewModel.addressType) {
            binding.area.text = getAddress()
        }
        observeLatestOn(viewModel.enableLevel) {
            binding.area.text = getAddress()
        }
    }

    private fun search(keyword: String) {
        val city = addressData?.city.orEmpty().ifEmpty { "成都市" }
        viewModel.performSearch(city, keyword, if (isLevelEnabled) getAddress() else "")
    }

    fun onEnableLevelChanged(enableLevel: Boolean) {
        viewModel.enableLevel.value = enableLevel
        binding.searchInput.text.toString().takeIf { it.isNotEmpty() }
            ?.let { search(it) }
    }

    fun updateAddressType() {
        val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        when (sp.getString(SpKey.LocationLevel, "city")) {
            "province" -> {
                viewModel.addressType.value = AddressType.PROVINCE
            }

            "city" -> {
                viewModel.addressType.value = AddressType.CITY
            }

            "district" -> {
                viewModel.addressType.value = AddressType.DISTRICT
            }

            "street" -> {
                viewModel.addressType.value = AddressType.STREET
            }

            "town" -> {
                viewModel.addressType.value = AddressType.TOWN
            }

            "country" -> {
                viewModel.addressType.value = AddressType.COUNTRY
            }

            else -> {
                viewModel.addressType.value = AddressType.NONE
            }
        }
        binding.searchInput.text.toString().takeIf { it.isNotEmpty() }
            ?.let { search(it) }
    }

    /**
     * 监听Flow的最新值，并在指定的生命周期状态下执行操作。
     *
     * @param flow 要监听的数据流。
     * @param lifeEvent 生命周期状态，默认为Lifecycle.State.STARTED。
     * @param action 处理收集到的值的操作。
     */
    protected fun <T> observeLatestOn(
        flow: Flow<T>,
        lifeEvent: Lifecycle.State = Lifecycle.State.STARTED,
        action: suspend (value: T) -> Unit
    ) {
        lifecycleOwner.lifecycleScope.launch {
            lifecycleOwner.repeatOnLifecycle(lifeEvent) {
                flow.collectLatest(action)
            }
        }
    }

    fun getAddress(): String {
        if (!isLevelEnabled) return "已关闭地址级别"
        return when (viewModel.addressType.value) {
            AddressType.COUNTRY -> addressData!!.country
            AddressType.PROVINCE -> addressData!!.province
            AddressType.CITY -> addressData!!.city
            AddressType.DISTRICT -> addressData!!.district
            AddressType.STREET -> addressData!!.street
            AddressType.TOWN -> addressData!!.town
            AddressType.NONE -> "已关闭地址级别"
        }
    }

    // 弹出面板动画
    fun show(dataModel: AddressDataModel) {
        this.animator?.cancel()
        showing = true
        binding.searchInput.text.clear()
        addressData = dataModel
        initAdapter()
        visibility = VISIBLE
        // 计算面板的目标 Y 坐标
        val targetY = height.toFloat()
        // 创建属性动画
        val animator = ObjectAnimator.ofFloat(this, View.TRANSLATION_Y, targetY, 0f)
        // 设置动画持续时间
        animator.duration = 100
        // 启动动画
        animator.start()
        this.animator = animator
    }

    // 隐藏面板动画
    fun hide() {
        this.animator?.cancel()
        showing = false
        binding.searchInput.hideKeyboard()
        // 计算面板的目标 Y 坐标
        val targetY = height.toFloat()
        // 创建属性动画
        val animator = ObjectAnimator.ofFloat(this, View.TRANSLATION_Y, 0f, targetY)
        // 设置动画持续时间
        animator.duration = 100
        // 启动动画
        animator.start()
        this.animator = animator
        animator.doOnEnd { visibility = INVISIBLE }
        hideListener?.invoke()
    }

    inner class PoiAdapter(var poiList: List<PoiInfo>) : RecyclerView.Adapter<PoiAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_poi, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.poiName.text = poiList[position].poiName
            holder.poiAddress.text = poiList[position].poiAddress
            holder.itemView.setOnClickListener {
                putData(poiList[position].poiName)
                hide()
            }
        }

        private fun putData(currentAddress: String) {
            println(currentAddress)
            selectAddressListener?.invoke(currentAddress)
        }

        override fun getItemCount(): Int {
            return poiList.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val poiName: TextView = itemView.findViewById(R.id.poiName)
            val poiAddress: TextView = itemView.findViewById(R.id.poiAddress)
        }
    }

}
