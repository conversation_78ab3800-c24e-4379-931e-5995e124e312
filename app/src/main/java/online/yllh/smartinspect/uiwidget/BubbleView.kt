package online.yllh.smartinspect.uiwidget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import online.yllh.smartinspect.extension.dpF


class BubbleView(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    private val paint: Paint = Paint()
    private val bubblePath: Path = Path()
    var offset = 10.dpF

    init {
        paint.color = Color.parseColor("#80000000") // 半透明背景色
        paint.style = Paint.Style.FILL
        paint.isAntiAlias = true
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 绘制聊天气泡
        val width = width.toFloat()
        val height = height.toFloat()
        val cornerRadius = 8.dpF // 圆角半径
        val triangleHeight = 8.dpF // 三角尖高度
        val triangleWidth = 18.dpF // 三角尖宽度

        bubblePath.reset()
//        bubblePath.moveTo(cornerRadius, triangleHeight + cornerRadius) // 左上角

        // 绘制左上角圆弧
        val topLeftArc = RectF(0f, triangleHeight, 2 * cornerRadius, triangleHeight + 2 * cornerRadius)
        bubblePath.arcTo(topLeftArc, 180f, 90f)

//        bubblePath.lineTo(offset, triangleHeight) // 三角尖左侧
//        bubblePath.lineTo((offset + triangleWidth / 2) , 0f) // 三角尖顶点
//        bubblePath.lineTo((offset + triangleWidth), triangleHeight) // 三角尖右侧

        // 绘制右上角圆弧
        val topRightArc = RectF(width - 2 * cornerRadius, triangleHeight, width, triangleHeight + 2 * cornerRadius)
        bubblePath.arcTo(topRightArc, 270f, 90f)

//        bubblePath.lineTo(width - cornerRadius, triangleHeight + cornerRadius) // 右上角
        bubblePath.lineTo(width, height - cornerRadius) // 右下角

        // 绘制右下角圆弧
        val bottomRightArc = RectF(width - 2 * cornerRadius, height - 2 * cornerRadius, width, height)
        bubblePath.arcTo(bottomRightArc, 0f, 90f)

        bubblePath.lineTo(cornerRadius, height) // 左下角

        // 绘制左下角圆弧
        val bottomLeftArc = RectF(0f, height - 2 * cornerRadius, 2 * cornerRadius, height)
        bubblePath.arcTo(bottomLeftArc, 90f, 90f)

        bubblePath.close()

        canvas.drawPath(bubblePath, paint)
    }
}