package online.yllh.smartinspect.uiwidget

import android.content.Context
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.LayoutExifItemBinding
import online.yllh.smartinspect.extension.layoutInflater

class ExitItem(
    context: Context,
    attrs: AttributeSet? = null,
) : ConstraintLayout(context, attrs) {
    private var binding = LayoutExifItemBinding.inflate(context.layoutInflater, this, true)

    init {
        val array = context.obtainStyledAttributes(attrs, R.styleable.ExifItem)
        val title = array.getString(R.styleable.ExifItem_name)
        val desc = array.getString(R.styleable.ExifItem_desc)
        array.recycle()
        binding.text1.id = generateViewId()
        binding.text2.id = generateViewId()
        binding.text1.text = title
        binding.text2.text = desc
    }

    fun setTitle(string: String) {
        binding.text1.text = string
    }

    fun setDescription(string: String) {
        binding.text2.text = string
    }


}
