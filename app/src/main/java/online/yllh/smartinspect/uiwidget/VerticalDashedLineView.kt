package online.yllh.smartinspect.uiwidget

import android.content.Context
import android.graphics.Canvas
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import online.yllh.smartinspect.R
import online.yllh.smartinspect.extension.toColorInt

class VerticalDashedLineView(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {

    private val paint = Paint()

    init {
        paint.color = R.color.dashLine.toColorInt(context) // 设置虚线颜色
        paint.strokeWidth = 5f // 设置虚线宽度
        paint.pathEffect = DashPathEffect(floatArrayOf(6f, 6f), 6f) // 设置虚线间隔
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val centerX = width / 2f
        canvas.drawLine(centerX, 0f, centerX, height.toFloat(), paint)
    }
}
