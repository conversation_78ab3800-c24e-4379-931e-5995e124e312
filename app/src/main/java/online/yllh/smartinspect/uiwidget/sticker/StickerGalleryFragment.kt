package online.yllh.smartinspect.uiwidget.sticker

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import online.yllh.smartinspect.BaseFragment
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.FragmentStickerGalleryBinding
import online.yllh.smartinspect.extension.dp
import online.yllh.smartinspect.extension.layoutInflater
import kotlin.math.roundToInt

class StickerGalleryFragment(
    private val stickerAdapter: StickerAdapter
) : BaseFragment() {
    private lateinit var binding: FragmentStickerGalleryBinding
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentStickerGalleryBinding.inflate(inflater, container, false)
        binding.stickerGalleryRecyclerView.adapter = stickerAdapter
        binding.stickerGalleryRecyclerView.layoutManager = GridLayoutManager(requireContext(), 3)
        return binding.root
    }

    class StickerAdapter(
        var stickerList: List<StickerPanel.StickerInfo>,
        private val selectListener: ((String) -> Unit)?,
        var currentSticker: String
    ) : RecyclerView.Adapter<StickerAdapter.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = parent.context.layoutInflater.inflate(R.layout.item_sticker, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val sticker = stickerList[position]
            val context = holder.itemView.context
            val displayMetrics = context.resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val imageWidth = (screenWidth - 12.dp) / 3
            holder.itemView.layoutParams.width = imageWidth
            holder.itemView.layoutParams.height = (imageWidth * (158 / 119f)).roundToInt()
            holder.stickerCons.layoutParams.width = imageWidth - 4.dp
            holder.stickerCons.layoutParams.height = (imageWidth * (158 / 119f)).roundToInt()
            holder.name.text = sticker.stickerName

            if (sticker.stickerImage.isNotEmpty()) {
                when {
                    sticker.stickerImage.startsWith("R.drawable.") -> {
                        val resourceName = sticker.stickerImage.substringAfter("R.drawable.")
                        val resourceId = context.resources.getIdentifier(resourceName, "drawable", context.packageName)
                        if (resourceId != 0) {
                            Glide.with(context)
                                .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                                .load(resourceId)
                                .placeholder(R.drawable.placeholder)
                                .into(holder.stickerImage)
                        } else {
                            holder.stickerImage.setImageResource(R.drawable.placeholder)
                        }
                    }
                    sticker.stickerImage.startsWith("http") -> {
                        Glide.with(context)
                            .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                            .load(sticker.stickerImage)
                            .placeholder(R.drawable.placeholder)
                            .into(holder.stickerImage)
                    }
                    else -> {
                        Glide.with(context)
                            .applyDefaultRequestOptions(RequestOptions.diskCacheStrategyOf(DiskCacheStrategy.ALL))
                            .load(sticker.stickerImage)
                            .placeholder(R.drawable.placeholder)
                            .into(holder.stickerImage)
                    }
                }
            } else {
                holder.stickerImage.setImageResource(R.drawable.placeholder)
            }
            holder.itemView.setOnClickListener {
                selectListener?.invoke(sticker.stickerId)
            }
            if (sticker.stickerId == currentSticker) {
                holder.select.visibility = View.VISIBLE
            } else {
                holder.select.visibility = View.GONE
            }
        }

        override fun getItemCount(): Int {
            return stickerList.size
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val select: View = itemView.findViewById(R.id.select)
            val name: TextView = itemView.findViewById(R.id.stickerName)
            val stickerImage: ImageView = itemView.findViewById(R.id.stickerImage)
            val stickerCons: ConstraintLayout = itemView.findViewById(R.id.stickerCons)
        }
    }
}
