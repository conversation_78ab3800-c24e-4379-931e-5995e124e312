package online.yllh.smartinspect.dao

import androidx.room.*
import online.yllh.smartinspect.entity.RecycledPhoto

@Dao
interface RecycledPhotoDao {
    @Insert
    suspend fun insert(recycledPhoto: RecycledPhoto)

    @Update
    suspend fun update(recycledPhoto: RecycledPhoto)

    @Delete
    suspend fun delete(recycledPhoto: RecycledPhoto)

    @Query("SELECT * FROM recycled_photos WHERE id = :id")
    suspend fun getRecycledPhotoById(id: Int): RecycledPhoto?

    @Query("SELECT * FROM recycled_photos WHERE uri = :uri LIMIT 1")
    suspend fun getRecycledPhotoByUri(uri: String): RecycledPhoto?

    @Query("SELECT * FROM recycled_photos ORDER BY deleteTime DESC")
    suspend fun getAllRecycledPhotos(): List<RecycledPhoto>

    @Query("DELETE FROM recycled_photos WHERE deleteTime < :expiredTime")
    suspend fun deleteExpiredPhotos(expiredTime: Long)

    @Query("SELECT COUNT(*) FROM recycled_photos")
    suspend fun getRecycledPhotoCount(): Int

    @Query("DELETE FROM recycled_photos")
    suspend fun deleteAll()
}
