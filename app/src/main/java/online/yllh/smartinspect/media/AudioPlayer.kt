package online.yllh.smartinspect.media

import android.content.Context
import android.media.MediaPlayer

class AudioPlayer private constructor(context: Context) {
    private val mediaPlayer: MediaPlayer = MediaPlayer()
    private var isInitialized = false

    init {
        mediaPlayer.setOnPreparedListener { isInitialized = true }
    }

    fun play(filePath: String, onCompletionListener: () -> Unit) {
        if (isInitialized) {
            stop()
        }
        mediaPlayer.reset()
        mediaPlayer.setDataSource(filePath)
        mediaPlayer.prepare()
        mediaPlayer.setOnCompletionListener {
            stop()
            isInitialized = false
            onCompletionListener()
        }
        mediaPlayer.start()

    }

    fun isPlaying(): Boolean {
        if (!isInitialized) return false
        return mediaPlayer.isPlaying
    }

    fun stop() {
        if (mediaPlayer.isPlaying) {
            mediaPlayer.stop()
        }
    }

    companion object {
        @Volatile
        private var instance: AudioPlayer? = null

        fun getInstance(context: Context): AudioPlayer {
            return instance ?: synchronized(this) {
                instance ?: AudioPlayer(context).also { instance = it }
            }
        }
    }
}
