package online.yllh.smartinspect.media

import android.media.MediaPlayer
import java.io.IOException

class AudioDurationCalculator {
    companion object {
        fun getDurationInSeconds(filePath: String): Int {
            val mediaPlayer = MediaPlayer()
            return try {
                mediaPlayer.setDataSource(filePath)
                mediaPlayer.prepare()
                val duration = mediaPlayer.duration / 1000 // 将毫秒转换为秒
                mediaPlayer.release()
                duration
            } catch (e: IOException) {
                e.printStackTrace()
                0
            }
        }
    }
}