package online.yllh.smartinspect.media

import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import online.yllh.smartinspect.model.Album
import online.yllh.smartinspect.model.Image
import java.io.File
import java.io.FileInputStream
import java.io.InputStream
import java.io.OutputStream


fun saveImageToGallery(context: Context, imageFile: File): Uri? {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        val values = ContentValues().apply {
            put(MediaStore.Images.Media.DISPLAY_NAME, imageFile.name)
            put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
            put(MediaStore.Images.Media.DATE_ADDED, System.currentTimeMillis() / 1000)
            put(MediaStore.Images.Media.DATE_TAKEN, System.currentTimeMillis())
            put(MediaStore.Images.Media.RELATIVE_PATH, "DCIM/Camera")
        }

        val contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
        val contentResolver = context.contentResolver

        // 插入到媒体数据库
        val uri: Uri? = contentResolver.insert(contentUri, values)
        try {
            val out: OutputStream? = contentResolver.openOutputStream(uri!!)
            val fileInputStream = FileInputStream(imageFile)
            out?.write(fileInputStream.readBytes())
            fileInputStream.close()
            out?.close()
            values.clear()
            values.put(MediaStore.MediaColumns.IS_PENDING, 0)
            values.putNull(MediaStore.MediaColumns.DATE_EXPIRES)
            contentResolver.update(uri, values, null, null)
        } catch (e: Exception) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                contentResolver.delete(uri!!, null)
            }
        }
        // 刷新系统相册
        uri?.let {
            MediaScannerConnection.scanFile(context, arrayOf(imageFile.absolutePath), null) { _, _ ->
                // Scan completed
            }
        }
        uri
    } else {
        val values = ContentValues().apply {
            put(MediaStore.Images.ImageColumns.TITLE, imageFile.name)
            put(MediaStore.Images.ImageColumns.DISPLAY_NAME, imageFile.name)
            put(MediaStore.Images.ImageColumns.DATE_TAKEN, System.currentTimeMillis())
            put(MediaStore.Images.ImageColumns.MIME_TYPE, "image/jpeg")
            put(MediaStore.Images.Media.DATA, imageFile.absolutePath)
            put(MediaStore.Images.ImageColumns.ORIENTATION, 0)
        }
        val desFile = File("${Environment.getExternalStorageDirectory()}/DCIM/Camera/${imageFile.name}")
        if (!desFile.exists()) {
            desFile.createNewFile()
        }
        values.put(MediaStore.MediaColumns.DATA, desFile.absolutePath)
        values.put(MediaStore.MediaColumns.SIZE, desFile.length())
        val targetUrl = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
        val uri = context.contentResolver?.insert(targetUrl, values)
        if (uri != null) {
            context.contentResolver?.openOutputStream(uri).use { out ->
                val inputStream: InputStream = FileInputStream(imageFile)
                val buffer = ByteArray(1024)
                inputStream.use { input ->
                    var len = 0
                    while (input.read(buffer).also { len = it } != -1) {
                        out?.write(buffer, 0, len)
                    }
                }
            }
        }
        uri
    }
}


fun getAllImagesSortedByTime(context: Context): List<Image> {
    val imageList = mutableListOf<Image>()
    val contentResolver: ContentResolver = context.contentResolver

    val uri: Uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
    val projection = arrayOf(
        MediaStore.Images.Media._ID,
        MediaStore.Images.Media.DATA,
        MediaStore.Images.Media.DATE_TAKEN,
        MediaStore.Images.Media._ID
    )
    val sortOrder = "${MediaStore.Images.Media.DATE_TAKEN} DESC" // 按照时间降序排序
    val cursor: Cursor? = contentResolver.query(uri, projection, null, null, sortOrder)

    cursor?.use {
        while (it.moveToNext()) {
            val imageId = it.getString(it.getColumnIndex(MediaStore.Images.Media._ID))
            val imagePath = it.getString(it.getColumnIndex(MediaStore.Images.Media.DATA))
            val imageDateTaken = it.getLong(it.getColumnIndex(MediaStore.Images.Media.DATE_TAKEN))
            val coverUri = Uri.withAppendedPath(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                it.getString(it.getColumnIndex(MediaStore.Images.Media._ID))
            )
            val image = Image(imageId, imagePath, coverUri, imageDateTaken)
            imageList.add(image)
        }
    } ?: Log.e(TAG, "Cursor is null.")

    return imageList
}

private const val TAG = "AlbumManager"

fun getAllAlbums(context: Context): List<Album> {
    val albumList = mutableListOf<Album>()
    val contentResolver: ContentResolver = context.contentResolver

    val uri: Uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
    val projection = arrayOf(
        MediaStore.Images.Media.BUCKET_ID,
        MediaStore.Images.Media.BUCKET_DISPLAY_NAME,
        MediaStore.Images.Media.DATA,
        MediaStore.Images.Media._ID
    )

    // 创建一个HashSet用于去重
    val albumSet = mutableSetOf<String>()

    val cursor: Cursor? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
        contentResolver.query(uri, projection, Bundle().apply {
            putString(ContentResolver.QUERY_ARG_SQL_SORT_ORDER, "${MediaStore.Images.Media.DATE_TAKEN} DESC")
            putString(ContentResolver.QUERY_ARG_SQL_GROUP_BY, MediaStore.Images.Media.BUCKET_ID)
        }, null)
    } else {
        contentResolver.query(uri, projection, null, null, "${MediaStore.Images.Media.DATE_TAKEN} DESC")
    }

    cursor?.use {
        while (it.moveToNext()) {
            val albumId = it.getString(it.getColumnIndex(MediaStore.Images.Media.BUCKET_ID))

            // 如果这个相册ID已经处理过了，则跳过
            if (albumSet.contains(albumId)) {
                continue
            }

            val albumName = it.getString(it.getColumnIndex(MediaStore.Images.Media.BUCKET_DISPLAY_NAME))
            val coverPath = it.getString(it.getColumnIndex(MediaStore.Images.Media.DATA))
            val latestPhotoUri = getLatestPhotoUri(context, albumId)
            val photoCount = getPhotoCount(context, albumId)

            // 创建Album对象并加入列表
            val album = Album(albumId, albumName, coverPath, latestPhotoUri, photoCount)
            albumList.add(album)

            // 添加到去重的set中
            albumSet.add(albumId)
        }
    } ?: Log.e(TAG, "Cursor is null.")

    return albumList
}

private fun getPhotoCount(context: Context, albumId: String): Int {
    val contentResolver: ContentResolver = context.contentResolver

    val uri: Uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
    val projection = arrayOf(
        MediaStore.Images.Media.BUCKET_ID
    )
    val selection = "${MediaStore.Images.Media.BUCKET_ID} = ?"
    val selectionArgs = arrayOf(albumId)
    val cursor: Cursor? = contentResolver.query(uri, projection, selection, selectionArgs, null)

    val count = cursor?.count ?: 0
    cursor?.close()
    return count
}

private fun getLatestPhotoUri(context: Context, albumId: String): Uri? {
    val contentResolver: ContentResolver = context.contentResolver

    val uri: Uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
    val projection = arrayOf(
        MediaStore.Images.Media._ID
    )
    val selection = "${MediaStore.Images.Media.BUCKET_ID} = ?"
    val selectionArgs = arrayOf(albumId)
    val sortOrder = "${MediaStore.Images.Media.DATE_ADDED} DESC"
    val cursor: Cursor? = contentResolver.query(uri, projection, selection, selectionArgs, sortOrder)

    val latestPhotoUri: Uri? = if (cursor != null && cursor.moveToFirst()) {
        val photoId = cursor.getLong(cursor.getColumnIndex(MediaStore.Images.Media._ID))
        Uri.withAppendedPath(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, photoId.toString())
    } else {
        null
    }

    cursor?.close()
    return latestPhotoUri
}

fun getAllPhotosInAlbum(context: Context, albumId: String): List<Image> {
    val photoList = mutableListOf<Image>()
    val contentResolver: ContentResolver = context.contentResolver

    val uri: Uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
    val projection = arrayOf(
        MediaStore.Images.Media._ID,
        MediaStore.Images.Media.DATA,
        MediaStore.Images.Media.DATE_TAKEN
    )
    val selection = "${MediaStore.Images.Media.BUCKET_ID} = ?"
    val selectionArgs = arrayOf(albumId)
    val sortOrder = "${MediaStore.Images.Media.DATE_TAKEN} DESC"
    val cursor: Cursor? = contentResolver.query(uri, projection, selection, selectionArgs, sortOrder)

    cursor?.use {
        while (it.moveToNext()) {
            val photoId = it.getString(it.getColumnIndex(MediaStore.Images.Media._ID))
            val photoPath = it.getString(it.getColumnIndex(MediaStore.Images.Media.DATA))
            val photoUri = Uri.withAppendedPath(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, photoId.toString())
            val photoDate = it.getLong(it.getColumnIndex(MediaStore.Images.Media.DATE_TAKEN))

            val image = Image(photoId, photoPath, photoUri, photoDate)
            photoList.add(image)
        }
    } ?: Log.e(TAG, "Cursor is null.")

    return photoList
}

fun isPhotoExists(context: Context, uri: Uri): Boolean {
    val projection = arrayOf(MediaStore.Images.Media._ID)
    val selection = MediaStore.Images.ImageColumns.IS_TRASHED + "=?"
    val selectionArgs = arrayOf("0")
    val cursor = context.contentResolver.query(uri, projection, selection, selectionArgs, null)
    val exists = cursor?.use { it.moveToFirst() } == true
    return exists
}
