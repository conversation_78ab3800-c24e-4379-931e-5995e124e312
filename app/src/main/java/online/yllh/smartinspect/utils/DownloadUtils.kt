package online.yllh.smartinspect.utils

import okhttp3.OkHttpClient
import okhttp3.Request
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

object DownloadUtils {
    suspend fun downloadFile(url: String): ByteArray? {
        return suspendCoroutine {
            val client = OkHttpClient()
            val request = Request.Builder()
                .url(url)
                .build()

            try {
                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) {
                        it.resume(null)
                    } else {
                        val body = response.body
                        it.resume(body?.bytes())
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                it.resume(null)
            }
        }
    }
}
