package online.yllh.smartinspect.utils

import android.webkit.MimeTypeMap
import java.util.Locale

object MediaTypeUtils {
    fun getMediaTypeFromFileName(fileName: String): String {
        val fileExtension = getFileExtension(fileName)
        return getMediaTypeFromExtension(fileExtension)
    }

    private fun getFileExtension(fileName: String): String {
        val dotIndex = fileName.lastIndexOf('.')
        return if (dotIndex != -1) {
            fileName.substring(dotIndex + 1).lowercase(Locale.getDefault())
        } else {
            ""
        }
    }

    private fun getMediaTypeFromExtension(extension: String): String {
        return MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension) ?: "application/octet-stream"
    }
}
