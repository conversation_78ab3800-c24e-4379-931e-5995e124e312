package online.yllh.smartinspect.utils

import android.annotation.SuppressLint
import android.location.LocationListener
import android.location.LocationManager
import android.util.Log
import androidx.core.content.getSystemService
import com.baidu.location.BDAbstractLocationListener
import com.baidu.location.BDLocation
import com.baidu.location.LocationClient
import com.baidu.location.LocationClientOption
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import online.yllh.smartinspect.model.Location
import online.yllh.smartinspect.model.PoiInfo
import online.yllh.smartinspect.provider.ApplicationContextProvider
import java.util.Locale
import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

object LocationHelper : BDAbstractLocationListener() {
    private val context get() = ApplicationContextProvider.context

    private val locationClient by lazy { LocationClient(context).also { it.init() } }
    private val locationManager by lazy { context.getSystemService<LocationManager>() }

    /**
     * PI：圆周率
     * A：长半轴参数
     * EE：偏心率平方
     */
    private const val PI = Math.PI
    private const val A = 6378245.0
    private const val EE = 0.006693421622965943

    private val _location = MutableStateFlow(Location())
    val location = _location.asStateFlow()

    private val _located = MutableStateFlow(false)
    val located = _located.asStateFlow()

    private val nativeLocationListener = object : LocationListener {
        override fun onLocationChanged(location: android.location.Location) {
            updateNativeLocation(location)
        }

        override fun onProviderEnabled(provider: String) {
            Log.d("Maosi", "Location provider enabled: $provider")
        }

        override fun onProviderDisabled(provider: String) {
            Log.d("Maosi", "Location provider disabled: $provider")
        }
    }

    fun start() {
        if (!locationClient.isStarted) {
            locationClient.start()
        }
        startNativeLocationUpdates()
    }

    fun stop() {
        locationClient.stop()
        stopNativeLocationUpdates()
    }

    /**
     * 强制刷新位置信息
     */
    fun refreshLocation() {
        if (locationClient.isStarted) {
            locationClient.requestLocation()
        }
    }

    override fun onReceiveLocation(location: BDLocation) {
        when (val locType = location.locType) {
            BDLocation.TypeNetWorkLocation, BDLocation.TypeGpsLocation -> {
                val country = location.country.orEmpty()
                val province = location.province.orEmpty()
                val city = location.city.orEmpty()
                val district = location.district.orEmpty()
                val street = location.street.orEmpty()
                val town = location.town.orEmpty()
                val poiList = location.poiList?.map { PoiInfo(it.name, it.addr) }.orEmpty()

                // 强制更新位置信息，不依赖之前的缓存
                _location.value = _location.value.copy(
                    country = country,
                    province = province,
                    city = city,
                    district = district,
                    street = street,
                    town = town,
                    poiList = poiList,
                )

                if (_location.value.isValid) {
                    _located.value = true
                }

                Log.i("Maosi", "onReceiveLocation ok, locType: $locType, city: $city, district: $district, street: $street, poiList size: ${poiList.size}")
            }

            else -> {
                Log.d("Maosi", "onReceiveLocation failed, locType: $locType, error: ${location.locTypeDescription}")
            }
        }
    }

    private fun LocationClient.init() {
        val option = LocationClientOption()
        option.isOpenGnss = true
        option.setCoorType(BDLocation.BDLOCATION_COOR_TYPE_BD09LL)
        option.setIsNeedAddress(true)
        option.setNeedNewVersionRgc(true)
        option.setIsNeedLocationPoiList(true)
        option.setIsNeedAltitude(true)
        option.setNeedDeviceDirect(true)
        option.setScanSpan(2000)
        option.setLocationMode(LocationClientOption.LocationMode.Hight_Accuracy)
        option.setOpenAutoNotifyMode()
        locOption = option
        registerLocationListener(this@LocationHelper)
    }

    /**
     * 开始原生位置更新
     */
    @SuppressLint("MissingPermission")
    private fun startNativeLocationUpdates() {
        locationManager?.let { manager ->
            try {
                val lastKnownLocation = manager.getLastKnownLocation(LocationManager.GPS_PROVIDER)
                    ?: manager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER)

                lastKnownLocation?.let { updateNativeLocation(it) }

                for (string in manager.getProviders(true)) {
                    manager.requestLocationUpdates(
                        string,
                        2000L,
                        0.1f,
                        nativeLocationListener
                    )
                    Log.d("Maosi", "Started GPS location updates")
                }
            } catch (e: SecurityException) {
                Log.e("Maosi", "Location permission not granted", e)
            } catch (e: Exception) {
                Log.e("Maosi", "Error starting location updates", e)
            }
        }
    }

    /**
     * 停止原生位置更新
     */
    private fun stopNativeLocationUpdates() {
        locationManager?.let { manager ->
            try {
                manager.removeUpdates(nativeLocationListener)
                Log.d("Maosi", "Stopped native location updates")
            } catch (e: Exception) {
                Log.e("Maosi", "Error stopping location updates", e)
            }
        }
    }

    /**
     * 更新原生位置信息
     */
    private fun updateNativeLocation(location: android.location.Location) {
        val preLocation = _location.value

        // 原始WGS84坐标
        val originalLng = location.longitude
        val originalLat = location.latitude

        // 转换为百度坐标系（BD09）
        val convertedCoords = wgs84ToBd09(originalLng, originalLat)
        val longitude = String.format(Locale.ROOT, "%.6f", convertedCoords.first).toDouble()
        val latitude = String.format(Locale.ROOT, "%.6f", convertedCoords.second).toDouble()

        Log.d("Maosi", "updateNativeLocation, preLocation altitude: ${preLocation.altitude}")
        val altitude = if (location.hasAltitude() && location.altitude > 0) location.altitude else preLocation.altitude

        Log.d(
            "Maosi",
            "updateNativeLocation, provider: ${location.provider}, " +
                    "original: (${String.format(Locale.ROOT, "%.6f", originalLng)}, ${String.format(Locale.ROOT, "%.6f", originalLat)}), " +
                    "converted: ($longitude, $latitude), " +
                    "hasAltitude: ${location.hasAltitude()}, altitude: ${location.altitude}, accuracy: ${location.accuracy}"
        )

        val newLocation = preLocation.copy(
            longitude = longitude,
            latitude = latitude,
            altitude = altitude
        )
        _location.value = newLocation

        if (newLocation.isValid) {
            _located.value = true
        }
    }

    private fun wgs84ToBd09(lng: Double, lat: Double): Pair<Double, Double> {
        // 先转换为GCJ02坐标系
        val gcj02 = wgs84ToGcj02(lng, lat)
        // 再转换为BD09坐标系
        return gcj02ToBd09(gcj02.first, gcj02.second)
    }

    private fun wgs84ToGcj02(lng: Double, lat: Double): Pair<Double, Double> {
        if (outOfChina(lat, lng)) {
            return Pair(lng, lat)
        }
        var dLat = transformLat(lng - 105.0, lat - 35.0)
        var dLng = transformLng(lng - 105.0, lat - 35.0)
        val radLat = lat / 180.0 * PI
        var magic = sin(radLat)
        magic = 1 - EE * magic * magic
        val sqrtMagic = sqrt(magic)
        dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI)
        dLng = (dLng * 180.0) / (A / sqrtMagic * cos(radLat) * PI)
        val mgLat = lat + dLat
        val mgLng = lng + dLng
        return Pair(mgLng, mgLat)
    }

    private fun gcj02ToBd09(lng: Double, lat: Double): Pair<Double, Double> {
        val z = sqrt(lng * lng + lat * lat) + 0.00002 * sin(lat * PI)
        val theta = atan2(lat, lng) + 0.000003 * cos(lng * PI)
        val bdLng = z * cos(theta) + 0.0065
        val bdLat = z * sin(theta) + 0.006
        return Pair(bdLng, bdLat)
    }

    private fun outOfChina(lat: Double, lng: Double): Boolean {
        return lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271
    }

    private fun transformLat(lng: Double, lat: Double): Double {
        var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * sqrt(abs(lng))
        ret += (20.0 * sin(6.0 * lng * PI) + 20.0 * sin(2.0 * lng * PI)) * 2.0 / 3.0
        ret += (20.0 * sin(lat * PI) + 40.0 * sin(lat / 3.0 * PI)) * 2.0 / 3.0
        ret += (160.0 * sin(lat / 12.0 * PI) + 320 * sin(lat * PI / 30.0)) * 2.0 / 3.0
        return ret
    }

    private fun transformLng(lng: Double, lat: Double): Double {
        var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * sqrt(abs(lng))
        ret += (20.0 * sin(6.0 * lng * PI) + 20.0 * sin(2.0 * lng * PI)) * 2.0 / 3.0
        ret += (20.0 * sin(lng * PI) + 40.0 * sin(lng / 3.0 * PI)) * 2.0 / 3.0
        ret += (150.0 * sin(lng / 12.0 * PI) + 300.0 * sin(lng / 30.0 * PI)) * 2.0 / 3.0
        return ret
    }
}
