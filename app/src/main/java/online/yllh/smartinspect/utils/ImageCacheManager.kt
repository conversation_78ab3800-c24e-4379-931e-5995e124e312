package online.yllh.smartinspect.utils

import android.content.Context
import android.graphics.drawable.Drawable
import android.net.ConnectivityManager
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import online.yllh.smartinspect.R

/**
 * 图片缓存管理器
 * 统一管理云相册图片的加载和缓存策略
 */
object ImageCacheManager {

    /**
     * 清除内存缓存
     * @param context 上下文
     */
    fun clearMemoryCache(context: Context) {
        Glide.get(context).clearMemory()
    }

    /**
     * 清除磁盘缓存（需要在后台线程调用）
     * @param context 上下文
     */
    fun clearDiskCache(context: Context) {
        Thread {
            Glide.get(context).clearDiskCache()
        }.start()
    }

    /**
     * 获取缓存大小（需要在后台线程调用）
     * @param context 上下文
     * @return 缓存大小（字节）
     */
    fun getCacheSize(context: Context): Long {
        return try {
            Glide.getPhotoCacheDir(context)?.let { cacheDir ->
                cacheDir.walkTopDown().filter { it.isFile }.map { it.length() }.sum()
            } ?: 0L
        } catch (e: Exception) {
            0L
        }
    }
}
