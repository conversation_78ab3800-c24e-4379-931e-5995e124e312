package online.yllh.smartinspect.utils

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import androidx.core.content.getSystemService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

class CompassSensorHelper(context: Context) : SensorEventListener {

    private val sensorManager = context.getSystemService<SensorManager>()
    private val rotationVectorSensor = sensorManager?.getDefaultSensor(Sensor.TYPE_ROTATION_VECTOR)

    private val rotationMatrix = FloatArray(9)
    private val orientationAngles = FloatArray(3)
    private val _azimuth = MutableStateFlow(-1)
    val azimuth = _azimuth.asStateFlow()

    fun start() {
        rotationVectorSensor?.let {
            sensorManager?.registerListener(this, it, SensorManager.SENSOR_DELAY_UI)
        }
    }

    fun stop() {
        sensorManager?.unregisterListener(this)
    }

    override fun onSensorChanged(event: SensorEvent) {
        if (event.sensor.type == Sensor.TYPE_ROTATION_VECTOR) {
            // 将 rotation vector 转换为 rotation matrix
            SensorManager.getRotationMatrixFromVector(rotationMatrix, event.values)

            // 从 rotation matrix 获取方位角等三个方向角（弧度）
            SensorManager.getOrientation(rotationMatrix, orientationAngles)

            // 方向角：[azimuth(Z), pitch(X), roll(Y)]
            val azimuthRad = orientationAngles[0]
            val azimuthDeg = Math.toDegrees(azimuthRad.toDouble()).toFloat()

            // 归一化到 0-360
            val azimuth = (azimuthDeg + 360) % 360

            _azimuth.value = azimuth.toInt()
        }
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // 可选：处理传感器精度变化
    }
}
