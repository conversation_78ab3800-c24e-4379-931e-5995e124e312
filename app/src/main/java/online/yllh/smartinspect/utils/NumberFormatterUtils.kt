package online.yllh.smartinspect.utils

import android.icu.number.LocalizedNumberFormatter
import android.icu.number.NumberFormatter
import android.icu.number.Precision
import online.yllh.smartinspect.extension.equalOrBeforeAndroid10
import java.util.Locale

/**
 * Created by ijays on 2023/11/6.
 */
object NumberFormatterUtils {
    val numberFormatter4: LocalizedNumberFormatter?
        get() {
            return if (equalOrBeforeAndroid10()) {
                null
            } else {
                NumberFormatter.withLocale(Locale.ENGLISH).precision(Precision.fixedFraction(4))
            }
        }

    val numberFormatter2: LocalizedNumberFormatter?
        get() {
            return if (equalOrBeforeAndroid10()) {
                null
            } else {
                NumberFormatter.withLocale(Locale.ENGLISH).precision(Precision.fixedFraction(2))
            }
        }
    val numberFormatter1: LocalizedNumberFormatter?
        get() {
            return if (equalOrBeforeAndroid10()) {
                null
            } else {
                NumberFormatter.withLocale(Locale.ENGLISH).precision(Precision.fixedFraction(1))
            }
        }

}