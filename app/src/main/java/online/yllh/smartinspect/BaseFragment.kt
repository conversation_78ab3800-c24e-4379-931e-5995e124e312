package online.yllh.smartinspect

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

open class BaseFragment : Fragment() {
    protected val viewLifecycleScope: LifecycleCoroutineScope
        get() = viewLifecycleOwner.lifecycleScope

    protected fun <T> observeOn(
        stateFlow: Flow<T>,
        state: Lifecycle.State = Lifecycle.State.STARTED,
        collector: FlowCollector<T>,
    ) {
        viewLifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(state) {
                stateFlow.collect(collector)
            }
        }
    }

    protected fun <T> observeLatestOn(
        flow: Flow<T>,
        state: Lifecycle.State = Lifecycle.State.STARTED,
        action: suspend (value: T) -> Unit
    ) {
        viewLifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(state) {
                flow.collectLatest(action)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(null)
    }

    override fun setInitialSavedState(state: SavedState?) {
        super.setInitialSavedState(null)
    }
}
