package online.yllh.smartinspect

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import online.yllh.smartinspect.dao.PhotoDao
import online.yllh.smartinspect.dao.RecycledPhotoDao
import online.yllh.smartinspect.entity.Photo
import online.yllh.smartinspect.entity.RecycledPhoto
import online.yllh.smartinspect.provider.ApplicationContextProvider

@Database(entities = [Photo::class, RecycledPhoto::class], version = 2)
abstract class AppDatabase : RoomDatabase() {
    abstract fun photoDao(): PhotoDao
    abstract fun recycledPhotoDao(): RecycledPhotoDao

    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null

        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS `recycled_photos` (
                        `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        `uri` TEXT NOT NULL,
                        `cachePath` TEXT NOT NULL,
                        `shotTime` INTEGER NOT NULL,
                        `longitude` REAL NOT NULL,
                        `latitude` REAL NOT NULL,
                        `deleteTime` INTEGER NOT NULL
                    )
                """.trimIndent())
            }
        }

        fun get(): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: Room.databaseBuilder(
                    ApplicationContextProvider.context,
                    AppDatabase::class.java,
                    "gst"
                ).addMigrations(MIGRATION_1_2).build().also { INSTANCE = it }
            }
        }
    }
}
