package online.yllh.smartinspect

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import online.yllh.smartinspect.model.WeatherResults

object GlobalVals {
    val weather = MutableStateFlow<WeatherResults.Result.Now?>(null)
    val networkStateEventSource = MutableSharedFlow<Boolean>()
    val networkStateEvent = networkStateEventSource.distinctUntilChanged()
}
