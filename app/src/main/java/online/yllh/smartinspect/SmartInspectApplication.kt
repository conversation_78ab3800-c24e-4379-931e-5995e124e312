package online.yllh.smartinspect

import android.app.Application
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkRequest
import android.util.Log
import androidx.core.content.getSystemService
import androidx.work.Configuration
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import online.yllh.smartinspect.provider.ApplicationContextProvider
import online.yllh.smartinspect.service.RecycleBinCleanupService

class SmartInspectApplication : Application(), Configuration.Provider {
    override fun onCreate() {
        super.onCreate()
        ApplicationContextProvider.initialize(this)
        observeNetworkState()
        
        // 初始化回收站自动清理服务
        RecycleBinCleanupService.schedulePeriodicCleanup()
    }

    private fun observeNetworkState() {
        val connectivityManager = getSystemService<ConnectivityManager>()

        val registerTime = System.currentTimeMillis()
        val networkCallback = object : ConnectivityManager.NetworkCallback() {
            private var first = true

            override fun onAvailable(network: Network) {
                // 网络可用
                Log.d("kofua", "网络可用")
                // ignore register first
                if (first && (System.currentTimeMillis() - registerTime < 1000)) {
                    Log.d("kofua", "Register first event ignored.")
                    first = false
                } else MainScope().launch {
                    GlobalVals.networkStateEventSource.emit(true)
                }
            }

            override fun onLost(network: Network) {
                // 网络不可用
                Log.d("kofua", "网络不可用")
                MainScope().launch {
                    GlobalVals.networkStateEventSource.emit(false)
                }
            }
        }

        val request = NetworkRequest.Builder().build()
        connectivityManager?.registerNetworkCallback(request, networkCallback)
    }

    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setMinimumLoggingLevel(Log.INFO)
            .build()
}
