package online.yllh.smartinspect

import android.annotation.SuppressLint
import android.os.Build
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.baidu.location.LocationClient
import com.baidu.mapapi.SDKInitializer
import com.tencent.bugly.crashreport.CrashReport
import com.tencent.bugly.crashreport.CrashReport.UserStrategy
import online.yllh.smartinspect.databinding.ActivityMainBinding
import online.yllh.smartinspect.dialog.UpdateDialog
import online.yllh.smartinspect.extension.observeWithLifecycle
import online.yllh.smartinspect.extension.viewBinding

class MainActivity : AppCompatActivity() {
    private val binding by viewBinding(ActivityMainBinding::inflate)
    private val viewModel by viewModels<MainViewModel>()
    private var updateDialog: UpdateDialog? = null

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(null)
        setContentView(binding.root)
        LocationClient.setAgreePrivacy(true)
        SDKInitializer.setAgreePrivacy(this.applicationContext, true)
        SDKInitializer.initialize(this.applicationContext)
        val strategy = UserStrategy(applicationContext)
        strategy.deviceID = "${Build.DEVICE} ${Build.MODEL}"
        CrashReport.initCrashReport(applicationContext, "24eba07186", true, strategy)
        viewModel.update.observeWithLifecycle(this) { (hasUpdate, apkInfo) ->
            if (hasUpdate && apkInfo != null) {
                updateDialog = UpdateDialog(
                    this,
                    apkInfo,
                    onDownloadClick = {
                        val progress = viewModel.apkDownloadProgress.value
                        if (progress == 0) {
                            apkInfo.apkAddress?.let { viewModel.downloadApk(it) }
                        } else if (progress == 100) {
                            viewModel.installApk(this@MainActivity)
                        }
                    },
                    onLaterClick = {}
                )
                updateDialog?.show()
            }
        }
        viewModel.apkDownloadProgress.observeWithLifecycle(this) { progress ->
            updateDialog?.updateDownloadProgress(progress)
            if (progress == 100) {
                viewModel.installApk(this)
            }
        }

        viewModel.apkDownloadError.observeWithLifecycle(this) { hasError ->
            if (hasError) {
                updateDialog?.showDownloadError()
            }
        }

        viewModel.checkUpdate()
        viewModel.fetchWeatherWhenLocated()
    }
}
