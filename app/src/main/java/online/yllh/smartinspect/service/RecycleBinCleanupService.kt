package online.yllh.smartinspect.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import androidx.work.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import online.yllh.smartinspect.AppDatabase
import online.yllh.smartinspect.provider.ApplicationContextProvider
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * 回收站自动清理服务
 * 负责清理超过30天的回收站照片
 */
class RecycleBinCleanupService : Service() {

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        performCleanup()
        return START_NOT_STICKY
    }

    private fun performCleanup() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val recycledPhotoDao = AppDatabase.get().recycledPhotoDao()
                val thirtyDaysAgo = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000L)
                
                // 获取过期的照片
                val allRecycledPhotos = recycledPhotoDao.getAllRecycledPhotos()
                val expiredPhotos = allRecycledPhotos.filter { it.deleteTime < thirtyDaysAgo }
                
                // 删除过期照片的本地文件
                expiredPhotos.forEach { recycledPhoto ->
                    try {
                        val file = File(recycledPhoto.cachePath)
                        if (file.exists()) {
                            file.delete()
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
                
                // 从数据库中删除过期记录
                recycledPhotoDao.deleteExpiredPhotos(thirtyDaysAgo)
                
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                stopSelf()
            }
        }
    }

    companion object {
        /**
         * 启动清理服务
         */
        fun startCleanup(context: Context) {
            val intent = Intent(context, RecycleBinCleanupService::class.java)
            context.startService(intent)
        }

        /**
         * 调度定期清理任务
         */
        fun schedulePeriodicCleanup() {
            val cleanupRequest = PeriodicWorkRequestBuilder<RecycleBinCleanupWorker>(1, TimeUnit.DAYS)
                .setConstraints(
                    Constraints.Builder()
                        .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                        .setRequiresBatteryNotLow(true)
                        .build()
                )
                .build()

            WorkManager.getInstance(ApplicationContextProvider.context)
                .enqueueUniquePeriodicWork(
                    "recycle_bin_cleanup",
                    ExistingPeriodicWorkPolicy.KEEP,
                    cleanupRequest
                )
        }
    }
}

/**
 * 回收站清理Worker
 */
class RecycleBinCleanupWorker(context: Context, params: WorkerParameters) : CoroutineWorker(context, params) {
    
    override suspend fun doWork(): Result {
        return try {
            val recycledPhotoDao = AppDatabase.get().recycledPhotoDao()
            val thirtyDaysAgo = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000L)
            
            // 获取过期的照片
            val allRecycledPhotos = recycledPhotoDao.getAllRecycledPhotos()
            val expiredPhotos = allRecycledPhotos.filter { it.deleteTime < thirtyDaysAgo }
            
            // 删除过期照片的本地文件
            expiredPhotos.forEach { recycledPhoto ->
                try {
                    val file = File(recycledPhoto.cachePath)
                    if (file.exists()) {
                        file.delete()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            
            // 从数据库中删除过期记录
            recycledPhotoDao.deleteExpiredPhotos(thirtyDaysAgo)
            
            Result.success()
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure()
        }
    }
}
