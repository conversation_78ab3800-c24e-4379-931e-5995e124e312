package online.yllh.smartinspect

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.core.content.FileProvider
import androidx.core.net.toUri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import online.yllh.smartinspect.network.RetrofitClient
import online.yllh.smartinspect.network.model.response.ApkInfo
import online.yllh.smartinspect.provider.ApplicationContextProvider
import online.yllh.smartinspect.utils.LocationHelper
import online.yllh.smartinspect.utils.launchX
import java.io.File
import java.util.concurrent.TimeUnit


class MainViewModel : ViewModel() {
    val update = MutableSharedFlow<Pair<Boolean, ApkInfo?>>()
    val apkDownloadProgress = MutableStateFlow(0)
    val apkDownloadError = MutableSharedFlow<Boolean>()
    private val apkDownloadPath by lazy { File(ApplicationContextProvider.context.filesDir, "update.apk").path }

    fun checkUpdate() = viewModelScope.launch(Dispatchers.IO) {
        val newest = runCatching {
            RetrofitClient.service.verifyApkVersion(BuildConfig.VERSION_CODE.toString())
                .let { if (it.ok) it.data else true }
        }.getOrElse { true }
        val apkInfo = if (!newest) runCatching {
            RetrofitClient.service.getNewestApkUrl().let { if (it.ok) it.data else null }
        }.getOrElse { null } else null
        Log.d("kofua", "checkUpdate, newest: $newest, apkInfo: $apkInfo")
        if (!newest && apkInfo != null && !apkInfo.apkAddress.isNullOrEmpty()) {
            update.emit(true to apkInfo)
        } else {
            update.emit(false to null)
        }
    }

    fun downloadApk(url: String) {
        val client = OkHttpClient.Builder()
            .callTimeout(60, TimeUnit.SECONDS)
            .build()
        val request = Request.Builder().url(url).get().build()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                client.newCall(request).execute().use { response ->
                    if (response.isSuccessful) {
                        val body = response.body!!
                        val length = body.contentLength()
                        val apkFile = File(apkDownloadPath).also { it.delete() }
                        body.byteStream().use { input ->
                            apkFile.outputStream().use { output ->
                                var bytesCopied: Long = 0
                                val buffer = ByteArray(8192)
                                var bytes = input.read(buffer)
                                while (bytes >= 0) {
                                    output.write(buffer, 0, bytes)
                                    bytesCopied += bytes
                                    val progress = ((bytesCopied.toFloat() / length) * 100).toInt().coerceAtMost(100)
                                    apkDownloadProgress.emit(progress)
                                    bytes = input.read(buffer)
                                }
                                apkDownloadProgress.emit(100)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    Toast.makeText(ApplicationContextProvider.context, "下载失败，请重新下载", Toast.LENGTH_SHORT).show()
                }
                apkDownloadProgress.emit(0)
                apkDownloadError.emit(true)
            }
        }
    }

    fun installApk(activity: MainActivity) {
        fun install() {
            val intent = Intent(Intent.ACTION_VIEW)
            val apkFile = File(apkDownloadPath)
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                val uri = FileProvider.getUriForFile(activity, activity.packageName + ".fileprovider", apkFile)
                intent.setDataAndType(uri, "application/vnd.android.package-archive")
            } else {
                intent.setDataAndType(Uri.fromFile(apkFile), "application/vnd.android.package-archive")
            }
            activity.startActivity(intent)
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (activity.packageManager.canRequestPackageInstalls()) {
                install()
            } else viewModelScope.launch {
                activity.launchX(Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES, "package:${activity.packageName}".toUri()))
                if (activity.packageManager.canRequestPackageInstalls())
                    install()
            }
        } else {
            install()
        }
    }

    fun fetchWeatherWhenLocated() = viewModelScope.launch {
        val sequentialDispatcher = Dispatchers.IO.limitedParallelism(1)
        LocationHelper.location.collect { loc ->
            val longitude = loc.longitude
            val latitude = loc.latitude
            if (longitude != 0.0 && latitude != 0.0) {
                withContext(sequentialDispatcher) {
                    if (GlobalVals.weather.value == null) {
                        runCatching {
                            val weatherResults = RetrofitClient.service.getWeather(latitude.toString(), longitude.toString())
                            weatherResults.data.results.firstOrNull()?.let {
                                GlobalVals.weather.value = it.now
                            }
                        }.onFailure {
                            Log.e("MainViewModel", "fetchWeather failed, msg: ${it.message}")
                        }
                    }
                }
            }
        }
    }
}
