package online.yllh.smartinspect.provider

import android.app.Application
import android.content.Context


class ApplicationContextProvider private constructor(private val application: Application) {
    companion object {
        private var instance: ApplicationContextProvider? = null

        fun initialize(application: Application) {
            if (instance == null) {
                instance = ApplicationContextProvider(application)
            }
        }

        val context: Context
            get() {
                checkNotNull(instance) { "ApplicationContextProvider must be initialized first" }
                return instance!!.application.applicationContext
            }
    }
}
