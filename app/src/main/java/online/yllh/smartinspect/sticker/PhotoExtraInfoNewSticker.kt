package online.yllh.smartinspect.sticker

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.TypedValue
import android.view.View.MeasureSpec
import android.widget.TextView
import androidx.core.view.children
import androidx.core.view.drawToBitmap
import androidx.core.view.isVisible
import online.yllh.smartinspect.extension.dpF
import online.yllh.smartinspect.extension.isNetworkAvailable
import online.yllh.smartinspect.model.WatermarkState
import online.yllh.smartinspect.provider.ApplicationContextProvider

/**
 * 新版水印贴纸类 - 移除顶部标题和蓝色背景，只保留绿点，文字改为白色
 */
class PhotoExtraInfoNewSticker(
    val showWatermark: Boolean,
    val showProject: Boolean,
    val projectName: String,
    val projectRemark: String,
    val watermarkState: WatermarkState,
) : BaseSticker() {
    override var stickerId = "dateAndLocationNewSticker"

    private var previewWidthDp = 375.dpF
    private var width = 0
    private var height = 0

    private var remarkRect = RectF()
    private var stickerRect = RectF()

    private fun splitTextIntoLines(text: String, maxCharsPerLine: Int): List<String> {
        if (text.isEmpty()) return emptyList()

        val lines = mutableListOf<String>()
        var currentIndex = 0

        while (currentIndex < text.length) {
            val endIndex = minOf(currentIndex + maxCharsPerLine, text.length)
            lines.add(text.substring(currentIndex, endIndex))
            currentIndex = endIndex
        }

        return lines
    }

    @SuppressLint("SetTextI18n")
    override fun createBitmap(width: Int, height: Int): Bitmap {
        this.width = width
        this.height = height
        val bitmap = super.createBitmap(width, height)
        val canvas = Canvas(bitmap)
        val shadowColor = Color.GRAY // 阴影颜色为灰色
        val shadowDx = 0f // 阴影在 x 轴上的偏移量为 2 像素
        val shadowDy = 0f // 阴影在 y 轴上的偏移量为 2 像素
        val shadowRadius = 2.dpF // 阴影的模糊半径为 3 像素

        val textSizeScale = width / previewWidthDp
        var projectLayoutLeft = 0f
        if (showProject) {
            val namePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                textSize = 12.dpF * textSizeScale
                color = Color.WHITE
                setShadowLayer(shadowRadius, shadowDx, shadowDy, shadowColor)
            }

            val remarkPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                textSize = 12.dpF * textSizeScale
                color = Color.BLACK
            }

            val nameWidth = namePaint.measureText(projectName)
            val nameFontMetrics = namePaint.fontMetrics
            val nameHeight = nameFontMetrics.descent - nameFontMetrics.ascent
            val namePadding = 30.dpF
            val marginBottom = 30.dpF

            val remarkLines = splitTextIntoLines(projectRemark, 10)
            val remarkFontMetrics = remarkPaint.fontMetrics
            val remarkLineHeight = remarkFontMetrics.descent - remarkFontMetrics.ascent
            val remarkTotalHeight = remarkLineHeight * remarkLines.size

            val maxRemarkWidth = remarkLines.maxOfOrNull { remarkPaint.measureText(it) } ?: 0f

            canvas.drawText(projectName, width - namePadding - nameWidth, height - marginBottom - nameHeight - remarkTotalHeight, namePaint)

            val remarkBgPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                color = Color.WHITE
                alpha = 200
            }

            val remarkStartY = height - marginBottom - remarkTotalHeight + remarkFontMetrics.descent
            val remarkX = width - namePadding - nameWidth / 2 - maxRemarkWidth / 2
            val remarkBgRadius = remarkLineHeight / 2
            val remarkBgY = remarkStartY + remarkFontMetrics.ascent
            val remarkPadding = 3.dpF
            remarkRect.set(remarkX - remarkPadding, remarkBgY, remarkX + maxRemarkWidth + remarkPadding, remarkBgY + remarkTotalHeight)
            canvas.drawRoundRect(remarkRect, remarkBgRadius, remarkBgRadius, remarkBgPaint)

            remarkLines.forEachIndexed { index, line ->
                val lineWidth = remarkPaint.measureText(line)
                val lineX = width - namePadding - nameWidth / 2 - lineWidth / 2
                val lineY = remarkStartY + index * remarkLineHeight
                canvas.drawText(line, lineX, lineY, remarkPaint)
            }

            val linePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                style = Paint.Style.STROKE
                strokeWidth = 1.dpF
                color = Color.WHITE
                setShadowLayer(shadowRadius, shadowDx, shadowDy, shadowColor)
            }
            val lineLeft = width - nameWidth - namePadding * 2 - 2.dpF
            canvas.drawLine(
                lineLeft,
                height - marginBottom - 38.dpF,
                lineLeft,
                height - marginBottom, linePaint
            )
            projectLayoutLeft = lineLeft
        } else remarkRect.setEmpty()

        if (showWatermark) {
            val context = ApplicationContextProvider.context
            val infoWatermarkView = PhotoInfoWatermarkNewView(context)
            infoWatermarkView.children.filterIsInstance<TextView>().forEach {
                it.setTextSize(TypedValue.COMPLEX_UNIT_PX, it.textSize * textSizeScale)
            }
            infoWatermarkView.binding.mark.isSelected = context.isNetworkAvailable()
            infoWatermarkView.binding.groupTime.isVisible = watermarkState.showTime
            infoWatermarkView.binding.groupWeather.isVisible = watermarkState.showWeather
            infoWatermarkView.binding.groupLocation.isVisible = watermarkState.showLocation
            infoWatermarkView.binding.groupAltitude.isVisible = watermarkState.showAltitude
            infoWatermarkView.binding.groupAzimuth.isVisible = watermarkState.showAzimuth
            infoWatermarkView.binding.groupAddress.isVisible = watermarkState.showAddress
            infoWatermarkView.binding.tvTime.text = watermarkState.readableTime
            infoWatermarkView.binding.tvWeather.text = watermarkState.weatherDesc
            infoWatermarkView.binding.tvLocation.text = watermarkState.locationDesc
            infoWatermarkView.binding.tvAltitude.text = watermarkState.altitudeDesc
            infoWatermarkView.binding.tvAzimuth.text = watermarkState.azimuthDesc
            infoWatermarkView.binding.tvAddress.text = watermarkState.address
            
            // 如果所有信息都不显示，至少显示绿点
            if (!watermarkState.showTime && !watermarkState.showWeather
                && !watermarkState.showLocation && !watermarkState.showAltitude && !watermarkState.showAzimuth
                && !watermarkState.showAddress
            ) {
                // 绿点始终显示
            }
            
            val margin = 12.dpF
            val infoWatermarkWidth = if (projectLayoutLeft != 0f) {
                (width * 0.6f).toInt().coerceAtMost((projectLayoutLeft - 10.dpF - margin).toInt())
            } else (width * 0.6f).toInt()
            infoWatermarkView.measure(
                MeasureSpec.makeMeasureSpec(infoWatermarkWidth, MeasureSpec.EXACTLY),
                MeasureSpec.makeMeasureSpec(height, MeasureSpec.AT_MOST),
            )
            infoWatermarkView.layout(0, 0, infoWatermarkView.measuredWidth, infoWatermarkView.measuredHeight)
            val watermarkBitmap = infoWatermarkView.drawToBitmap()
            stickerRect.set(margin, height - margin - infoWatermarkView.height, margin + infoWatermarkView.width, height - margin)
            canvas.drawBitmap(watermarkBitmap, null, stickerRect, null)
        } else {
            stickerRect.setEmpty()
        }

        return bitmap
    }

    override fun getStickerRect(): RectF {
        return if (showWatermark) {
            stickerRect
        } else RectF()
    }

    override fun getProjectRemarkRect(): RectF {
        return if (showProject) {
            remarkRect
        } else RectF()
    }
}
