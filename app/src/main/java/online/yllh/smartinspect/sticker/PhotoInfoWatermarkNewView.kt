package online.yllh.smartinspect.sticker

import android.content.Context
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import online.yllh.smartinspect.R
import online.yllh.smartinspect.databinding.ViewPhotoInfoWatermarkNewBinding
import online.yllh.smartinspect.extension.layoutInflater

class PhotoInfoWatermarkNewView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    val binding = ViewPhotoInfoWatermarkNewBinding.inflate(context.layoutInflater, this)

    init {
        setBackgroundResource(R.drawable.bg_photo_info_watermark)
    }
}
