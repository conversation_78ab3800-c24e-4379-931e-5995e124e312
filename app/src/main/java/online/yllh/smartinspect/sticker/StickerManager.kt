package online.yllh.smartinspect.sticker

import android.content.Context
import com.google.gson.Gson
import online.yllh.smartinspect.contents.SpKey
import online.yllh.smartinspect.uiwidget.sticker.StickerPanel

object StickerManager {
    var stickerList = arrayListOf(
        StickerPanel.StickerInfo("现场核验", "dateAndLocationSticker", "R.drawable.watermark"),
        StickerPanel.StickerInfo("现场核验(新)", "dateAndLocationNewSticker", "R.drawable.watermark")
    )

    fun getRecentlyStickerList(context: Context): List<StickerPanel.StickerInfo> {
        val sp = context.getSharedPreferences(SpKey.AppSP, Context.MODE_PRIVATE)
        val recentlySticker = sp.getString(SpKey.RecentlySticker, "").orEmpty()
        val gson = Gson()
        if (recentlySticker.isNotEmpty()) {
            val list = gson.fromJson<List<String>>(recentlySticker, List::class.java)
            return list.mapNotNull { recentlySticker ->
                stickerList.find { it.stickerId == recentlySticker }
            }
        } else {
            return emptyList()
        }
    }
}
