<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/navigation_graph"
    app:startDestination="@id/cameraFragment">

    <fragment
        android:id="@+id/cameraFragment"
        android:name="online.yllh.smartinspect.view.camera.CameraFragment"
        android:label="fragment_camera"
        tools:layout="@layout/fragment_camera">
        <action
            android:id="@+id/action_cameraFragment_to_snapshotResultFragment"
            app:destination="@id/snapshotResultFragment"
            app:restoreState="true"
            app:popUpToSaveState="true"/>
        <action
            android:id="@+id/action_cameraFragment_to_settingFragment"
            app:destination="@id/settingFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim" />
        <action
            android:id="@+id/action_cameraFragment_to_loginFragment"
            app:destination="@id/loginFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_cameraFragment_to_myFragment"
            app:destination="@id/myFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_cameraFragment_to_galleryFragment"
            app:destination="@id/galleryFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim" />
        <action
            android:id="@+id/action_cameraFragment_to_addressFragment"
            app:destination="@id/addressFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_cameraFragment_to_projectListFragment"
            app:destination="@id/projectListFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/snapshotResultFragment"
        android:name="online.yllh.smartinspect.view.snapshotResult.SnapshotResultFragment"
        android:label="SnapshotResultFragment" >
        <action
            android:id="@+id/action_snapshotResultFragment_to_addressFragment"
            app:destination="@id/addressFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_snapshotResultFragment_to_editResultFragment"
            app:destination="@id/editResultFragment"
            app:popUpTo="@id/cameraFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/settingFragment"
        android:name="online.yllh.smartinspect.view.setting.SettingFragment"
        android:label="SettingFragment" >
        <action
            android:id="@+id/action_settingFragment_to_loginFragment"
            app:destination="@id/loginFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_settingFragment_to_myFragment"
            app:destination="@id/myFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_settingFragment_to_userAgentFragment"
            app:destination="@id/userAgentFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_settingFragment_to_cacheFragment"
            app:destination="@id/cacheFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_settingFragment_to_updateFragment"
            app:destination="@id/updateFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_settingFragment_to_privacyFragment"
            app:destination="@id/privacyFragment" />
    </fragment>
    <fragment
        android:id="@+id/loginFragment"
        android:name="online.yllh.smartinspect.view.login.LoginFragment"
        android:label="fragment_login"
        app:route="loginFragment"
        tools:layout="@layout/fragment_login" >
        <action
            android:id="@+id/action_loginFragment_to_signupFragment"
            app:destination="@id/signupFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_loginFragment_to_myFragment"
            app:destination="@id/myFragment"
            app:popUpToInclusive="true"
            app:popUpTo="@id/loginFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_loginFragment_to_forgetPassFragment"
            app:destination="@id/forgetPassFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>

    </fragment>
    <fragment
        android:id="@+id/myFragment"
        android:name="online.yllh.smartinspect.view.my.MyFragment"
        android:label="fragment_my"
        tools:layout="@layout/fragment_my" >
        <action
            android:id="@+id/action_myFragment_to_changeUserInfoFragment"
            app:destination="@id/changeUserInfoFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_myFragment_to_loginFragment"
            app:destination="@id/loginFragment"
            app:popUpTo="@id/cameraFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/changeUserInfoFragment"
        android:name="online.yllh.smartinspect.view.changeUserInfo.ChangeUserInfoFragment"
        android:label="fragment_change_user_info"
        tools:layout="@layout/fragment_change_user_info" >
        <action
            android:id="@+id/action_changeUserInfoFragment_to_loginFragment"
            app:destination="@id/loginFragment"
            app:popUpTo="@id/settingFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_changeUserInfoFragment_to_changeUserPhoneFragment"
            app:destination="@id/changeUserPhoneFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/galleryFragment"
        android:name="online.yllh.smartinspect.view.gallery.GalleryFragment"
        android:label="fragment_gallery"
        tools:layout="@layout/fragment_gallery" >
        <action
            android:id="@+id/action_galleryFragment_to_photoAlbumFragment"
            app:destination="@id/photoAlbumFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_galleryFragment_to_editImageFragment"
            app:destination="@id/editImageFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_galleryFragment_to_cloudPhotoAlbumFragment"
            app:destination="@id/cloudPhotoAlbumFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_galleryFragment_to_loginFragment"
            app:destination="@id/loginFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_galleryFragment_to_uploadPhotoFragment"
            app:destination="@id/uploadPhotoFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/photoAlbumFragment"
        android:name="online.yllh.smartinspect.view.gallery.local.PhotoAlbumFragment"
        android:label="PhotoAlbumFragment" >
        <action
            android:id="@+id/action_photoAlbumFragment_to_editImageFragment"
            app:destination="@id/editImageFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/editImageFragment"
        android:name="online.yllh.smartinspect.view.editImage.EditImageFragment"
        android:label="fragment_edit_image"
        tools:layout="@layout/fragment_edit_image" >
        <action
            android:id="@+id/action_editImageFragment_to_watermarkEditFragment"
            app:destination="@id/watermarkEditFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/watermarkEditFragment"
        android:name="online.yllh.smartinspect.view.editImage.WatermarkEditFragment"
        android:label="fragment_watermark_edit"
        tools:layout="@layout/fragment_watermark_edit" >
        <action
            android:id="@+id/action_watermarkEditFragment_to_editResultFragment"
            app:destination="@id/editResultFragment"
            app:popUpTo="@id/cameraFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_watermarkEditFragment_to_addressFragment"
            app:destination="@id/addressFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/editResultFragment"
        android:name="online.yllh.smartinspect.view.editImage.EditResultFragment"
        android:label="EditResultFragment" >
    </fragment>
    <fragment
        android:id="@+id/signupFragment"
        android:name="online.yllh.smartinspect.view.signup.SignupFragment"
        android:label="SignupFragment" >
        <action
            android:id="@+id/action_signupFragment_to_myFragment"
            app:destination="@id/myFragment"
            app:popUpTo="@id/cameraFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/addressFragment"
        android:name="online.yllh.smartinspect.view.address.AddressFragment"
        android:label="fragment_address"
        tools:layout="@layout/fragment_address" >
        <action
            android:id="@+id/action_addressFragment_to_addressLevelFragment"
            app:destination="@id/addressLevelFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/addressLevelFragment"
        android:name="online.yllh.smartinspect.view.address.AddressLevelFragment"
        android:label="AddressLevelFragment" />
    <fragment
        android:id="@+id/forgetPassFragment"
        android:name="online.yllh.smartinspect.view.forgetPass.ForgetPassFragment"
        android:label="ForgetPassFragment" />
    <fragment
        android:id="@+id/cloudPhotoAlbumFragment"
        android:name="online.yllh.smartinspect.view.gallery.cloud.CloudPhotoAlbumFragment"
        android:label="CloudPhotoAlbumFragment" >
        <action
            android:id="@+id/action_cloudPhotoAlbumFragment_to_cloudPhotoDetailFragment"
            app:destination="@id/cloudPhotoDetailFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/cloudPhotoDetailFragment"
        android:name="online.yllh.smartinspect.view.gallery.cloud.CloudPhotoDetailFragment"
        android:label="fragment_cloud_photo_detail"
        tools:layout="@layout/fragment_cloud_photo_detail" >
        <action
            android:id="@+id/action_cloudPhotoDetailFragment_to_cloudPhotoExifDetailFragment"
            app:destination="@id/cloudPhotoExifDetailFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
        <action
            android:id="@+id/action_cloudPhotoDetailFragment_to_photoMessageFragment"
            app:destination="@id/photoMessageFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/cloudPhotoExifDetailFragment"
        android:name="online.yllh.smartinspect.view.gallery.cloud.CloudPhotoExifDetailFragment"
        android:label="fragment_cloud_photo_exif_detail"
        tools:layout="@layout/fragment_cloud_photo_exif_detail" >
        <action
            android:id="@+id/action_cloudPhotoExifDetailFragment_to_photoMessageFragment"
            app:destination="@id/photoMessageFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/cacheFragment"
        android:name="online.yllh.smartinspect.view.setting.CacheFragment"
        android:label="fragment_cache"
        tools:layout="@layout/fragment_cache" />
    <fragment
        android:id="@+id/userAgentFragment"
        android:name="online.yllh.smartinspect.view.setting.UserAgentFragment"
        android:label="fragment_user_agent"
        tools:layout="@layout/fragment_user_agent" />
    <fragment
        android:id="@+id/updateFragment"
        android:name="online.yllh.smartinspect.view.update.UpdateFragment"
        android:label="fragment_update"
        tools:layout="@layout/fragment_update" />
    <fragment
        android:id="@+id/changeUserPhoneFragment"
        android:name="online.yllh.smartinspect.view.changeUserInfo.ChangeUserPhoneFragment"
        android:label="ChangeUserPhoneFragment" />
    <fragment
        android:id="@+id/privacyFragment"
        android:name="online.yllh.smartinspect.view.setting.PrivacyFragment"
        android:label="PrivacyFragment" />
    <fragment
        android:id="@+id/projectListFragment"
        android:name="online.yllh.smartinspect.view.project.ProjectListFragment"
        android:label="fragment_project_list"
        tools:layout="@layout/fragment_project_list" />
    <fragment
        android:id="@+id/photoMessageFragment"
        android:name="online.yllh.smartinspect.view.message.PhotoMessageFragment"
        android:label="fragment_photo_message"
        app:route="photoMessageFragment"
        tools:layout="@layout/fragment_photo_message" />
    <fragment
        android:id="@+id/cloudAlbumFragment"
        android:name="online.yllh.smartinspect.view.gallery.cloud.CloudAlbumFragment"
        android:label="CloudAlbumFragment" />
    <fragment
        android:id="@+id/galleryInfoFragment"
        android:name="online.yllh.smartinspect.view.galleryInfo.GalleryInfoFragment"
        android:label="GalleryInfoFragment" />
    <fragment
        android:id="@+id/uploadPhotoFragment"
        android:name="online.yllh.smartinspect.view.gallery.unupload.UploadPhotoFragment"
        android:label="UploadPhotoFragment" />
</navigation>
