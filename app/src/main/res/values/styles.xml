<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="MyCheckBox" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">#FF10F9B7</item>
        <item name="colorControlActivated">#FF10F9B7</item>
    </style>

    <style name="CustomDialog" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>
</resources>