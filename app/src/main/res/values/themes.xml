<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.SmartInspect" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <!-- <item name="colorPrimary">@color/my_light_primary</item> -->
    </style>

    <style name="Theme.SmartInspect" parent="Base.Theme.SmartInspect" />

    <style name="MyTextInputLayoutStyle" parent="Widget.MaterialComponents.TextInputLayout.FilledBox">
        <!-- 设置提示文本字体大小 -->
        <item name="hintTextAppearance">@style/MyHintTextAppearance</item>
    </style>

    <style name="MessageTextInputLayoutStyle" parent="Widget.MaterialComponents.TextInputLayout.FilledBox">
        <!-- 设置提示文本字体大小 -->
        <item name="hintTextAppearance">@style/MyHintTextAppearance</item>
    </style>

    <style name="MyHintTextAppearance" parent="TextAppearance.Design.Hint">
        <!-- 设置提示文本字体大小 -->
        <item name="android:textSize">14dp</item> <!-- 设置为所需的字体大小 -->
    </style>
    <style name="MessageHintTextAppearance" parent="TextAppearance.Design.Hint">
        <!-- 设置提示文本字体大小 -->
        <item name="android:textSize">14dp</item>
        <item name="android:textColor">#FFCBCBCB</item><!-- 设置为所需的字体大小 -->
    </style>
</resources>
