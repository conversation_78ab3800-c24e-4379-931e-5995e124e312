<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 选中状态下的背景 -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="#FFFFFFFF" /> <!-- 设置背景颜色 -->
            <corners android:radius="11dp" />
        </shape>
    </item>
    <!-- 默认状态下的背景 -->
    <item android:drawable="@android:color/transparent"/> <!-- 设置背景颜色 -->
</selector>