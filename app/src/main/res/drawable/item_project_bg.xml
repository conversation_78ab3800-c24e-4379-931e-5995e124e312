<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 选中状态下的背景 -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <stroke android:width="2dp" android:color="#FF10F9B7" />
            <solid android:color="#1AFFFFFF" /> <!-- 设置背景颜色 -->
            <corners android:radius="12dp" />
        </shape>
    </item>
    <item android:state_selected="false">
        <shape android:shape="rectangle">
            <solid android:color="#1AFFFFFF" /> <!-- 设置背景颜色 -->
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>