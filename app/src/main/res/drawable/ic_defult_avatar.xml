<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="121dp"
    android:height="120dp"
    android:viewportWidth="121"
    android:viewportHeight="120">
  <path
      android:pathData="M60.5,60m-60,0a60,60 0,1 1,120 0a60,60 0,1 1,-120 0"
      android:strokeAlpha="0.20107886"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:fillAlpha="0.20107886">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="60.5"
          android:startY="-0"
          android:endX="60.5"
          android:endY="120"
          android:type="linear">
        <item android:offset="0" android:color="#FF806A6A"/>
        <item android:offset="1" android:color="#FF665654"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M93.03,60m60,0a60,60 0,1 0,-120 0a60,60 0,1 0,120 0"/>
    <path
        android:pathData="M64.85,19.88C63.25,12.88 70.83,7.51 78.65,11.2C97.05,19.88 86.37,32.88 79.77,31.48C73.17,30.08 66.45,26.88 64.85,19.88Z"
        android:strokeAlpha="0.20107886"
        android:strokeWidth="1"
        android:fillType="nonZero"
        android:strokeColor="#00000000"
        android:fillAlpha="0.20107886">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="80.15"
            android:startY="7.51"
            android:endX="80.15"
            android:endY="32.88"
            android:type="linear">
          <item android:offset="0" android:color="#FF1D0024"/>
          <item android:offset="1" android:color="#FF100014"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M60.5,60m-60,0a60,60 0,1 1,120 0a60,60 0,1 1,-120 0"/>
    <path
        android:pathData="M69.3,64C60.71,68.13 59.45,67.91 40.5,52.4C11.9,29 38.9,7.6 67.9,19C99.3,32.8 88.78,54.64 69.3,64Z"
        android:strokeAlpha="0.20107886"
        android:strokeWidth="1"
        android:fillType="nonZero"
        android:strokeColor="#00000000"
        android:fillAlpha="0.20107886">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="55.6"
            android:startY="7.6"
            android:endX="55.6"
            android:endY="68.13"
            android:type="linear">
          <item android:offset="0" android:color="#FF1D0024"/>
          <item android:offset="1" android:color="#FF100014"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M60.5,60m-60,0a60,60 0,1 1,120 0a60,60 0,1 1,-120 0"/>
    <path
        android:pathData="M70.1,80.4C67.17,86.8 63.43,90 60.5,90C57.57,90 53.83,86.8 50.9,80.4L50.9,72.4C50.03,72.43 49.3,72.43 48.7,72.4C39.34,72 36.22,56.95 37.69,41.9C38.76,30.87 48.1,46 63.3,35.4C62.7,47 67.7,45.8 69.9,44.4C79.9,39.8 80.1,58.6 70.22,57.14C70.22,57.34 70.18,65.1 70.1,80.4Z"
        android:strokeAlpha="0.20107886"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"
        android:fillAlpha="0.20107886">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="58.16"
            android:startY="30.87"
            android:endX="58.16"
            android:endY="90"
            android:type="linear">
          <item android:offset="0" android:color="#FFF7B186"/>
          <item android:offset="1" android:color="#FFFFC299"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M60.5,60m-60,0a60,60 0,1 1,120 0a60,60 0,1 1,-120 0"/>
    <path
        android:pathData="M50.9,72.4C54.5,72.13 58.98,70.12 64.35,66.36C62.71,72.03 58.22,75.37 50.9,76.4L50.9,72.4Z"
        android:strokeAlpha="0.20107886"
        android:strokeWidth="1"
        android:fillColor="#FF9768"
        android:fillType="nonZero"
        android:strokeColor="#00000000"
        android:fillAlpha="0.20107886"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60.5,60m-60,0a60,60 0,1 1,120 0a60,60 0,1 1,-120 0"/>
    <path
        android:pathData="M47.59,82.73C48,79.95 48.67,78.44 49.61,78.2C51.03,77.85 59.3,77.8 59.31,79.4C59.31,79.7 59.59,82.84 60.53,82.84C61.48,82.84 61.72,79.7 61.72,79.4C61.73,77.8 69.95,77.86 71.41,78.2C72.39,78.43 73.06,79.94 73.43,82.73C87.83,89.51 95.78,94.35 97.3,97.24C99.26,100.98 100.72,109.64 101.7,123.24L19.3,123.24C20.28,109.64 21.74,100.98 23.7,97.24C25.22,94.35 33.18,89.51 47.59,82.73Z"
        android:strokeAlpha="0.20107886"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"
        android:fillAlpha="0.20107886">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="60.5"
            android:startY="123.24"
            android:endX="60.5"
            android:endY="77.8"
            android:type="linear">
          <item android:offset="0" android:color="#FFFCF2EB"/>
          <item android:offset="1" android:color="#FFFFF9F5"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M60.5,60m-60,0a60,60 0,1 1,120 0a60,60 0,1 1,-120 0"/>
    <path
        android:pathData="M60.5,95.6C61.38,95.6 62.1,94.88 62.1,94C62.1,93.12 61.38,92.4 60.5,92.4C59.62,92.4 58.9,93.12 58.9,94C58.9,94.88 59.62,95.6 60.5,95.6ZM60.5,103.6C61.38,103.6 62.1,102.88 62.1,102C62.1,101.12 61.38,100.4 60.5,100.4C59.62,100.4 58.9,101.12 58.9,102C58.9,102.88 59.62,103.6 60.5,103.6ZM60.5,111.6C61.38,111.6 62.1,110.88 62.1,110C62.1,109.12 61.38,108.4 60.5,108.4C59.62,108.4 58.9,109.12 58.9,110C58.9,110.88 59.62,111.6 60.5,111.6Z"
        android:strokeAlpha="0.20107886"
        android:strokeWidth="1"
        android:fillColor="#664F4D"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"
        android:fillAlpha="0.20107886"/>
  </group>
  <group>
    <clip-path
        android:pathData="M74.89,60m60,0a60,60 0,1 0,-120 0a60,60 0,1 0,120 0"/>
    <path
        android:pathData="M64.74,31.77C58.34,47.77 61.94,55.57 66.94,64.97C70.27,71.24 69.87,77.3 65.74,83.17C70.54,80.1 72.94,75.17 72.94,68.37C72.94,58.17 65.74,51.77 71.74,43.37C77.74,34.97 71.14,15.77 64.74,31.77Z"
        android:strokeAlpha="0.20107886"
        android:strokeWidth="1"
        android:fillType="nonZero"
        android:strokeColor="#00000000"
        android:fillAlpha="0.20107886">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="68.04"
            android:startY="15.77"
            android:endX="68.04"
            android:endY="83.17"
            android:type="linear">
          <item android:offset="0" android:color="#FF1D0024"/>
          <item android:offset="1" android:color="#FF100014"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M22.86,60m60,0a60,60 0,1 0,-120 0a60,60 0,1 0,120 0"/>
    <path
        android:pathData="M41.68,49.64m1.64,0a1.64,1.64 0,1 0,-3.27 0a1.64,1.64 0,1 0,3.27 0"
        android:strokeAlpha="0.20107886"
        android:strokeWidth="1"
        android:fillColor="#403731"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"
        android:fillAlpha="0.20107886"/>
  </group>
  <group>
    <clip-path
        android:pathData="M47.95,60m60,0a60,60 0,1 0,-120 0a60,60 0,1 0,120 0"/>
    <path
        android:pathData="M54.23,49.64m1.64,0a1.64,1.64 0,1 0,-3.27 0a1.64,1.64 0,1 0,3.27 0"
        android:strokeAlpha="0.20107886"
        android:strokeWidth="1"
        android:fillColor="#403731"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"
        android:fillAlpha="0.20107886"/>
  </group>
  <group>
    <clip-path
        android:pathData="M33.77,60m60,0a60,60 0,1 0,-120 0a60,60 0,1 0,120 0"/>
    <path
        android:pathData="M49.32,55.09a1.09,2.18 90,1 1,-4.36 0a1.09,2.18 90,1 1,4.36 0z"
        android:strokeAlpha="0.20107886"
        android:strokeWidth="1"
        android:fillColor="#F1A16B"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"
        android:fillAlpha="0.20107886"/>
  </group>
  <group>
    <clip-path
        android:pathData="M35.41,60m60,0a60,60 0,1 0,-120 0a60,60 0,1 0,120 0"/>
    <path
        android:pathData="M47.71,63.82C45.72,63.82 43.86,62.71 43.86,61.18C43.86,59.66 45.14,61.34 47.71,61.39C50.29,61.44 52.05,59.66 52.05,61.18C52.05,62.71 49.71,63.82 47.71,63.82Z"
        android:strokeAlpha="0.20107886"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"
        android:fillAlpha="0.20107886"/>
  </group>
</vector>
