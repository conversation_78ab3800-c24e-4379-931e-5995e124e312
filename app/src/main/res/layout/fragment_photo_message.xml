<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/parentConstraintLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FF242424"
    tools:context=".view.message.PhotoMessageFragment">

    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="自定义信息"
        app:subMenuSrc="@drawable/ic_upload"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/messageList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBar" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/addMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="32dp"
        android:src="@drawable/ic_add_gallery"
        app:backgroundTint="#FF10F9B7"
        app:layout_constraintBottom_toBottomOf="@+id/messageList"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBar"
        app:layout_constraintVertical_bias="1.0"/>

    <online.yllh.smartinspect.uiwidget.MessageEditView
        android:id="@+id/inputPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:background="@drawable/bg_input_message_panel"
        app:layout_constraintBottom_toBottomOf="@+id/messageList"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <EditText
        android:id="@+id/fullEdit"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        style="@style/MessageTextInputLayoutStyle"
        android:hint="输入内容"
        android:visibility="gone"
        android:gravity="top"
        android:inputType="text"
        android:imeOptions="actionDone"
        android:focusable="true"
        android:textColor="@color/black"
        android:paddingStart="16dp"
        android:paddingTop="5dp"
        android:paddingEnd="16dp"
        android:paddingBottom="5dp"
        app:layout_constraintBottom_toBottomOf="@+id/messageList"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBar" />

</androidx.constraintlayout.widget.ConstraintLayout>