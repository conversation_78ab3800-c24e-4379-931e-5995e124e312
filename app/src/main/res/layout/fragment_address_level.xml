<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".view.address.AddressFragment">

    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:background="#FF333333"
        android:paddingTop="26dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBar">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:gravity="center">
            <TextView
                android:id="@+id/area"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="成都市"
                android:textColor="@color/white"
                android:textSize="24dp"/>
            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/levelSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:thumbTint="@color/white"
                app:trackTint="@drawable/switch_track_color_checked"/>
        </LinearLayout>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:background="#FF242424"
            android:gravity="center_vertical"
            android:textColor="#9FFFFFFF"
            android:textSize="14dp"
            android:text="从下列勾选地址显示级别, 可设置省/市/"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/addressList"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>