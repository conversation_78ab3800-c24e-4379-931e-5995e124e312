<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_photo_info_watermark"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_photo_info_watermark_title"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:paddingVertical="2dp"
        android:text="现场记录"
        android:textColor="@color/white"
        android:textSize="14dp"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/mark"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:layout_marginStart="8dp"
        android:background="@drawable/bg_photo_info_watermark_title_mark"
        app:layout_constraintBottom_toBottomOf="@id/tvTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvTitle" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvTitle,mark"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvTittleTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:text="拍摄时间:"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@id/tvTime"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvTime" />

    <TextView
        android:id="@+id/tvTime"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="2dp"
        android:layout_marginEnd="4dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvTittleTime"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        tools:text="2025.06.23 21:48" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvTittleTime,tvTime"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvTitleWeather"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:text="天\u3000\u3000气:"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@id/tvWeather"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvWeather" />

    <TextView
        android:id="@+id/tvWeather"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="2dp"
        android:layout_marginEnd="4dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvTitleWeather"
        app:layout_constraintTop_toBottomOf="@id/tvTime"
        tools:text="晴 28℃" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupWeather"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvTitleWeather,tvWeather"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvTitleLocation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:text="定\u3000\u3000位:"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@id/tvLocation"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvLocation" />

    <TextView
        android:id="@+id/tvLocation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="2dp"
        android:layout_marginEnd="4dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvTitleLocation"
        app:layout_constraintTop_toBottomOf="@id/tvWeather"
        tools:text="104.123°N 30.456°E" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupLocation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvTitleLocation,tvLocation" />

    <TextView
        android:id="@+id/tvTitleAltitude"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:text="海\u3000\u3000拔:"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@id/tvAltitude"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvAltitude" />

    <TextView
        android:id="@+id/tvAltitude"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="2dp"
        android:layout_marginEnd="4dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvTitleAltitude"
        app:layout_constraintTop_toBottomOf="@id/tvLocation"
        tools:text="500米" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupAltitude"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvTitleAltitude,tvAltitude" />

    <TextView
        android:id="@+id/tvTitleAzimuth"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:text="角\u3000\u3000度:"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@id/tvAzimuth"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvAzimuth" />

    <TextView
        android:id="@+id/tvAzimuth"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="2dp"
        android:layout_marginEnd="4dp"
        android:ellipsize="end"
        android:fontFeatureSettings="'tnum'"
        android:maxLines="1"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvTitleAzimuth"
        app:layout_constraintTop_toBottomOf="@id/tvAltitude"
        tools:text="300°" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupAzimuth"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvTitleAzimuth,tvAzimuth" />

    <TextView
        android:id="@+id/tvTitleAddress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:text="地\u3000\u3000点:"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@id/tvAddress"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvAddress" />

    <TextView
        android:id="@+id/tvAddress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="2dp"
        android:layout_marginEnd="4dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvTitleAddress"
        app:layout_constraintTop_toBottomOf="@id/tvAzimuth"
        tools:text="华阳街道·鸿阁一号" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupAddress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvTitleAddress,tvAddress" />
</merge>
