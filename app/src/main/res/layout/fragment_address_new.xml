<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/addressSelectPanel"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <View
            android:id="@+id/addressMaskView"
            android:layout_width="match_parent"
            android:layout_height="154dp"
            android:alpha="0.3"
            android:background="#FF000000" />

        <LinearLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="0dp"
            android:background="#FFFFFFFF"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingTop="26dp"
            android:paddingEnd="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:gravity="center"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintStart_toStartOf="parent">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_select_address" />

                    <TextView
                        android:id="@+id/area"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="4dp"
                        android:layout_weight="1"
                        android:text="成都市"
                        android:textColor="#FF333333"
                        android:textSize="14dp" />
                </LinearLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/input_bg"
                    android:gravity="center">

                    <EditText
                        android:id="@+id/searchInput"
                        style="@style/MyTextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/bg_gray_6round"
                        android:hint="输入地址搜索"
                        android:inputType="text"
                        android:paddingStart="10dp"
                        android:paddingTop="10dp"
                        android:paddingEnd="10dp"
                        android:paddingBottom="10dp"
                        android:textColor="@color/black"
                        android:textColorHint="#FF999999"
                        android:textSize="14dp" />

                    <ImageView
                        android:id="@+id/clean"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="16dp"
                        android:src="@drawable/ic_clean" />
                </RelativeLayout>
            </LinearLayout>
            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:layout_marginTop="6dp"-->
            <!--                        android:layout_marginBottom="16dp"-->
            <!--                        android:textColor="#9FFFFFFF"-->
            <!--                        android:textSize="14dp"-->
            <!--                        android:text="地址显示级别, 可设置省/市/区/镇"/>-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/changeAddressLevel"
                    android:layout_width="77dp"
                    android:layout_height="26dp"
                    android:background="@drawable/btn_bg_grey_13round"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:src="@drawable/ic_edit_white" />

                    <View
                        android:layout_width="5dp"
                        android:layout_height="0dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="修改"
                        android:textColor="#FF333333"
                        android:textSize="14dp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/clearAddressLevel"
                    android:layout_width="133dp"
                    android:layout_height="26dp"
                    android:layout_marginLeft="14dp"
                    android:background="@drawable/btn_bg_grey_13round"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:src="@drawable/ic_clear_white" />

                    <View
                        android:layout_width="5dp"
                        android:layout_height="0dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="清空地址信息"
                        android:textColor="#FF333333"
                        android:textSize="14dp" />
                </LinearLayout>
            </LinearLayout>


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/addressList"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
