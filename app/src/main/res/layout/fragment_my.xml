<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.my.MyFragment">

    <!-- TODO: Update blank fragment layout -->
    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="我的账号"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBar" >

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.35000002">

            <online.yllh.smartinspect.uiwidget.maskImage.CircularImage
                android:id="@+id/avatar"
                android:layout_width="161dp"
                android:layout_height="161dp"
                android:src="@mipmap/ic_launcher" />

            <View
                android:layout_width="wrap_content"
                android:layout_height="20dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="昵称"
                android:textColor="#FFA7A7A7"
                android:textSize="16dp" />

            <View
                android:layout_width="wrap_content"
                android:layout_height="6dp" />

            <TextView
                android:id="@+id/nickName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="用户昵称"
                android:textColor="#FFFFFF"
                android:textSize="24dp" />

            <View
                android:layout_width="wrap_content"
                android:layout_height="20dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="手机号"
                android:textColor="#FFA7A7A7"
                android:textSize="16dp" />

            <View
                android:layout_width="wrap_content"
                android:layout_height="6dp" />

            <TextView
                android:id="@+id/phoneNum"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="13888888888"
                android:textColor="#FFFFFF"
                android:textSize="24dp" />

            <View
                android:layout_width="wrap_content"
                android:layout_height="80dp" />

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/changeUserInfo"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginStart="67dp"
                android:layout_marginEnd="67dp"
                app:style="green"
                app:text="修改资料" />

            <View
                android:layout_width="wrap_content"
                android:layout_height="10dp" />

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/logOut"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginStart="67dp"
                android:layout_marginEnd="67dp"
                app:style="grey"
                app:text="退出登录" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>