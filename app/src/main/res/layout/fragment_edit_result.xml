<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.editImage.EditImageFragment">

    <LinearLayout
        android:id="@+id/linearLayout5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="64dp"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="照片已存储到本地，可在未同步相册中查看。"
            android:textColor="@color/white" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="15dp"
            android:layout_marginStart="6dp"
            android:src="@drawable/ic_done_green"/>
    </LinearLayout>

    <ImageView
        android:id="@+id/mainImage"
        android:layout_width="283dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="21dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout5"
        app:layout_constraintVertical_bias="0.0" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="141dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">
        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/upload"
            android:layout_width="241dp"
            android:layout_height="40dp"
            app:text="上传云端" />
        <View
            android:layout_width="wrap_content"
            android:layout_height="10dp" />

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/back"
            android:layout_width="241dp"
            android:layout_height="40dp"
            app:style="grey"
            app:text="返回首页" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>