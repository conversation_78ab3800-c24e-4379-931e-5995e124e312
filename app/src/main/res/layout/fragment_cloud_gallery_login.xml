<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.gallery.cloud.CloudGalleryLoginFragment">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="80dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_info" />

        <View
            android:layout_width="0dp"
            android:layout_height="40dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="当前没有登录, 无法使用云端相册功能"
            android:textColor="@color/white"
            android:textSize="16dp" />

        <View
            android:layout_width="0dp"
            android:layout_height="40dp" />

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/login"
            android:layout_width="241dp"
            android:layout_height="40dp"
            app:text="去登录" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>