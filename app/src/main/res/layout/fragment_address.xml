<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".view.address.AddressFragment">

    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:background="#FF333333"
        android:paddingTop="26dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBar">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center">
            <TextView
                android:id="@+id/area"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="成都市"
                android:textColor="@color/white"
                android:textSize="24dp"/>
            <LinearLayout
                android:id="@+id/changeAddressLevel"
                android:layout_width="77dp"
                android:layout_height="26dp"
                android:orientation="horizontal"
                android:gravity="center"
                android:background="@drawable/btn_bg_grey_13round">
                <ImageView
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:src="@drawable/ic_edit_green"/>
                <View
                    android:layout_width="5dp"
                    android:layout_height="0dp"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="14dp"
                    android:text="修改"/>
            </LinearLayout>
        </LinearLayout>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="16dp"
            android:textColor="#9FFFFFFF"
            android:textSize="14dp"
            android:text="地址显示级别, 可设置省/市/区/镇"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginBottom="30dp"
            android:background="@drawable/input_bg">
            <EditText
                android:id="@+id/searchInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="输入地址搜索"
                style="@style/MyTextInputLayoutStyle"
                android:background="#00ffffff"
                android:textColorHint="#FF999999"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:inputType="text"
                android:textColor="@color/black" />
            <ImageView
                android:id="@+id/clean"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:src="@drawable/ic_clean"/>
        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/addressList"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>