<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/photoImageView"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="3dp">
        <ImageView
            android:id="@+id/imageView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"/>
        <FrameLayout
            android:id="@+id/delete"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@drawable/bg_delete_icon">
            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_album_delete"/>
        </FrameLayout>
    </FrameLayout>
    <View
        android:layout_width="wrap_content"
        android:layout_height="6dp"/>
    <TextView
        android:id="@+id/itemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="16dp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:text="标题"/>
    <View
        android:layout_width="wrap_content"
        android:layout_height="2dp"/>
    <TextView
        android:id="@+id/subTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="14dp"
        android:textStyle="bold"
        android:textColor="#FF999999"
        android:text="标题"/>
</LinearLayout>
