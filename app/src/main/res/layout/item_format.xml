<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:ignore="UseCompoundDrawables">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvFormat"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:paddingVertical="10dp"
            android:textColor="@color/format_title"
            android:textSize="14dp"
            tools:text="111111111111111" />

        <ImageView
            android:id="@+id/ivCheck"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_check"
            android:visibility="invisible"
            tools:visibility="visible" />
    </LinearLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#DDDDDD" />
</LinearLayout>
