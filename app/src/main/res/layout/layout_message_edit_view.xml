<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="300dp">

    <LinearLayout
        android:id="@+id/inputLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="18dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0">

        <ImageView
            android:id="@+id/voice"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_voice"
            app:tint="@color/black" />

        <EditText
            android:id="@+id/messageInput"
            style="@style/MessageTextInputLayoutStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/input_item_transparent_bg"
            android:hint="输入内容"
            android:inputType="text"
            android:imeOptions="actionDone"
            android:focusable="true"
            android:paddingStart="16dp"
            android:paddingTop="5dp"
            android:paddingEnd="16dp"
            android:paddingBottom="5dp"
            android:textColor="@color/black" />

        <ImageView
            android:id="@+id/fullScreen"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_full_sceen" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/recordVoice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        android:layout_marginBottom="14dp"
        android:layout_marginTop="20dp"
        app:layout_constraintBottom_toTopOf="@+id/linearLayout7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/inputLayout">

        <Chronometer
            android:id="@+id/timerText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12dp"
            android:textColor="#FF333333"
            android:layout_centerInParent="true"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="250dp"
            android:layout_height="24dp"
            android:layout_weight="1"
            android:background="@drawable/voice_item_bg">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_voice_wave"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginTop="15dp"
            android:background="@drawable/btn_bg_oval_stroke_green">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_trans" />
        </FrameLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="松开添加语音消息, 上滑取消"
            android:textColor="#FF999999"
            android:textSize="14dp" />
    </LinearLayout>
    <LinearLayout
        android:id="@+id/cancelRecord"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="12dp"
        app:layout_constraintBottom_toTopOf="@+id/linearLayout7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/inputLayout">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="松开取消添加语音消息"
            android:textColor="#FF999999"
            android:textSize="14dp" />
        <FrameLayout
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginTop="13dp"
            android:background="@drawable/btn_bg_oval_stroke_red">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_trans"
                app:tint="#FFEC5628" />
        </FrameLayout>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/linearLayout7"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/inputLayout">

        <FrameLayout
            android:id="@+id/holdSpeaker"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:background="@drawable/btn_circe_bg_green">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_voice"
                app:tint="@color/white" />
        </FrameLayout>

        <TextView
            android:id="@+id/holdSpeakerText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="按住说话"
            android:textColor="#FF999999"
            android:textSize="14dp" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>