<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="65dp"
    android:gravity="center_vertical"
    android:orientation="vertical">
    <TextView
        android:id="@+id/dateTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FF999999"
        android:layout_marginBottom="10dp"
        android:textSize="14dp"
        android:text="2023-10-1"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">
        <LinearLayout
            android:id="@+id/voiceItem"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:layout_weight="1"
            >

            <View
                android:layout_width="10dp"
                android:layout_height="0dp"/>
            <ImageView
                android:id="@+id/play"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_voice_play"/>
            <ImageView
                android:id="@+id/stop"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_pause"/>
            <ProgressBar
                android:id="@+id/progress"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:indeterminate="true"/>
            <View
                android:layout_width="10dp"
                android:layout_height="0dp"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/voice_item_bg">

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:src="@drawable/ic_voice_wave"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <View
                android:layout_width="8dp"
                android:layout_height="0dp"/>
            <TextView
                android:id="@+id/second"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="s"
                android:textColor="#FF666666"
                android:textSize="12dp"/>
        </LinearLayout>
        <LinearLayout
            android:id="@id/messageItem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_weight="1"
            android:gravity="center">
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#FFF0F0F0"
                android:padding="9dp">
                <TextView
                    android:id="@+id/message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#FF666666"
                    android:textSize="14dp"
                    android:text="自定义信息"/>
            </FrameLayout>
        </LinearLayout>
        <ImageView
            android:id="@+id/delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:src="@drawable/ic_trans"/>
    </LinearLayout>
</LinearLayout>