<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.login.LoginFragment">

    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <LinearLayout
        android:id="@+id/logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="88dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="67dp"
            android:layout_height="67dp"
            android:layout_marginBottom="6dp"
            android:src="@mipmap/ic_launcher" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="古树通相机"
            android:textColor="@color/white"
            android:textSize="18dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="241dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="70dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/logo">

        <EditText
            android:id="@+id/phoneNumEditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_phone_num"
            android:drawablePadding="8dp"
            android:hint="请输入手机号"
            style="@style/MyTextInputLayoutStyle"
            android:background="@drawable/input_item_bg"
            android:textColorHint="#99FFFFFF"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:inputType="phone"
            android:textColor="@color/white" />
        <View
            android:layout_width="wrap_content"
            android:layout_height="20dp"/>
        <EditText
            android:id="@+id/passNumEditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_pass_word"
            android:drawablePadding="8dp"
            android:hint="请输入密码"
            style="@style/MyTextInputLayoutStyle"
            android:background="@drawable/input_item_bg"
            android:textColorHint="#99FFFFFF"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:inputType="textPassword"
            android:textColor="@color/white" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp">

            <CheckBox
                android:id="@+id/rememberPass"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="3dp"
                android:text="记住密码"
                android:textColor="@color/white"
                android:textSize="12dp"
                android:theme="@style/MyCheckBox"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/forgetPass"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="spread_inside"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/forgetPass"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="忘记密码"
                android:textColor="#FF10F9B7"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/rememberPass"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/login"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            app:style="green"
            app:text="登录"/>
        <View
            android:layout_width="wrap_content"
            android:layout_height="10dp"/>
        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/register"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            app:style="grey"
            app:text="注册"/>
        <View
            android:layout_width="wrap_content"
            android:layout_height="20dp"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFFFF"
            android:textSize="14dp"
            android:text="客服电话 : 010-86461432"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>