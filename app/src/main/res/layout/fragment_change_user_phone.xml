<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.changeUserInfo.ChangeUserInfoFragment">

    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="修改手机号"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBar" >

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.35000002">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <online.yllh.smartinspect.uiwidget.maskImage.CircularImage
                    android:id="@+id/avatar"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:src="@mipmap/ic_launcher"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="wrap_content"
                android:layout_height="40dp" />

            <LinearLayout
                android:layout_width="241dp"
                android:layout_height="40dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:background="@drawable/input_item_bg">
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_phone_num"/>
                <TextView
                    android:id="@+id/orgPhoneNum"
                    android:paddingStart="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14dp"
                    android:textColor="#FF8F8F8F"
                    android:text="原手机号"/>
            </LinearLayout>
            <View
                android:layout_width="wrap_content"
                android:layout_height="10dp" />
            <LinearLayout
                android:layout_width="241dp"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">
                <EditText
                    android:id="@+id/phoneNum"
                    android:layout_width="160dp"
                    android:layout_height="wrap_content"
                    android:drawableStart="@drawable/ic_phone_num"
                    android:drawablePadding="8dp"
                    style="@style/MyTextInputLayoutStyle"
                    android:background="@drawable/input_item_bg"
                    android:textColorHint="#99FFFFFF"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:hint="请输入手机号"
                    android:inputType="phone"
                    android:textColor="@color/white" />
                <View
                    android:layout_width="10dp"
                    android:layout_height="0dp" />
                <online.yllh.smartinspect.uiwidget.GradientButton
                    android:id="@+id/sendSmsCode"
                    android:layout_width="71dp"
                    android:layout_height="40dp"
                    app:text="发送"/>
            </LinearLayout>
            <View
                android:layout_width="wrap_content"
                android:layout_height="10dp" />
            <EditText
                android:id="@+id/verifyCode"
                android:layout_width="241dp"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_verify_code"
                android:drawablePadding="8dp"
                style="@style/MyTextInputLayoutStyle"
                android:background="@drawable/input_item_bg"
                android:textColorHint="#99FFFFFF"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:hint="请输入验证码"
                android:inputType="text"
                android:textColor="@color/white" />
            <View
                android:layout_width="wrap_content"
                android:layout_height="52dp" />

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/okButton"
                android:layout_width="241dp"
                android:layout_height="40dp"
                app:style="green"
                app:text="确定" />

            <View
                android:layout_width="wrap_content"
                android:layout_height="10dp" />

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/cancelButton"
                android:layout_width="241dp"
                android:layout_height="40dp"
                app:style="grey"
                app:text="取消" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>