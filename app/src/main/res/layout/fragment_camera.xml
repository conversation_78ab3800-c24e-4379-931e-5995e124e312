<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.camera.CameraFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/parentConstraintLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false">

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/flash"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_flash"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/focalLength"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
                <!--                <ImageView-->
                <!--                    android:id="@+id/setting"-->
                <!--                    android:layout_width="32dp"-->
                <!--                    android:layout_height="32dp"-->
                <!--                    android:src="@drawable/ic_setting"-->
                <!--                    app:layout_constraintBottom_toBottomOf="parent"-->
                <!--                    app:layout_constraintEnd_toStartOf="@+id/focalLength"-->
                <!--                    app:layout_constraintHorizontal_chainStyle="spread_inside"-->
                <!--                    app:layout_constraintStart_toStartOf="parent"-->
                <!--                    app:layout_constraintTop_toTopOf="parent" />-->

                <ImageView
                    android:id="@+id/focalLength"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_focal_length"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/countDown"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/flash"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/countDown"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_count_down"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/setting"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/focalLength"
                    app:layout_constraintTop_toTopOf="parent" />

                <!--                <ImageView-->
                <!--                    android:id="@+id/switchCamera"-->
                <!--                    android:layout_width="32dp"-->
                <!--                    android:layout_height="32dp"-->
                <!--                    android:src="@drawable/ic_switch_camera"-->
                <!--                    app:layout_constraintBottom_toBottomOf="parent"-->
                <!--                    app:layout_constraintEnd_toStartOf="@+id/countDown"-->
                <!--                    app:layout_constraintHorizontal_bias="0.5"-->
                <!--                    app:layout_constraintStart_toEndOf="@+id/focalLength"-->
                <!--                    app:layout_constraintTop_toTopOf="parent" />-->

                <ImageView
                    android:id="@+id/setting"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_setting"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/countDown"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <online.yllh.smartinspect.uiwidget.VerticalDashedLineView
                android:layout_width="1dp"
                android:layout_height="18dp"
                android:layout_marginEnd="16dp" />

            <ImageView
                android:id="@+id/my"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_my" />
        </LinearLayout>

        <TextureView
            android:id="@+id/cameraPreviewView"
            android:layout_width="match_parent"
            android:layout_height="500dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/linearLayout"
            app:layout_constraintVertical_bias="0.0" />

        <TextView
            android:id="@+id/countDownText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="5"
            android:textColor="#10F9B7"
            android:textSize="240dp"
            app:layout_constraintBottom_toTopOf="@+id/constraintLayout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/cameraPreviewView"
            app:layout_constraintTop_toTopOf="@+id/cameraPreviewView" />

        <!-- <LinearLayout
             android:id="@+id/photoProjectViewLayout"
             android:layout_width="wrap_content"
             android:layout_height="wrap_content"
             android:layout_marginBottom="30dp"
             android:gravity="center"
             android:orientation="horizontal"
             app:layout_constraintBottom_toBottomOf="@id/cameraPreviewView"
             app:layout_constraintEnd_toEndOf="@+id/cameraPreviewView"
             app:layout_constraintHorizontal_bias="1.0"
             app:layout_constraintStart_toStartOf="@+id/cameraPreviewView">

             <online.yllh.smartinspect.uiwidget.VerticalDividingLineView
                 android:layout_width="1dp"
                 android:layout_height="38dp" />

             <online.yllh.smartinspect.uiwidget.PhotoProjectView
                 android:id="@+id/photoProjectView"
                 android:layout_width="wrap_content"
                 android:layout_height="42dp"
                 android:paddingStart="30dp"
                 android:paddingEnd="30dp" />
         </LinearLayout>-->

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cameraPreviewView"
            app:layout_constraintVertical_bias="0.0">

            <LinearLayout
                android:id="@+id/projectListView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:id="@+id/projectList"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginEnd="30dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="6dp"
                        android:src="@drawable/ic_project_list" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="项目列表"
                        android:textColor="@drawable/switch_project_model_text_color"
                        android:textSize="14dp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/projectNameLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_project_select"
                        app:tint="@drawable/switch_project_select_ic_color" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:src="@drawable/ic_project"
                            app:tint="@drawable/switch_project_model_text_color" />

                        <TextView
                            android:id="@+id/projectNameText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@drawable/switch_project_model_text_color"
                            android:textSize="14dp" />
                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bottomLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="72dp"
                android:paddingStart="15dp"
                android:paddingTop="0dp"
                android:paddingEnd="15dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0">

                <LinearLayout
                    android:id="@+id/linearLayout3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="18dp"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/snapshotButton"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:id="@+id/galleryContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical">

                        <online.yllh.smartinspect.uiwidget.HomeRoundIcon
                            android:id="@+id/gallery"
                            android:layout_width="44dp"
                            android:layout_height="44dp"
                            app:imageHeight="30dp"
                            app:imageWidth="30dp"
                            app:src="@drawable/ic_gallery" />

                        <TextView
                            android:id="@+id/galleryTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="10dp"
                            android:text="相册"
                            android:textColor="@android:color/white"
                            android:textSize="12dp" />
                    </LinearLayout>

                    <View
                        android:layout_width="20dp"
                        android:layout_height="0dp" />

                    <LinearLayout
                        android:id="@+id/locationContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical">

                        <online.yllh.smartinspect.uiwidget.HomeRoundIcon
                            android:id="@+id/location"
                            android:layout_width="44dp"
                            android:layout_height="44dp"
                            app:imageHeight="30dp"
                            app:imageWidth="30dp"
                            app:src="@drawable/ic_location" />

                        <TextView
                            android:id="@+id/locationTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="10dp"
                            android:text="地点"
                            android:textColor="@android:color/white"
                            android:textSize="12sp" />
                    </LinearLayout>
                </LinearLayout>

                <online.yllh.smartinspect.uiwidget.SnapshotButton
                    android:id="@+id/snapshotButton"
                    android:layout_width="75dp"
                    android:layout_height="75dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/linearLayout4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="18dp"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:id="@+id/switchContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"


                        android:orientation="horizontal"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="@id/snapshotButton"
                        app:layout_constraintHorizontal_bias="1.0"
                        app:layout_constraintStart_toEndOf="@+id/snapshotButton"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="1.0">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical">

                            <online.yllh.smartinspect.uiwidget.HomeRoundIcon
                                android:id="@+id/switchCamera"
                                android:layout_width="44dp"
                                android:layout_height="44dp"
                                app:imageHeight="30dp"
                                app:imageWidth="30dp"
                                app:src="@drawable/ic_switch_camera" />

                            <TextView
                                android:id="@+id/switchTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="10dp"
                                android:text="翻转"
                                android:textColor="@android:color/white"
                                android:textSize="12dp" />
                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="20dp"
                        android:layout_height="0dp" />

                    <LinearLayout
                        android:id="@+id/watermarkContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"


                        android:orientation="horizontal"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="1.0"
                        app:layout_constraintStart_toEndOf="@+id/snapshotButton"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="1.0">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical">

                            <online.yllh.smartinspect.uiwidget.HomeRoundIcon
                                android:id="@+id/watermark"
                                android:layout_width="44dp"
                                android:layout_height="44dp"
                                app:imageHeight="30dp"
                                app:imageWidth="30dp"
                                app:src="@drawable/ic_watermark" />

                            <TextView
                                android:id="@+id/watermarkTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="10dp"
                                android:text="水印"
                                android:textColor="@android:color/white"
                                android:textSize="12dp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/focalModeSelectView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="8dp"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="@+id/cameraPreviewView"
            app:layout_constraintEnd_toEndOf="@+id/cameraPreviewView"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="@+id/cameraPreviewView"
            app:layout_constraintVertical_bias="0.0">

            <online.yllh.smartinspect.uiwidget.BubbleView
                android:id="@+id/bubbleView"
                android:layout_width="match_parent"
                android:layout_height="72dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/macro"
                android:layout_width="32dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@+id/bubbleView"
                app:layout_constraintEnd_toStartOf="@+id/standard"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintHorizontal_chainStyle="spread_inside"
                app:layout_constraintStart_toStartOf="@+id/bubbleView"
                app:layout_constraintTop_toTopOf="@+id/bubbleView"
                app:layout_constraintVertical_bias="0.7">

                <ImageView
                    android:id="@+id/ic_macro"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center"
                    android:layout_marginBottom="4dp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_macro"
                    app:tint="@drawable/selector_focal_icon_tint" />

                <TextView
                    android:id="@+id/text_macro"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="微距"
                    android:textColor="@drawable/selector_focal_icon_tint"
                    android:textSize="12dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/standard"
                android:layout_width="32dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@+id/bubbleView"
                app:layout_constraintEnd_toStartOf="@+id/longFocal"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/macro"
                app:layout_constraintTop_toTopOf="@+id/bubbleView"
                app:layout_constraintVertical_bias="0.7">

                <ImageView
                    android:id="@+id/ic_standard"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center"
                    android:layout_marginBottom="4dp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_standard"
                    app:tint="@drawable/selector_focal_icon_tint" />

                <TextView
                    android:id="@+id/text_standard"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="标准"
                    android:textColor="@drawable/selector_focal_icon_tint"
                    android:textSize="12dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/longFocal"
                android:layout_width="32dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@+id/bubbleView"
                app:layout_constraintEnd_toStartOf="@+id/wideAngle"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/standard"
                app:layout_constraintTop_toTopOf="@+id/bubbleView"
                app:layout_constraintVertical_bias="0.7">

                <ImageView
                    android:id="@+id/ic_long_focal"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center"
                    android:layout_marginBottom="4dp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_long_focal"
                    app:tint="@drawable/selector_focal_icon_tint" />

                <TextView
                    android:id="@+id/text_long_focal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="长焦"
                    android:textColor="@drawable/selector_focal_icon_tint"
                    android:textSize="12dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/wideAngle"
                android:layout_width="32dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="30dp"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@+id/bubbleView"
                app:layout_constraintEnd_toEndOf="@+id/bubbleView"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/longFocal"
                app:layout_constraintTop_toTopOf="@+id/bubbleView"
                app:layout_constraintVertical_bias="0.7">

                <ImageView
                    android:id="@+id/ic_wide_angle"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center"
                    android:layout_marginBottom="4dp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_wide_angle"
                    app:tint="@drawable/selector_focal_icon_tint" />

                <TextView
                    android:id="@+id/text_wide_angle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="广角"
                    android:textColor="@drawable/selector_focal_icon_tint"
                    android:textSize="12dp" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/countDownModeSelectView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="8dp"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="@+id/cameraPreviewView"
            app:layout_constraintEnd_toEndOf="@+id/cameraPreviewView"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="@+id/cameraPreviewView"
            app:layout_constraintVertical_bias="0.0">

            <online.yllh.smartinspect.uiwidget.BubbleView
                android:id="@+id/countDownBubbleView"
                android:layout_width="match_parent"
                android:layout_height="72dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/countDownOff"
                android:layout_width="32dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@+id/countDownBubbleView"
                app:layout_constraintEnd_toStartOf="@+id/countDown3s"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintHorizontal_chainStyle="spread_inside"
                app:layout_constraintStart_toStartOf="@+id/countDownBubbleView"
                app:layout_constraintTop_toTopOf="@+id/countDownBubbleView"
                app:layout_constraintVertical_bias="0.7">

                <ImageView
                    android:id="@+id/ic_count_down_off"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center"
                    android:layout_marginBottom="4dp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_count_down_off"
                    app:tint="@drawable/selector_focal_icon_tint" />

                <TextView
                    android:id="@+id/text_count_down_off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="关闭"
                    android:textColor="@drawable/selector_focal_icon_tint"
                    android:textSize="12dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/countDown3s"
                android:layout_width="32dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@+id/countDownBubbleView"
                app:layout_constraintEnd_toStartOf="@+id/countDown5s"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/countDownOff"
                app:layout_constraintTop_toTopOf="@+id/countDownBubbleView"
                app:layout_constraintVertical_bias="0.7">

                <ImageView
                    android:id="@+id/ic_count_down_3s"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center"
                    android:layout_marginBottom="4dp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_count_down_3s"
                    app:tint="@drawable/selector_focal_icon_tint" />

                <TextView
                    android:id="@+id/text_count_down_3s"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3s"
                    android:textColor="@drawable/selector_focal_icon_tint"
                    android:textSize="12dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/countDown5s"
                android:layout_width="32dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@+id/countDownBubbleView"
                app:layout_constraintEnd_toStartOf="@+id/countDown10s"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/countDown3s"
                app:layout_constraintTop_toTopOf="@+id/countDownBubbleView"
                app:layout_constraintVertical_bias="0.7">

                <ImageView
                    android:id="@+id/ic_count_down_5s"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center"
                    android:layout_marginBottom="4dp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_count_down_5s"
                    app:tint="@drawable/selector_focal_icon_tint" />

                <TextView
                    android:id="@+id/text_count_down_5s"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="5s"
                    android:textColor="@drawable/selector_focal_icon_tint"
                    android:textSize="12dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/countDown10s"
                android:layout_width="32dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="30dp"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@+id/countDownBubbleView"
                app:layout_constraintEnd_toEndOf="@+id/countDownBubbleView"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/countDown5s"
                app:layout_constraintTop_toTopOf="@+id/countDownBubbleView"
                app:layout_constraintVertical_bias="0.7">

                <ImageView
                    android:id="@+id/ic_count_down_10s"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="center"
                    android:layout_marginBottom="4dp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_count_down_10s"
                    app:tint="@drawable/selector_focal_icon_tint" />

                <TextView
                    android:id="@+id/text_count_down_10s"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10s"
                    android:textColor="@drawable/selector_focal_icon_tint"
                    android:textSize="12dp" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <online.yllh.smartinspect.uiwidget.EditWatermarkPanel
            android:id="@+id/editWatermarkPanel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible" />

        <online.yllh.smartinspect.uiwidget.AddressPanel
            android:id="@+id/addressSelectPanel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1.0" />


        <online.yllh.smartinspect.uiwidget.sticker.StickerPanel
            android:id="@+id/stickerPanel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1.0" />

        <online.yllh.smartinspect.uiwidget.AddressLevelPanel
            android:id="@+id/addressLevelPanel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1.0" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>
