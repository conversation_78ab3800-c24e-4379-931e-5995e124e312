<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/titleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="图片缓存管理"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cacheInfoCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/titleText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="缓存信息"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/cacheSizeText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="缓存大小: 计算中..."
                android:textSize="14sp"
                android:textColor="@color/black"
                android:layout_marginTop="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="缓存包含云相册图片、头像等数据"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="4dp" />

            <Button
                android:id="@+id/refreshCacheInfo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="刷新"
                android:layout_marginTop="8dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cacheActionsCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/cacheInfoCard"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="缓存操作"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <Button
                android:id="@+id/clearMemoryCache"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="清除内存缓存"
                android:layout_marginTop="12dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="清除当前在内存中的图片缓存，释放内存空间"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="8dp" />

            <Button
                android:id="@+id/clearDiskCache"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="清除磁盘缓存"
                android:layout_marginTop="8dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="清除存储在设备上的图片缓存文件，释放存储空间"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="8dp" />

            <Button
                android:id="@+id/clearAllCache"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="清除所有缓存"
                android:layout_marginTop="8dp"
                android:backgroundTint="@color/design_default_color_error"
                style="@style/Widget.Material3.Button" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="清除所有图片缓存，下次加载图片时需要重新下载"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/cacheActionsCard"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="缓存优化说明"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="• 图片缓存可以显著提升云相册的加载速度\n• 内存缓存用于快速显示最近查看的图片\n• 磁盘缓存避免重复下载相同的图片\n• 建议定期清理缓存以释放存储空间"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:lineSpacingExtra="4dp"
                android:layout_marginTop="8dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
