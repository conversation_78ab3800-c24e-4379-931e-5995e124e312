<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".view.gallery.local.PhotoFragment">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/photoRecyclerView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:layout_marginTop="16dp"
        app:layout_constraintBottom_toTopOf="@+id/bottomActionBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/bottomActionBar"
        android:layout_width="match_parent"
        android:layout_height="86dp"
        android:elevation="8dp"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/toggleSelectionButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            app:style="grey"
            app:text="多选" />

        <TextView
            android:id="@+id/selectionCountText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:layout_marginTop="8dp"
            android:text="已选择 0/0"
            android:textSize="14sp"
            android:visibility="gone" />

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/selectAllButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            app:style="grey"
            app:text="全选"
            android:visibility="gone" />

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/deleteButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            app:style="red"
            app:text="删除"
            android:visibility="gone" />

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/uploadButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            app:style="green"
            app:text="上传"
            android:visibility="gone" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/emptyStateLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:src="@drawable/placeholder"
            android:alpha="0.5" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="暂无未同步照片"
            android:textSize="16sp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
