<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="293dp"
        android:layout_height="wrap_content"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingTop="17dp"
        android:paddingBottom="19dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="项目筛选"
            android:textColor="#FF333333"
            android:textSize="18dp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:orientation="vertical"
            android:clipChildren="false"
            android:gravity="center">

            <EditText
                android:id="@+id/name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="请输入项目名称"
                style="@style/MyTextInputLayoutStyle"
                android:background="@drawable/input_item_write_bg"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:inputType="text" />

            <EditText
                android:id="@+id/owner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="请输入项目发起人"
                style="@style/MyTextInputLayoutStyle"
                android:background="@drawable/input_item_write_bg"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:inputType="text" />
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:background="@drawable/input_item_write_bg"
                android:layout_marginBottom="16dp"
                android:clipChildren="false">
                <Spinner
                    android:id="@+id/statusSelect"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </FrameLayout>
            <LinearLayout
                android:id="@+id/startTime"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/input_item_write_bg"
                android:paddingEnd="10dp"
                android:paddingStart="10dp"
                android:gravity="center">
                <TextView
                    android:id="@+id/startTimeText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="选择项目开始时间"
                    android:textSize="12dp"
                    android:textColor="#FF333333"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0"
                    android:src="@drawable/ic_calendar"/>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/endTime"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:background="@drawable/input_item_write_bg"
                android:paddingEnd="10dp"
                android:paddingStart="10dp"
                android:gravity="center">
                <TextView
                    android:id="@+id/endTimeText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="选择项目结束时间"
                    android:textSize="12dp"
                    android:textColor="#FF333333"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0"
                    android:src="@drawable/ic_calendar"/>
            </LinearLayout>
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/cancelButton"
                android:layout_width="116dp"
                android:layout_height="40dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/okButton"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="spread_inside"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:style="grey"
                app:text="取消" />

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/okButton"
                android:layout_width="116dp"
                android:layout_height="40dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/cancelButton"
                app:layout_constraintTop_toTopOf="parent"
                app:style="green"
                app:text="确定" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>