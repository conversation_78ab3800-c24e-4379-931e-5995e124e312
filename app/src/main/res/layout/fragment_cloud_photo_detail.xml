<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.gallery.cloud.CloudPhotoDetailFragment">

    <!-- TODO: Update blank fragment layout -->
    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/photoViewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/buttonContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_bar" />

    <TextView
        android:id="@+id/photoIndicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@android:color/black"
        android:backgroundTint="#80000000"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6dp"
        android:text="1/1"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/photoViewPager" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/buttonContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="36dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/showDetail"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginEnd="2dp"
                android:paddingHorizontal="2dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/addMessage"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:textSize="12dp"
                app:text="查看详情"/>

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/addMessage"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:paddingHorizontal="2dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/movePhoto"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/showDetail"
                app:layout_constraintTop_toTopOf="parent"
                app:textSize="12dp"
                app:text="自定义信息"/>

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/movePhoto"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:paddingHorizontal="2dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/deletePhoto"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/addMessage"
                app:layout_constraintTop_toTopOf="parent"
                app:style="blue"
                app:textSize="12dp"
                app:text="移动文件"/>

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/deletePhoto"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginStart="2dp"
                android:paddingHorizontal="2dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/movePhoto"
                app:layout_constraintTop_toTopOf="parent"
                app:style="red"
                app:textSize="12dp"
                app:text="删除"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>