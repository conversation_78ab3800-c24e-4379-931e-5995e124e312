<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.gallery.GalleryFragment">

    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/appTitleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="相册" />

    <LinearLayout
        android:id="@+id/linearLayout2"
        android:layout_width="wrap_content"
        android:layout_height="36dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/switch_button_bg"
        android:orientation="horizontal"
        android:padding="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appTitleBar">

        <TextView
            android:id="@+id/localAlbum"
            android:layout_width="76dp"
            android:layout_height="32dp"
            android:background="@drawable/switch_select_bg"
            android:gravity="center"
            android:text="本地相册"
            android:textColor="@drawable/switch_select_text_color"
            android:textSize="14dp" />

        <View
            android:layout_width="10dp"
            android:layout_height="0dp" />

        <TextView
            android:id="@+id/cloudAlbum"
            android:layout_width="76dp"
            android:layout_height="32dp"
            android:background="@drawable/switch_select_bg"
            android:gravity="center"
            android:text="云相册"
            android:textColor="@drawable/switch_select_text_color"
            android:textSize="14dp" />

        <View
            android:layout_width="10dp"
            android:layout_height="0dp" />

        <TextView
            android:id="@+id/unUploadAlbum"
            android:layout_width="76dp"
            android:layout_height="32dp"
            android:background="@drawable/switch_select_bg"
            android:gravity="center"
            android:text="未同步"
            android:textColor="@drawable/switch_select_text_color"
            android:textSize="14dp" />

        <View
            android:layout_width="10dp"
            android:layout_height="0dp" />

        <TextView
            android:id="@+id/recycleBin"
            android:layout_width="76dp"
            android:layout_height="32dp"
            android:background="@drawable/switch_select_bg"
            android:gravity="center"
            android:text="回收站"
            android:textColor="@drawable/switch_select_text_color"
            android:textSize="14dp" />
    </LinearLayout>

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragmentContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout2" />
</androidx.constraintlayout.widget.ConstraintLayout>
