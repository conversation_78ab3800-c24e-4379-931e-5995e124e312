<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.gallery.unupload.UploadPhotoFragment">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/photoViewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="0dp"
        app:layout_constraintBottom_toTopOf="@+id/buttonContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/photoIndicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@android:color/black"
        android:backgroundTint="#80000000"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6dp"
        android:text="1/1"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/photoViewPager" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/buttonContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="36dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/cancelButton"
            android:layout_width="0dp"
            android:layout_height="40dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/deleteButton"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:style="grey"
            app:text="取消" />

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/deleteButton"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_margin="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/okButton"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/cancelButton"
            app:layout_constraintTop_toTopOf="parent"
            app:style="red"
            app:text="删除" />

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/okButton"
            android:layout_width="0dp"
            android:layout_height="40dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/deleteButton"
            app:layout_constraintTop_toTopOf="parent"
            app:text="上传到云端" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
