<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground">

    <!-- 照片数量显示 -->
    <TextView
        android:id="@+id/photoCountText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:text="回收站 (0)"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 照片网格 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/photoRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:paddingHorizontal="6dp"
        app:layout_constraintBottom_toTopOf="@+id/bottomActionBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/photoCountText" />

    <!-- 空状态视图 -->
    <LinearLayout
        android:id="@+id/emptyStateLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:alpha="0.5"
            android:src="@drawable/ic_delete"
            app:tint="@color/gray" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="回收站为空"
            android:textColor="@color/gray"
            android:textSize="16sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="删除的照片会在此保留30天"
            android:textColor="@color/gray"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- 底部操作栏 -->
    <LinearLayout
        android:id="@+id/bottomActionBar"
        android:layout_width="match_parent"
        android:layout_height="86dp"
        android:elevation="8dp"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/toggleSelectionButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            app:style="grey"
            app:text="选择" />

        <TextView
            android:id="@+id/selectionCountText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:layout_weight="1"
            android:text="已选择 0/0"
            android:textSize="14sp"
            android:visibility="gone" />

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/selectAllButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            android:layout_marginStart="8dp"
            app:style="grey"
            app:text="全选"
            android:visibility="gone" />

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/clearAllButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginStart="8dp"
            app:style="red"
            app:text="清空" />

    </LinearLayout>

    <!-- 选择模式工具栏 -->
    <LinearLayout
        android:id="@+id/selectionToolbar"
        android:layout_width="match_parent"
        android:layout_height="86dp"
        android:background="@color/blackBackground"
        android:elevation="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="8dp"
        android:layout_marginBottom="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/selectedCountText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="已选择 0 张"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/restoreButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            app:style="grey"
            app:text="恢复" />

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/deleteButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            app:style="red"
            app:text="彻底删除" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
