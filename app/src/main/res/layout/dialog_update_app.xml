<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="32dp"
    android:background="@drawable/dialog_bg"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="更新提示"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- Logo和版本说明区域 -->
    <LinearLayout
        android:id="@+id/layout_version_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Logo -->
        <ImageView
            android:layout_width="170dp"
            android:layout_height="170dp"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:src="@mipmap/ic_launcher" />

        <!-- 版本说明 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="版本说明："
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <!-- 更新内容列表 -->
        <LinearLayout
            android:id="@+id/layout_update_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp">

        </LinearLayout>

    </LinearLayout>

    <!-- 下载进度区域 -->
    <LinearLayout
        android:id="@+id/layout_download_progress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="24dp"
        android:visibility="gone">

        <!-- 进度文本和状态 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/tv_progress_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="正在下载，进度40%"
                android:textColor="@color/text33"
                android:textSize="14sp"
                android:gravity="center" />

            <!-- 失败状态图标 -->
            <ImageView
                android:id="@+id/iv_error_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_error_red"
                android:visibility="gone"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- 进度条 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:max="100"
            android:progress="40"
            android:progressTint="#FFD02E"
            android:progressBackgroundTint="#E0E0E0" />

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:id="@+id/layout_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:weightSum="2">

        <!-- 稍后下载按钮 -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_later"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:visibility="gone"
            android:layout_marginEnd="8dp"
            android:background="@drawable/btn_bg_grey"
            android:text="稍后下载"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <!-- 下载/重新下载按钮 -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_download"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:background="@drawable/btn_bg_yellow"
            android:text="下载"
            android:textColor="@color/white"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 下载中的按钮区域 -->
    <LinearLayout
        android:id="@+id/layout_downloading_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:visibility="gone">

        <!-- 完成按钮 -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_complete"
            android:layout_width="120dp"
            android:layout_height="40dp"
            android:background="@drawable/btn_bg_grey"
            android:text="完成"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:enabled="false" />

    </LinearLayout>

</LinearLayout>
