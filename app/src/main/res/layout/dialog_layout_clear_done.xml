<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="293dp"
        android:layout_height="wrap_content"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingTop="17dp"
        android:paddingBottom="19dp"
        android:orientation="vertical"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_sccuess"/>
        <View
            android:layout_width="10dp"
            android:layout_height="20dp" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="清理完成！"
            android:textColor="#FF333333"
            android:textSize="18dp"
            android:textStyle="bold" />
        <View
            android:layout_width="10dp"
            android:layout_height="10dp" />
        <TextView
            android:id="@+id/clear_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="共释放了372.274MB空间"
            android:textColor="#FF333333"
            android:textSize="14dp" />
        <View
            android:layout_width="10dp"
            android:layout_height="20dp" />
        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/okButton"
            android:layout_width="116dp"
            android:layout_height="40dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/cancelButton"
            app:layout_constraintTop_toTopOf="parent"
            app:style="green"
            app:text="确定" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>