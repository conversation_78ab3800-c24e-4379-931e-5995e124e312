<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.gallery.cloud.CloudPhotoExifDetailFragment">

    <!-- TODO: Update blank fragment layout -->
    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/appTitleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appTitleBar"
        app:layout_constraintVertical_bias="0.0">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">
            <ImageView
                android:id="@+id/mainImage"
                android:layout_width="wrap_content"
                android:layout_height="169dp"/>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:padding="16dp"
                android:background="@drawable/photo_detail_card_bg"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/voiceInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/textView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="语音信息"
                        android:textColor="@color/white"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/voiceExpand"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/voiceExpand"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/ic_expand"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toEndOf="@+id/textView"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/voiceList"
                    android:layout_marginTop="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:padding="16dp"
                android:background="@drawable/photo_detail_card_bg"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/msgInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/textView1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="自定义信息"
                        android:textColor="@color/white"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/msgExpand"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/msgExpand"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/ic_expand"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toEndOf="@+id/textView1"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/msgList"
                    android:layout_marginTop="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:padding="16dp"
                android:background="@drawable/photo_detail_card_bg"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/baseInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/textView2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="基本信息"
                        android:textColor="@color/white"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/baseInfoExpand"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/baseInfoExpand"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/ic_expand"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toEndOf="@+id/textView2"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <View
                    android:layout_width="0dp"
                    android:layout_height="20dp"/>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <online.yllh.smartinspect.uiwidget.ExitItem
                        android:id="@+id/shutTime"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:name="拍摄时间:"
                        app:desc=""/>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="16dp"/>
                    <online.yllh.smartinspect.uiwidget.ExitItem
                        android:id="@+id/shutAddress"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:name="拍摄地点:"
                        app:desc=""/>
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:padding="16dp"
                android:background="@drawable/photo_detail_card_bg"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/exifInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/textView3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="EXIF信息"
                        android:textColor="@color/white"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/exifInfoExpand"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/exifInfoExpand"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/ic_expand"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toEndOf="@+id/textView3"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <View
                    android:layout_width="0dp"
                    android:layout_height="20dp"/>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <online.yllh.smartinspect.uiwidget.ExitItem
                        android:id="@+id/exifDevice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:name="拍摄设备:"
                        app:desc=""/>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="16dp"/>
                    <online.yllh.smartinspect.uiwidget.ExitItem
                        android:id="@+id/exifDeviceModel"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:name="设备型号:"
                        app:desc=""/>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="16dp"/>
                    <online.yllh.smartinspect.uiwidget.ExitItem
                        android:id="@+id/exifSensitivity"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:name="感光度:"
                        app:desc=""/>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="16dp"/>
                    <online.yllh.smartinspect.uiwidget.ExitItem
                        android:id="@+id/exiffocalLength"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:name="焦距:"
                        app:desc=""/>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="16dp"/>
                    <online.yllh.smartinspect.uiwidget.ExitItem
                        android:id="@+id/exifExposure"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:name="曝光补偿:"
                        app:desc=""/>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="16dp"/>
                    <online.yllh.smartinspect.uiwidget.ExitItem
                        android:id="@+id/exifAperture"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:name="光圈大小:"
                        app:desc=""/>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="16dp"/>
                    <online.yllh.smartinspect.uiwidget.ExitItem
                        android:id="@+id/exitShutterTime"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:name="快门时间:"
                        app:desc=""/>
                </LinearLayout>
            </LinearLayout>
            <View
                android:layout_width="0dp"
                android:layout_height="30dp"/>
            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/addMessage"
                android:layout_width="170dp"
                android:layout_height="40dp"
                android:layout_marginEnd="16dp"
                android:layout_gravity="end"
                app:text="添加自定义信息"/>
            <View
                android:layout_width="0dp"
                android:layout_height="96dp"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>