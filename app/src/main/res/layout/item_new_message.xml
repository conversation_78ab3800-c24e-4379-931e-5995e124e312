<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="vertical"
    android:layout_marginTop="5dp"
    android:layout_marginBottom="5dp">
    <TextView
        android:id="@+id/dateTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:layout_marginTop="8dp"
        android:text="12"
        android:textSize="12dp"
        android:textColor="@color/white" />
    <LinearLayout
        android:id="@+id/messageItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/message_item_bg_green"
        android:gravity="center"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/textItem"
            android:layout_gravity="center"
            android:padding="16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:layout_weight="1"
            android:textSize="12dp"
            android:text="12434232132"/>
        <LinearLayout
            android:id="@+id/voiceItem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:layout_weight="1"
            android:orientation="horizontal">
            <ImageView
                android:id="@+id/play"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_voice_play"/>
            <ImageView
                android:id="@+id/stop"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_pause"/>
            <ProgressBar
                android:id="@+id/progress"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:indeterminate="true"/>
            <View
                android:layout_width="10dp"
                android:layout_height="0dp"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/voice_item_bg">

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:src="@drawable/ic_voice_wave"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <View
                android:layout_width="8dp"
                android:layout_height="0dp"/>
            <TextView
                android:id="@+id/second"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="s"
                android:textColor="@color/white"
                android:textSize="12dp"/>
        </LinearLayout>
        <ImageView
            android:id="@+id/lockStatus"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_unlock"/>
    </LinearLayout>
</LinearLayout>