<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.camera.CameraFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/parentConstraintLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextureView
            android:id="@+id/previewView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="56dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout"
            android:layout_width="match_parent"
            android:layout_height="213dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="158dp"
                android:layout_marginTop="10dp"
                android:paddingStart="15dp"
                android:paddingEnd="15dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0">

                <LinearLayout
                    android:id="@+id/linearLayout3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/okButton"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="1.0">

                    <LinearLayout
                        android:id="@+id/backContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="26dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <online.yllh.smartinspect.uiwidget.HomeRoundIcon
                            android:id="@+id/icBack"
                            android:layout_width="44dp"
                            android:layout_height="44dp"
                            app:imageHeight="30dp"
                            app:imageWidth="30dp"
                            app:src="@drawable/ic_back" />

                        <TextView
                            android:id="@+id/backTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="10dp"
                            android:text="返回"
                            android:textColor="@android:color/white"
                            android:textSize="12dp" />
                    </LinearLayout>

                    <View
                        android:layout_width="20dp"
                        android:layout_height="0dp" />

                    <LinearLayout
                        android:id="@+id/locationContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="26dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <online.yllh.smartinspect.uiwidget.HomeRoundIcon
                            android:id="@+id/location"
                            android:layout_width="44dp"
                            android:layout_height="44dp"
                            app:imageHeight="30dp"
                            app:imageWidth="30dp"
                            app:src="@drawable/ic_location" />

                        <TextView
                            android:id="@+id/locationTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="10dp"
                            android:text="地点"
                            android:textColor="@android:color/white"
                            android:textSize="12sp" />
                    </LinearLayout>
                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/okButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"

                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <online.yllh.smartinspect.uiwidget.SnapshotButton
                        android:layout_width="75dp"
                        android:layout_height="75dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="47dp"
                        android:layout_height="47dp"
                        android:src="@drawable/ic_done"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:id="@+id/watermarkContainer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toEndOf="@+id/okButton"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="1.0">

                    <LinearLayout
                        android:id="@+id/watermarkCon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="26dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <online.yllh.smartinspect.uiwidget.HomeRoundIcon
                            android:id="@+id/watermark"
                            android:layout_width="44dp"
                            android:layout_height="44dp"
                            app:imageHeight="30dp"
                            app:imageWidth="30dp"
                            app:src="@drawable/ic_watermark" />

                        <TextView
                            android:id="@+id/watermarkTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="10dp"
                            android:text="水印"
                            android:textColor="@android:color/white"
                            android:textSize="12dp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <online.yllh.smartinspect.uiwidget.sticker.StickerPanel
            android:id="@+id/stickerPanel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1.0" />

        <online.yllh.smartinspect.uiwidget.AddressPanel
            android:id="@+id/addressSelectPanel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1.0" />

        <online.yllh.smartinspect.uiwidget.AddressLevelPanel
            android:id="@+id/addressLevelPanel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1.0" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
