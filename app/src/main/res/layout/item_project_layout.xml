<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    android:background="@drawable/item_project_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/coverImage"
            android:layout_width="82dp"
            android:layout_height="110dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/startProject"
            android:layout_width="90dp"
            android:layout_height="40dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:text="开启任务" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="20dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/constraintLayout2"
        app:layout_constraintVertical_bias="0.0">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">
            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:layout_marginEnd="6dp"
                android:text="项目名称:"
                android:textColor="#80FFFFFF"
                android:textSize="12dp"/>
            <TextView
                android:id="@+id/projectName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="6dp"
                android:text="项目名称"
                android:textColor="#FFFFFFFF"
                android:textSize="12dp"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">
            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:layout_marginEnd="6dp"
                android:text="项目要求:"
                android:textColor="#80FFFFFF"
                android:textSize="12dp"/>
            <TextView
                android:id="@+id/projectRequire"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="项目要求"
                android:textColor="#FFFFFFFF"
                android:textSize="12dp"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">
            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:layout_marginEnd="6dp"
                android:text="项目发起人:"
                android:textColor="#80FFFFFF"
                android:textSize="12dp"/>
            <TextView
                android:id="@+id/projectOnwer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="项目发起人"
                android:textColor="#FFFFFFFF"
                android:textSize="12dp"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">
            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:layout_marginEnd="6dp"
                android:text="项目开始时间:"
                android:textColor="#80FFFFFF"
                android:textSize="12dp"/>
            <TextView
                android:id="@+id/projectStartTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="项目开始时间"
                android:textColor="#FFFFFFFF"
                android:textSize="12dp"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">
            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:layout_marginEnd="6dp"
                android:text="项目结束时间:"
                android:textColor="#80FFFFFF"
                android:textSize="12dp"/>
            <TextView
                android:id="@+id/projectEndTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="项目结束时间"
                android:textColor="#FFFFFFFF"
                android:textSize="12dp"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">
            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:layout_marginEnd="6dp"
                android:text="项目状态:"
                android:textColor="#80FFFFFF"
                android:textSize="12dp"/>
            <TextView
                android:id="@+id/projectStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="项目状态"
                android:textColor="#FFFFFFFF"
                android:textSize="12dp"/>
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>