<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/constraintLayout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bubble"
    android:layout_gravity="center_horizontal"
    android:paddingStart="20dp"
    android:paddingEnd="20dp"
    android:paddingTop="24dp"
    android:paddingBottom="20dp">

    <LinearLayout
        android:id="@+id/edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_item_edit" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="编辑"
            android:textColor="@color/white"
            android:textSize="14dp" />
    </LinearLayout>

    <View
        android:id="@+id/editPadding"
        android:layout_width="52dp"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/edit"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/editPadding"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_item_delete" />

        <TextView
            android:id="@+id/deleteText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="删除"
            android:textColor="@color/white"
            android:textSize="14dp" />
    </LinearLayout>

    <View
        android:id="@+id/deletePadding"
        android:layout_width="52dp"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/delete"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/copy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/deletePadding"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_item_copy" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="复制"
            android:textColor="@color/white"
            android:textSize="14dp" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>