<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0.3"
        android:background="@color/black" />

    <LinearLayout
        android:id="@+id/content"
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        android:background="@drawable/dialog_bg"
        android:orientation="vertical"
        android:paddingHorizontal="12dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="编辑水印"
                android:textColor="#FF333333"
                android:textSize="18dp"
                android:textStyle="bold" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:padding="6dp"
                android:src="@drawable/ic_close" />
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/groupTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <Switch
                android:id="@+id/switchTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginVertical="6dp"
                android:checked="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:text="现场记录"
                android:textColor="@color/black"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnMoreTitle"
                app:layout_constraintStart_toEndOf="@id/switchTitle"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btnMoreTitle"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:padding="6dp"
                android:rotation="180"
                android:src="@drawable/ic_page_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvTitle"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/black" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="8dp"
                android:background="#DDDDDD"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvTitle" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/groupTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <Switch
                android:id="@+id/switchTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginVertical="6dp"
                android:checked="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tvTime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:text="拍摄时间：2025-07-04 21:08"
                android:textColor="@color/black"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnMoreTime"
                app:layout_constraintStart_toEndOf="@id/switchTime"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btnMoreTime"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:padding="6dp"
                android:rotation="180"
                android:src="@drawable/ic_page_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvTime"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/black" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="8dp"
                android:background="#DDDDDD"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvTime" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/groupWeather"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <Switch
                android:id="@+id/switchWeather"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginVertical="6dp"
                android:checked="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tvWeather"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:text="天气：晴 28℃"
                android:textColor="@color/black"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnMoreWeather"
                app:layout_constraintStart_toEndOf="@id/switchWeather"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btnMoreWeather"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:padding="6dp"
                android:rotation="180"
                android:src="@drawable/ic_page_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvWeather"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/black" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="8dp"
                android:background="#DDDDDD"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvWeather" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <Switch
                android:id="@+id/switchLocation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginVertical="6dp"
                android:checked="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tvLocation"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="4dp"
                android:layout_weight="1"
                android:text="定位：104.123°N 30.456°E"
                android:textColor="@color/black"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/switchLocation"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="8dp"
                android:background="#DDDDDD"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvLocation" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <Switch
                android:id="@+id/switchAltitude"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginVertical="6dp"
                android:checked="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tvAltitude"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="4dp"
                android:layout_weight="1"
                android:text="海拔：500米"
                android:textColor="@color/black"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/switchAltitude"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="8dp"
                android:background="#DDDDDD"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvAltitude" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <Switch
                android:id="@+id/switchAzimuth"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginVertical="6dp"
                android:checked="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tvAzimuth"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="4dp"
                android:layout_weight="1"
                android:fontFeatureSettings="'tnum'"
                android:text="角度：300°"
                android:textColor="@color/black"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/switchAzimuth"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="8dp"
                android:background="#DDDDDD"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvAzimuth" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/groupAddress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <Switch
                android:id="@+id/switchAddress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginVertical="6dp"
                android:checked="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tvAddress"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:text="地点：华阳街道·鸿阁一号"
                android:textColor="@color/black"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnMoreAddress"
                app:layout_constraintStart_toEndOf="@id/switchAddress"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btnMoreAddress"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:padding="6dp"
                android:rotation="180"
                android:src="@drawable/ic_page_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvAddress"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/black" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:visibility="gone">

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/cancelButton"
                android:layout_width="116dp"
                android:layout_height="40dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/okButton"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="spread_inside"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:style="grey"
                app:text="取消" />

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/okButton"
                android:layout_width="116dp"
                android:layout_height="40dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/cancelButton"
                app:layout_constraintTop_toTopOf="parent"
                app:style="green"
                app:text="确定" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
