<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/mask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/content"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="390dp"
        android:background="@color/blackBackground"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mask">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

            <ImageView
                android:id="@+id/emptySticker"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_empty_sticker" />
            <!-- TODO: Update blank fragment layout -->
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayout"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@color/blackBackground"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tabIndicatorColor="@color/white"
                app:tabMode="scrollable"
                app:tabSelectedTextColor="@color/white"
                app:tabTextColor="#7FFFFFFF">

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="最近" />

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="工作" />
            </com.google.android.material.tabs.TabLayout>

            <ImageView
                android:id="@+id/closePanel"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_close_panel" />
        </LinearLayout>

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
