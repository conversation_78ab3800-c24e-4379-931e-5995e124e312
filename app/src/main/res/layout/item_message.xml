<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="vertical"
    android:layout_marginTop="5dp"
    android:layout_marginBottom="5dp">
    <LinearLayout
        android:id="@+id/voiceItem"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/text1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textColor="#7FFFFFFF"
            android:text="语音信息:"
            android:textSize="12dp" />
        <View
            android:layout_width="10dp"
            android:layout_height="0dp"/>
        <ImageView
            android:id="@+id/play"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_voice_play"/>
        <ImageView
            android:id="@+id/stop"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_pause"/>
        <ProgressBar
            android:id="@+id/progress"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:indeterminate="true"/>
        <View
            android:layout_width="10dp"
            android:layout_height="0dp"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/voice_item_bg">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_voice_wave"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <View
            android:layout_width="8dp"
            android:layout_height="0dp"/>
        <TextView
            android:id="@+id/second"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="s"
            android:textColor="@color/white"
            android:textSize="12dp"/>
    </LinearLayout>
    <online.yllh.smartinspect.uiwidget.ExitItem
        android:id="@+id/messageItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:name="自定义信息:"/>
    <online.yllh.smartinspect.uiwidget.ExitItem
        android:id="@+id/messageItemDate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:name="创建时间:"/>
</LinearLayout>
