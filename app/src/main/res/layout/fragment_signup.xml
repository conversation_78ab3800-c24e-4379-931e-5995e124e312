<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.login.LoginFragment">
    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <LinearLayout
        android:id="@+id/logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="88dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="67dp"
            android:layout_height="67dp"
            android:layout_marginBottom="6dp"
            android:src="@mipmap/ic_launcher" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="古树通相机"
            android:textColor="@color/white"
            android:textSize="18dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="241dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="70dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/logo">

        <LinearLayout
            android:layout_width="241dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">
            <EditText
                android:id="@+id/phoneNumEditText"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_phone_num"
                android:drawablePadding="8dp"
                android:hint="请输入手机号"
                style="@style/MyTextInputLayoutStyle"
                android:background="@drawable/input_item_bg"
                android:textColorHint="#99FFFFFF"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:inputType="phone"
                android:textColor="@color/white" />
            <View
                android:layout_width="10dp"
                android:layout_height="0dp" />
            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/sendSmsCode"
                android:layout_width="71dp"
                android:layout_height="wrap_content"
                app:text="发送"/>
        </LinearLayout>
        <View
            android:layout_width="wrap_content"
            android:layout_height="10dp" />
        <EditText
            android:id="@+id/verifyCodeEditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_verify_code"
            android:drawablePadding="8dp"
            android:hint="请输入验证码"
            style="@style/MyTextInputLayoutStyle"
            android:background="@drawable/input_item_bg"
            android:textColorHint="#99FFFFFF"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:inputType="text"
            android:textColor="@color/white" />
        <View
            android:layout_width="wrap_content"
            android:layout_height="10dp" />
        <EditText
            android:id="@+id/passEditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_phone_num"
            android:drawablePadding="8dp"
            style="@style/MyTextInputLayoutStyle"
            android:background="@drawable/input_item_bg"
            android:textColorHint="#99FFFFFF"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:hint="请输入密码"
            android:inputType="textPassword"
            android:textColor="@color/white" />
        <View
            android:layout_width="wrap_content"
            android:layout_height="10dp" />
        <EditText
            android:id="@+id/prepeatpassEditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_phone_num"
            android:drawablePadding="8dp"
            style="@style/MyTextInputLayoutStyle"
            android:background="@drawable/input_item_bg"
            android:hint="请再次输入密码"
            android:textColorHint="#99FFFFFF"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:inputType="textPassword"
            android:textColor="@color/white" />

        <View
            android:layout_width="wrap_content"
            android:layout_height="28dp"/>
        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/okButton"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            app:style="green"
            app:text="确定"/>
        <View
            android:layout_width="wrap_content"
            android:layout_height="10dp"/>
        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/cancelButton"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            app:style="grey"
            app:text="取消"/>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>