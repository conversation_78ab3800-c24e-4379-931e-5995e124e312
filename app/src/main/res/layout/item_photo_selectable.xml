<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="2dp">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp">

        <ImageView
            android:id="@+id/photoImageView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            tools:src="@drawable/placeholder" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#22000000"
            android:visibility="gone" />

        <CheckBox
            android:id="@+id/photoCheckBox"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_gravity="top|end"
            android:layout_margin="4dp"
            android:background="@drawable/checkbox_background"
            android:button="@null"
            android:checked="false"
            android:clickable="false"
            android:focusable="false"
            android:gravity="center"
            android:visibility="visible" />

    </androidx.cardview.widget.CardView>

</FrameLayout>
