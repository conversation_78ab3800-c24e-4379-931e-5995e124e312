<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="121dp"
    android:layout_height="160dp"
    android:background="@color/blackBackground">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/stickerCons"
        android:layout_width="117dp"
        android:layout_height="156dp"
        android:background="@drawable/sticker_item_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:id="@+id/stickerImage"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:contentDescription="@string/app_name"
            app:layout_constraintBottom_toTopOf="@+id/stickerName"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:src="@drawable/watermark"
            android:scaleType="fitXY"
            android:adjustViewBounds="true"
            />
        <TextView
            android:id="@+id/stickerName"
            android:layout_width="match_parent"
            android:layout_height="28dp"
            android:layout_marginBottom="0dp"
            android:background="@drawable/sticker_text_item_bg"
            android:gravity="center"
            android:text="贴纸"
            android:textColor="@color/white"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <View
        android:id="@+id/select"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/sticker_item_slect_bg"/>
</androidx.constraintlayout.widget.ConstraintLayout>