<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.setting.CacheFragment">

    <!-- TODO: Update blank fragment layout -->
    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/appTitleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="缓存管理" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appTitleBar"
        app:layout_constraintVertical_bias="0.0">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:layout_margin="16dp"
            android:background="@drawable/setting_item_bg">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:text="当前缓存"
                android:textColor="@color/white"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:gravity="center"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/cacheSize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0MB"
                    android:textColor="@color/white"
                    android:textSize="16dp" />

                <ProgressBar
                    android:id="@+id/progress"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:indeterminate="true"/>
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <online.yllh.smartinspect.uiwidget.GradientButton
            android:id="@+id/clearCache"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_margin="16dp"
            app:text="清理缓存"/>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>