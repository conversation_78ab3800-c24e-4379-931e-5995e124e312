<!-- res/layout/item_node.xml -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="36dp"
    android:gravity="center"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tvName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="@color/black"
        android:textSize="14dp"
        tools:text="1111111111111111111111111111111111111111111111111111111111" />

    <RadioButton
        android:id="@+id/ivCheck"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:src="@drawable/ic_check" />
</LinearLayout>
