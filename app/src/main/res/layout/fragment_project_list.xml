<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".view.project.ProjectListFragment"
    android:background="#FF242424">

    <!-- TODO: Update blank fragment layout -->
    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/hello_blank_fragment"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="项目列表" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/operate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_weight="1"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="23dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBar">

        <TextView
            android:id="@+id/currentProject"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="122"
            android:textColor="@color/white"
            android:background="@drawable/text_bg_grey_round"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/filterButton"
                android:layout_width="90dp"
                android:layout_height="40dp"
                app:text="筛选" />
            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/cleanFilter"
                android:layout_marginTop="8dp"
                android:layout_width="90dp"
                android:layout_height="40dp"
                app:text="清除筛选"
                app:style="grey"/>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/empty"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#FF242424"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/operate">

        <LinearLayout
            android:id="@+id/linearLayout6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.35000002">

            <ImageView
                android:layout_width="180dp"
                android:layout_height="180dp"
                android:layout_marginBottom="10dp"
                android:src="@drawable/ic_empty" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="当前没有结果"
                android:textColor="#9FFFFFFF"
                android:textSize="14dp" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/projectList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="18dp"
        android:paddingBottom="65dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/operate" />
</androidx.constraintlayout.widget.ConstraintLayout>