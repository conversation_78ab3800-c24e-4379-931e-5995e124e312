<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/mask"
        android:layout_width="match_parent"
        android:layout_height="154dp"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="154dp"
        android:background="#FFFFFFFF"
        android:orientation="vertical"
        android:paddingTop="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#FF333333"
            android:gravity="center"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

            <TextView
                android:id="@+id/area"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="成都市"
                android:textColor="#FFFFFFFF"
                android:textSize="24dp" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/levelSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:thumbTint="@color/white"
                app:trackTint="@drawable/switch_track_color_checked" />

            <ImageView
                android:id="@+id/closeAddressPanel"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_close_panel" />
        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:background="#FFFFFFFF"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:text="从下列勾选地址显示级别, 可设置省/市/"
            android:textColor="#FF333333"
            android:textSize="14dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/addressList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
