<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blackBackground"
    tools:context=".view.gallery.local.PhotoFragment">

    <online.yllh.smartinspect.uiwidget.AppTitleBar
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <!-- TODO: Update blank fragment layout -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBar"
        android:orientation="vertical">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp">

            <ImageView
                android:id="@+id/importPhoto"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_gallery_add"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <online.yllh.smartinspect.uiwidget.GradientButton
                android:id="@+id/info"
                android:layout_width="90dp"
                android:layout_height="40dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:text="详情信息" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/photoRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
                <FrameLayout
                    android:id="@+id/addGallery"
                    android:layout_width="110dp"
                    android:layout_height="110dp"
                    android:layout_marginTop="20dp"
                    android:background="@drawable/btn_bg_green">
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_gravity="center">
                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_add_gallery"/>
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:text="添加相册"
                            android:textColor="@color/black"
                            android:textSize="16dp"
                            android:textStyle="bold"/>
                    </LinearLayout>
                </FrameLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>