## $Id: Makefile.am,v 1.28 2013/06/12 09:16:29 rbrito Exp $

AUTOMAKE_OPTIONS = foreign

DEFS = @DEFS@ @CONFIG_DEFS@

ECHO ?= echo

nasm_sources = \
	choose_table.nas \
	cpu_feat.nas \
	fft3dn.nas \
	fftsse.nas

if HAVE_NASM
noinst_LTLIBRARIES = liblameasmroutines.la
liblameasmroutines_la_SOURCES = $(nasm_sources)
liblameasmroutines_la_DEPENDENCIES = $(nasm_sources:.nas.lo)
am_liblameasmroutines_la_OBJECTS = \
	choose_table$U.lo \
	cpu_feat$U.lo \
	fft3dn$U.lo \
	fftsse$U.lo
endif

noinst_HEADERS = nasm.h

INCLUDES = @INCLUDES@ -I$(top_srcdir)/libmp3lame/@CPUTYPE@

SUFFIXES = .nas .lo

EXTRA_liblameasmroutines_la_SOURCES = $(nasm_sources)

CLEANFILES = \
	choose_table.o.lst \
	choose_table.lo.lst \
	cpu_feat.o.lst \
	cpu_feat.lo.lst \
	fft3dn.o.lst \
	fft3dn.lo.lst \
	fftsse.o.lst \
	fftsse.lo.lst

EXTRA_DIST = \
	fft.nas \
	fftfpu.nas \
	ffttbl.nas \
	scalar.nas

NASM = @NASM@
NASMFLAGS=@NASM_FORMAT@ -i $(top_srcdir)/libmp3lame/@CPUTYPE@/

.nas.o: $< nasm.h
	$(NASM) $(NASMFLAGS) $< -o $@ -l $@.lst

.nas.lo: $< nasm.h
	$(ECHO) '# Generated by ltmain.sh - GNU libtool 1.5.22 (1.1220.2.365 2005/12/18 22:14:06)' >$@
	$(ECHO) "pic_object='$*.o'" >>$@
	$(ECHO) "non_pic_object='$*.o'" >>$@
	$(NASM) $(NASMFLAGS) $< -o $*.o -l $@.lst

COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) \
	$(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
CCLD = $(CC)
LINK = $(LIBTOOL) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@


#$(OBJECTS): libtool
#libtool: $(LIBTOOL_DEPS)
#	$(SHELL) $(top_builddir)/config.status --recheck
