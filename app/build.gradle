plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.devtools.ksp'
    id 'kotlin-parcelize'
}

android {

    namespace 'online.yllh.smartinspect'
    compileSdk 35
    buildFeatures {
        buildConfig = true
        viewBinding = true
    }
    defaultConfig {
        applicationId "online.yllh.smartinspect"
        minSdk 23
        targetSdk 34
        versionCode 37
        versionName "1.3.7"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters "armeabi-v7a", "arm64-v8a"
        }
        externalNativeBuild {
            cmake {
                cppFlags ""
                abiFilters "armeabi-v7a", "arm64-v8a"
            }
        }
    }
    signingConfigs {
        release {
            storeFile file("key.keystore")
            storePassword "ptz9WcGGMX&y"
            keyAlias "key0"
            keyPassword "ptz9WcGGMX&y"
        }
    }
    externalNativeBuild {
        cmake {
            path "CMakeLists.txt"
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            buildConfigField("String", "API_URL", "\"http://dev.guli.yllh.online\"")
            applicationIdSuffix ".dev"
        }
        releasePrd {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            buildConfigField("String", "API_URL", "\"https://prod.album.yllh.online\"")
        }
        debug {
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.release
            buildConfigField("String", "API_URL", "\"http://dev.guli.yllh.online\"")
            applicationIdSuffix ".dev"
        }
        debugPrd {
            debuggable true
            minifyEnabled false
            signingConfig signingConfigs.release
            buildConfigField("String", "API_URL", "\"https://prod.album.yllh.online\"")
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }
    buildFeatures {
        viewBinding true
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    android.applicationVariants.all { variant ->
        variant.outputs.all {
            def appName = variant.buildType.name.endsWith("Prd") ? "古树通APP" : "古树通DEV"
            outputFileName = "${appName}-v${variant.versionName}(${variant.versionCode})-${variant.buildType.name}-${getDate(false)}.apk"
        }
    }
}

static String getDate(boolean isGetStr) {
    return isGetStr ? "\"" + (new Date()).format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("GMT+08:00")) + "\"" : (new Date()).format("yyyyMMddHHmm", TimeZone.getTimeZone("GMT+08:00"))
}

dependencies {
    implementation 'androidx.core:core-ktx:1.16.0'
    implementation 'androidx.appcompat:appcompat:1.7.1'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.viewpager2:viewpager2:1.1.0'

    // PhotoView for image zoom and pan
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'

    def nav_version = "2.9.0"
    implementation "androidx.navigation:navigation-fragment-ktx:$nav_version"
    implementation "androidx.navigation:navigation-ui-ktx:$nav_version"

    def room_version = "2.7.2"
    implementation "androidx.room:room-runtime:$room_version"
    ksp "androidx.room:room-compiler:$room_version"
    implementation "androidx.room:room-ktx:$room_version"

    def coroutines_version = "1.10.2"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutines_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutines_version"

    def lifecycle_version = "2.9.1"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"

    def work_version = "2.9.0"
    implementation "androidx.work:work-runtime-ktx:$work_version"

    implementation 'pub.devrel:easypermissions:3.0.0'
    implementation 'com.github.bumptech.glide:glide:4.16.0'

    def retrofit_version = "2.9.0"
    implementation "com.squareup.retrofit2:retrofit:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit_version"

    def okhttp_version = "4.12.0"
    implementation("com.squareup.okhttp3:okhttp:$okhttp_version")
    implementation("com.squareup.okhttp3:logging-interceptor:$okhttp_version")

    implementation 'com.github.Dhaval2404:ImagePicker:v2.1'

    def immersionbar_version = "3.2.2"
    implementation "com.geyifeng.immersionbar:immersionbar:$immersionbar_version"
    implementation "com.geyifeng.immersionbar:immersionbar-ktx:$immersionbar_version"

    //其中latest.release指代最新Bugly SDK版本号，也可以指定明确的版本号，例如4.0.3
    implementation 'com.tencent.bugly:crashreport:latest.release'

    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation project(':module:edit_engine')
    implementation project(':module:camera_engine')
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
}
