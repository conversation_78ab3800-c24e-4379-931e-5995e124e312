package com.tapapk.camera_engine.core

import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.graphics.PointF
import android.graphics.Rect
import android.graphics.SurfaceTexture
import android.hardware.Camera
import android.opengl.GLES20
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.util.Size
import android.view.Surface
import android.view.WindowManager
import com.tapapk.camera_engine.config.EffectsConfig
import com.tapapk.camera_engine.egl.GLThread
import com.tapapk.camera_engine.`interface`.CameraListener
import com.tapapk.camera_engine.`interface`.GLCreateListener
import com.tapapk.camera_engine.render.CameraRender
import com.tapapk.camera_engine.render.EffectsRender
import com.tapapk.camera_engine.utils.CameraFrame
import com.tapapk.camera_engine.utils.CameraResolutionInfo
import com.tapapk.camera_engine.utils.EffectsUtils
import com.tapapk.camera_engine.utils.Exif
import com.tapapk.camera_engine.utils.GLUtils
import java.util.concurrent.Semaphore
import javax.microedition.khronos.egl.EGLContext


class CameraEngineCore {
    private var context: Context? = null
    private var glThread: GLThread? = null
    private var glHandler: Handler? = null
    private var mainHandler: Handler? = null
    private var numberOfCameras = 0
    private var camera: Camera? = null
    private var cameraTextureId = 0
    private var stickerTextureId = 0
    private var cameraSurface: SurfaceTexture? = null
    private var cameraRender: CameraRender? = null
    private var effectsRender: EffectsRender? = null
    private var cameraResolutionInfo: CameraResolutionInfo? = null
    private val cameraMatrix = Matrix()
    private var cameraOrientation: Int = 0
    private var viewWidth = 0
    private var viewHeight = 0
    private var canDraw = true
    private val cameraInfo = Camera.CameraInfo()
    private var lastRenderTime: Long = 0
    private val snapshotConfig = EffectsConfig()
    private val mCameraOpenCloseLock = Semaphore(1)

    var maxZoom = 0
    var maxExposure = 0
    var minExposure = 0
    var currentFacing = Camera.CameraInfo.CAMERA_FACING_BACK
    var currentFlashMode = FlashMode.AUTO
    var currentFocusMode = FocusMode.AUTO
    var currentFrameRatio = CameraFrame.FRAME_4_3
    var cameraListener: CameraListener? = null
    fun create(
        context: Context,
        surface: SurfaceTexture,
        width: Int,
        height: Int,
        glCreateListener: GLCreateListener?
    ) {
        this.context = context
        createGLEnvironment(surface, null, width, height, glCreateListener)
    }

    private fun createGLEnvironment(
        surface: SurfaceTexture?,
        eglSharedContext: EGLContext?,
        width: Int,
        height: Int,
        glCreateListener: GLCreateListener?
    ) {
        glThread = GLThread(surface, eglSharedContext, width, height)
        glThread?.start()
        if (glThread == null) {
            glCreateListener?.error()
            return
        }
        viewWidth = width
        viewHeight = height
        glHandler = Handler(glThread!!.looper)
        mainHandler = Handler(Looper.getMainLooper())
        glHandler?.post {
            glThread?.initEGL {
                if (it) {
                    cameraTextureId = GLUtils.createOESEmptyTexture2D()
                    cameraRender = CameraRender(this.context, width, height)
                    effectsRender = EffectsRender(this.context, width, height)
                    effectsRender?.setMatrix(cameraMatrix)
                    cameraRender?.oesTexture = cameraTextureId
                    cameraSurface = SurfaceTexture(cameraTextureId)
                    cameraSurface?.setOnFrameAvailableListener {
                        cameraSurface?.updateTexImage()
                        val now = System.currentTimeMillis()
                        val interval = now - lastRenderTime
                        if (interval < 20) { // 50帧
                            // 限制50帧刷新频率，否则低端机型eglSwap崩溃
//                            Log.d("Render","requestRender-skip")
                        } else {
//                            Log.d("Render","requestRender-render")
                            lastRenderTime = System.currentTimeMillis()
                            cameraRender?.draw()
                            effectsRender?.cameraTexture = cameraRender?.outTexture!!
                            effectsRender?.draw()
                            if (canDraw) {
                                requestRender()
                            }
                        }
                    }
                    mainHandler?.post {
                        initCamera()
                        glCreateListener?.success()
                    }
                } else {
                    mainHandler?.post {
                        glCreateListener?.error()
                    }
                }
            }
        }
    }

    private fun initCamera() {
        numberOfCameras = Camera.getNumberOfCameras()
        openCamera(currentFacing)
        setCameraRatio(currentFrameRatio)
        refreshMatrix()
    }

    fun switchCamera(facing:Int) {
        closeCamera()
        openCamera(facing)
        setCameraPixel(currentFrameRatio)
        startPreview()
    }

    fun setCameraRatio(cameraFrame: CameraFrame) {
        stopPreview()
        setCameraPixel(cameraFrame)
        startPreview()
    }

    fun openCamera() {
        openCamera(currentFacing)
        setCameraPixel(currentFrameRatio)
        startPreview()
    }

    private fun openCamera(facing: Int) {
        Log.i("Camera","相机初始化")
        currentFacing = facing
        if (camera != null) {
            return
        }
        for (i in 0 until numberOfCameras) {
            Camera.getCameraInfo(i, cameraInfo)
            if (cameraInfo.facing == facing) {
                cameraOrientation = getCameraDisplayOrientation(
                    context,
                    cameraInfo.orientation,
                    cameraInfo.facing
                )
                camera = Camera.open(i)
                camera?.setPreviewTexture(cameraSurface)
                camera?.setDisplayOrientation(cameraOrientation)
                break
            }
        }
        if (facing == Camera.CameraInfo.CAMERA_FACING_FRONT){
            effectsRender?.isFacingFront = true
        }else {
            effectsRender?.isFacingFront = false
        }
        //设置自动对焦
        val parameters = camera?.parameters ?: return
        calculateBestSize(parameters.supportedPreviewSizes, parameters.supportedPictureSizes)
        val isSupportAutoFocus = parameters.supportedFocusModes.contains(
            Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE
        ) &&
                parameters.supportedFocusModes != null
        if (isSupportAutoFocus) {
            parameters.focusMode = Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE
        }
        maxZoom = parameters.maxZoom
        maxExposure = parameters.maxExposureCompensation
        minExposure = parameters.minExposureCompensation
        camera?.parameters = parameters
        setFlashMode(currentFlashMode)
        Log.e("fggfgg",parameters.zoom.toString())
    }

    fun snapshot(processDone: (bitmap: Bitmap,ByteArray) -> Unit) {
        try {
            mCameraOpenCloseLock.acquire()

            camera?.let { cam ->
                cam.takePicture(
                    Camera.ShutterCallback { },
                    null,
                    Camera.PictureCallback { bytes, takeCamera ->
                        try {
                            cam.stopPreview()
                            cam.cancelAutoFocus() // 拍照完成后一定要取消对焦
                            val exif = Exif.getExifData(bytes)
                            val bitmap = transformation(cameraInfo.orientation, cameraInfo.facing, bytes)
                            processDone(bitmap, exif)
                        } catch (e: Exception) {
                            e.printStackTrace()
                        } finally {
                            mCameraOpenCloseLock.release()
                        }
                    }
                )
            } ?: run {
                mCameraOpenCloseLock.release()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            mCameraOpenCloseLock.release()
        }
    }

    fun setCameraZoom(value: Int){
        val parameters = camera?.parameters ?: return
        if (parameters.isZoomSupported){
            parameters.zoom = value
            camera?.parameters = parameters
        }
    }

    fun setCameraExposure(value: Int){
        val parameters = camera?.parameters ?: return
        parameters.exposureCompensation = value
        camera?.parameters = parameters
    }

    fun getCameraExposure():Int{
        val parameters = camera?.parameters ?: return 0
        return parameters.exposureCompensation
    }
    fun getCameraExposureStep():Float{
        val parameters = camera?.parameters ?: return 0f
        return parameters.exposureCompensationStep
    }

    fun setFocusArea(point: PointF, width: Int, height: Int, callback: Camera.AutoFocusCallback?) {
        val parameters = camera?.parameters ?: return
        if (parameters.maxNumFocusAreas <= 0) {
            camera?.autoFocus(callback)
            return
        }
        camera?.cancelAutoFocus()
        val areas: MutableList<Camera.Area> = ArrayList<Camera.Area>()
        val areasMatrix: MutableList<Camera.Area> = ArrayList<Camera.Area>()
        val focusRect = calculateTapArea(point.x, point.y, width, height, 1.0f)
        Log.e("onAutoFocus","${focusRect.toString()}")
        val matrixRect = calculateTapArea(point.x, point.y, width, height, 1.5f)
        areas.add(Camera.Area(focusRect, 1000))
        areasMatrix.add(Camera.Area(matrixRect, 1000))
        parameters.meteringAreas = areasMatrix
        parameters.focusMode = Camera.Parameters.FOCUS_MODE_AUTO
        parameters.focusAreas = areas
        try {
            camera?.setParameters(parameters)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        camera?.autoFocus(callback)
    }

    private fun calculateTapArea(
        x: Float,
        y: Float,
        width: Int,
        height: Int,
        coefficient: Float
    ): Rect {
        var areaX = when (cameraOrientation) { // 获取映射区域的X坐标
            90, 270 -> {
                ((y / height * 2000) - 1000).toInt()
            }
            else -> {
                ((x / width * 2000) - 1000).toInt()
            }
        }
        var areaY = when (cameraOrientation) { // 获取映射区域的Y坐标
            90, 270 -> {
                ((x / width * 2000) - 1000).toInt()
            }
            else -> {
                ((y / height * 2000) - 1000).toInt()
            }
        }
        areaY = -areaY // 镜像反转
//        val areaX:Int = ((y / height * 2000) - 1000).toInt() // 获取映射区域的X坐标
//        val areaY:Int = -((x / width * 2000) - 1000).toInt() // 获取映射区域的Y坐标
        Log.e("onAutoFocus","areaX:${areaX} areaY:${areaY}")
        // 创建Rect区域
        // 创建Rect区域
        val focusArea = Rect()
        focusArea.left = (areaX - (50 * coefficient).toInt()).coerceAtLeast(-1000)// 取最大或最小值，避免范围溢出屏幕坐标
        focusArea.top = (areaY - (50 * coefficient).toInt()).coerceAtLeast(-1000) // 取最大或最小值，避免范围溢出屏幕坐标
        focusArea.right = (areaX + (50 * coefficient).toInt()).coerceAtMost(1000) // 取最大或最小值，避免范围溢出屏幕坐标
        focusArea.bottom = (areaY + (50 * coefficient).toInt()).coerceAtMost(1000)// 取最大或最小值，避免范围溢出屏幕坐标
        return focusArea
//        val focusAreaSize = 100f
//        val areaSize = focusAreaSize * coefficient
//        var centerY = 0f
//        var centerX = 0f
//        centerX = (x / width * 2000 - 1000)
//        centerY = (y / height * 2000 - 1000)
//        val left = clamp(centerX - areaSize / 2f, -1000f, 1000f)
//        val top = clamp(centerY - areaSize / 2f, -1000f, 1000f)
//        val rectF = RectF(left, top, left + areaSize, top + areaSize)
//        return Rect(
//            Math.round(rectF.left),
//            Math.round(rectF.top),
//            Math.round(rectF.right),
//            Math.round(rectF.bottom)
//        )
    }

    private fun clamp(x: Float, min: Float, max: Float): Float {
        if (x > max) {
            return max
        }
        return if (x < min) {
            min
        } else x
    }

    /**
     * 矩阵变换
     */
    private fun transformation(
        cameraOrientation: Int,
        cameraFacing: Int,
        bytes: ByteArray
    ): Bitmap {
        val bitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
        val matrix = Matrix()
        /**
         * 一定要先旋转再做镜像
         */
        when (cameraOrientation) {
            90 -> matrix.postRotate(90f)
            180 -> matrix.postRotate(180f)
            270 -> matrix.postRotate(270f)
        }
        if (cameraFacing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
            matrix.postScale(-1f, 1f)//前置摄像头镜像翻转
        }
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }

    fun setFlashMode(flashMode: FlashMode) {
        val parameters = camera?.parameters
        this.currentFlashMode = flashMode
        when (flashMode) {
            FlashMode.AUTO -> {
                if (parameters?.supportedFlashModes?.contains("auto") == true){
                    parameters?.flashMode = Camera.Parameters.FLASH_MODE_AUTO
                }
            }
            FlashMode.OFF -> {
                if (parameters?.supportedFlashModes?.contains("off") == true) {
                    parameters?.flashMode = Camera.Parameters.FLASH_MODE_OFF
                }
            }
            FlashMode.ON -> {
                if (parameters?.supportedFlashModes?.contains("on") == true) {
                    parameters?.flashMode = Camera.Parameters.FLASH_MODE_ON
                }
            }
            FlashMode.TORCH -> {
                if (parameters?.supportedFlashModes?.contains("torch") == true) {
                    parameters?.flashMode = Camera.Parameters.FLASH_MODE_TORCH
                }
            }
        }
        camera?.parameters = parameters
    }

    fun setFocusMode(focusMode: FocusMode) {
        this.currentFocusMode = focusMode
        val parameters = camera?.parameters
        val supportAutoFocusList = parameters?.supportedFocusModes ?: return
        camera?.cancelAutoFocus()
        when (focusMode) {
            FocusMode.AUTO -> {
                val isSupportAutoFocus =
                    supportAutoFocusList.contains(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE)
                if (isSupportAutoFocus) {
                    parameters.focusMode = Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE
                }
            }
            FocusMode.TOUCH -> {
                val isSupportAutoFocus =
                    supportAutoFocusList.contains(Camera.Parameters.FOCUS_MODE_AUTO)
                if (isSupportAutoFocus) {
                    parameters.focusMode = Camera.Parameters.FOCUS_MODE_AUTO
                }
            }
        }
        camera?.parameters = parameters
    }

    fun setCameraPixel(cameraFrame: CameraFrame) {
        currentFrameRatio = cameraFrame
        snapshotConfig.frameRatio = currentFrameRatio
        //设置分辨率
        val parameters = camera?.parameters ?: return
        val bestPreviewSize = getBestPreviewSize(cameraFrame)
        val bestPictureSize = getBestPictureSize(cameraFrame)
        Log.e("CurrentPixel", "width:${bestPreviewSize.width} height:${bestPreviewSize.height}")
        parameters.setPreviewSize(bestPreviewSize.width, bestPreviewSize.height)
        parameters.setPictureSize(bestPictureSize.width, bestPictureSize.height)
        var renderWidth = when (cameraOrientation) {
            90, 270 -> {
                bestPreviewSize.height
            }
            else -> bestPreviewSize.width
        }
        var renderHeight = when (cameraOrientation) {
            90, 270 -> {
                bestPreviewSize.width
            }
            else -> bestPreviewSize.height
        }
        camera?.parameters = parameters
        refreshMatrix()
        effectsRender?.calculateMatrix()
        effectsRender?.setFrameSize(renderWidth, renderHeight)
    }

    fun refreshMatrix() {
        if (currentFacing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
            cameraMatrix.reset()
            cameraMatrix.postTranslate(viewWidth / 2f, viewHeight / 2f)
            cameraMatrix.postRotate(cameraOrientation.toFloat(), viewWidth / 2f, viewHeight / 2f)
            if (viewWidth == viewHeight) {
                cameraMatrix.postScale((4f / 3f), (4f / 3f), viewWidth / 2f, viewHeight / 2f)
            } else {
                cameraMatrix.postScale(1f, 1f, viewWidth / 2f, viewHeight / 2f)
            }
        } else {
            cameraMatrix.reset()
            cameraMatrix.postTranslate(viewWidth / 2f, viewHeight / 2f)
            cameraMatrix.postRotate(cameraOrientation.toFloat(), viewWidth / 2f, viewHeight / 2f)
            if (viewWidth == viewHeight) {
                cameraMatrix.postScale((4f / 3f), -(4f / 3f), viewWidth / 2f, viewHeight / 2f)
            } else {
                cameraMatrix.postScale(1f, -1f, viewWidth / 2f, viewHeight / 2f)
            }
        }
    }

    fun startPreview() {
        camera?.startPreview()
    }


    fun stopPreview() {
        camera?.stopPreview()
    }

    fun closeCamera() {
        camera?.stopPreview()
        camera?.release()
        camera = null
    }

    fun destroy() {
        closeCamera()
        glHandler?.post {
            if(cameraTextureId != 0){
                GLES20.glDeleteTextures(1, intArrayOf(cameraTextureId),0)
                cameraTextureId = 0
            }
            cameraRender?.destroy()
            effectsRender?.destroy()
            glThread?.destroyGL()
            glHandler = null
        }
    }

    fun cameraHardwareAvailable(): Boolean {
        //检查相机是否可用
        val cameraHardwareAvailable =
            context?.packageManager?.hasSystemFeature(PackageManager.FEATURE_CAMERA_ANY)
        if (cameraHardwareAvailable == null || !cameraHardwareAvailable) {
            return false
        }
        return true
    }

    fun changeCameraViewSize(width: Int, height: Int) {
        effectsRender?.viewSizeChanged(width.toFloat(), height.toFloat())
        viewWidth = width
        viewHeight = height
        refreshMatrix()
        effectsRender?.calculateMatrix()
    }

    fun setSoftSkinValue(value: Int) {
        snapshotConfig.softSkin = value
        effectsRender?.setTexelOffset(EffectsUtils.transSoftSkin(value).toFloat())
    }

    fun setBeautyValue(value: Int) {
        snapshotConfig.beauty = value
        val beautyValue = EffectsUtils.transBeautyValue(value)
        effectsRender?.setBeautyLevel(beautyValue.beautyLevel.toFloat())
        effectsRender?.setToneLevel(beautyValue.toneLevel.toFloat())
        effectsRender?.setBrightLevel(beautyValue.brightLevel.toFloat())
    }

    fun setEnableLut(boolean: Boolean) {
        snapshotConfig.lutEnable = boolean
        if (boolean) {
            effectsRender?.enableLut(1)
        } else {
            effectsRender?.enableLut(0)
        }
    }

    fun lutEnable(): Boolean {
        return snapshotConfig.lutEnable
    }

    fun setLut(context: Context?, resId: Int, cube: Float) {
        snapshotConfig.lutRes = resId
        glHandler?.post {
            effectsRender?.setLut(context, resId)
            effectsRender?.setLutCube(cube)
        }
    }

    fun setSticker(bitmap: Bitmap){
        glHandler?.post {
            effectsRender?.setSticker(bitmap)
        }
    }

    fun setLut(bitmap: Bitmap, cube: Float) {
        snapshotConfig.lut = bitmap
        glHandler?.post {
            effectsRender?.setLut(bitmap)
            effectsRender?.setLutCube(cube)
        }
    }

    fun setLutIntensity(value: Int) {
        snapshotConfig.lutLevel = value
        effectsRender?.setLutIntensity(EffectsUtils.transLutIntensity(value).toFloat())
    }

    private fun requestRender() {
        glHandler?.post {
            glThread?.swapBuffer()
        }
    }

    private fun getBestPreviewSize(cameraFrame: CameraFrame): Size {
        return cameraResolutionInfo?.targetPreviewSize?.get(cameraFrame)!!
    }

    private fun getBestPictureSize(cameraFrame: CameraFrame): Size {
        return cameraResolutionInfo?.targetPictureSize?.get(cameraFrame)!!
    }

    private fun calculateBestSize(
        previewSizesAll: List<Camera.Size>,
        pictureSizesAll: List<Camera.Size>
    ) {
        val dm: DisplayMetrics = context?.resources?.displayMetrics!!
        val screenWidth: Int = dm.widthPixels
        val screenHeight: Int = dm.heightPixels
        cameraResolutionInfo =
            CameraResolutionInfo(previewSizesAll, pictureSizesAll, Size(screenWidth, screenHeight))
    }

    /**
     * 旋转预览方向
     */
    private fun getCameraDisplayOrientation(
        context: Context?,
        cameraOrientation: Int,
        cameraFacing: Int
    ): Int {
        val rotation =
            (context?.getSystemService(Context.WINDOW_SERVICE) as WindowManager).defaultDisplay?.rotation
        var degrees = 0
        when (rotation) {
            Surface.ROTATION_0 -> degrees = 0
            Surface.ROTATION_90 -> degrees = 90
            Surface.ROTATION_180 -> degrees = 180
            Surface.ROTATION_270 -> degrees = 270
        }
        var result: Int
        if (cameraFacing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
            result = (cameraOrientation + degrees) % 360
            result = (360 - result) % 360  // compensate the mirror
        } else {  // back-facing
            result = (cameraOrientation - degrees + 360) % 360
        }
        return result
    }
}