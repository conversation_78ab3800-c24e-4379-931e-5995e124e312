package com.tapapk.camera_engine.render

import android.content.Context
import android.graphics.Bitmap
import android.opengl.GLES11Ext
import android.opengl.GLES20
import com.tapapk.camera_engine.shader.program.CameraShaderProgram
import com.tapapk.camera_engine.shader.program.EffectsShaderProgram
import com.tapapk.camera_engine.utils.GLUtils
import com.yunqi.gleditengine.edit.render.BaseRender
import java.nio.FloatBuffer


class CameraRender(context: Context?,width:Int,height:Int):BaseRender() {
    var cameraProgram = CameraShaderProgram(context)
    var oesTexture = 0
    var outTexture = 0
    var frameBuffer = 0
    init {
        viewWidth = width.toFloat()
        viewHeight = height.toFloat()
        portWidth = width.toFloat()
        portHeight = height.toFloat()
        init()
        createBufferAndOutTexture()
    }
    private fun createBufferAndOutTexture(){
        val frameBuffers = IntArray(1)
        GLES20.glGenFramebuffers(1, frameBuffers, 0)
        frameBuffer = frameBuffers[0]
        outTexture = GLUtils.createEmptyTexture2D(viewWidth.toInt(),viewHeight.toInt())
    }
    public override fun draw() {
        super.draw()
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, frameBuffer)
        GLES20.glFramebufferTexture2D(GLES20.GL_FRAMEBUFFER, GLES20.GL_COLOR_ATTACHMENT0, GLES20.GL_TEXTURE_2D, outTexture, 0)
        cameraProgram.useProgram()
        cameraProgram.setUniform(getIdentity(),oesTexture)
        GLES20.glEnableVertexAttribArray(cameraProgram.aPositionLocation)
        GLES20.glVertexAttribPointer(cameraProgram.aPositionLocation, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer)
        GLES20.glEnableVertexAttribArray(cameraProgram.aTextureCoordinateLocation)
        GLES20.glVertexAttribPointer(cameraProgram.aTextureCoordinateLocation, 2, GLES20.GL_FLOAT, false, 0, fragmentBuffer)
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4)
        GLES20.glDisableVertexAttribArray(cameraProgram.aPositionLocation)
        GLES20.glDisableVertexAttribArray(cameraProgram.aTextureCoordinateLocation)
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0)
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0)
    }

    public override fun destroy() {
        super.destroy()
        if(oesTexture != 0){
            GLES20.glDeleteTextures(1, intArrayOf(oesTexture), 0)
            oesTexture = 0
        }
        if (outTexture != 0){
            GLES20.glDeleteTextures(1, intArrayOf(outTexture), 0)
            outTexture = 0
        }
        if (frameBuffer != 0){
            GLES20.glDeleteBuffers(1, intArrayOf(frameBuffer), 0)
            frameBuffer = 0
        }
        cameraProgram.deleteProgram()
    }
}