package com.tapapk.camera_engine.utils

import android.hardware.Camera
import android.os.Build
import android.util.Rational
import android.util.Size
import androidx.annotation.RequiresApi
import java.util.*
import kotlin.collections.ArrayList
import kotlin.math.abs

/**
 * 相机画幅
 */
enum class CameraFrame {
    FRAME_1_1, FRAME_4_3, FRAME_16_9, FRAME_FULL;
}
@RequiresApi(Build.VERSION_CODES.LOLLIPOP)
val RATIO_16_9 = Rational(16, 9)
@RequiresApi(Build.VERSION_CODES.LOLLIPOP)
val RATIO_4_3 = Rational(4, 3)
val RATIO_1_1 = Rational(1, 1)
@RequiresApi(Build.VERSION_CODES.LOLLIPOP)
class CameraResolutionInfo(
        previewSizesAll: List<Camera.Size>,
        pictureSizesAll: List<Camera.Size>,
        screenSize: Size
) {

    val targetPreviewSize = EnumMap<CameraFrame, Size>(CameraFrame::class.java)
    val targetPictureSize = EnumMap<CameraFrame, Size>(CameraFrame::class.java)

    init {
        val start = System.currentTimeMillis()
        // 适配平板类
        val shortScreen = if (screenSize.width < screenSize.height) screenSize.width else screenSize.height
        val longScreen = if (screenSize.width > screenSize.height) screenSize.width else screenSize.height
        val screenRatio = Rational(longScreen, shortScreen)
        L.d("屏幕尺寸:width=${screenSize.width}, height=${screenSize.height}, ration=$screenRatio")
        L.d("预览原始尺寸:")
        printSizeInfo(previewSizesAll)
        L.d("照片原始尺寸:")
        printSizeInfo(pictureSizesAll)
        // 筛选相同比例的预览尺寸和图片尺寸
        val filteredPreviewSizes = filterRatioList(previewSizesAll, pictureSizesAll, screenRatio)
        val filteredPictureSizes = filterRatioList(pictureSizesAll, filteredPreviewSizes, screenRatio)
        L.d("预览筛选尺寸:")
        printSizeInfo(filteredPreviewSizes)
        L.d("图片筛选尺寸:")
        printSizeInfo(filteredPictureSizes)
        // 预览分辨率选择短边不超过屏幕尺寸的比例
        selectTargetSize(filteredPreviewSizes, targetPreviewSize, screenRatio, shortScreen)
        // 照片分辨率选择长边不超过MAX_PHOTO_LENGTH尺寸的比例
        selectTargetSize(filteredPictureSizes, targetPictureSize, screenRatio, MAX_PHOTO_LENGTH, false)
        // 最后筛选逻辑，剔除屏幕小于16:9的手机的大于16:9的分辨率
//        if (screenRatio.toDouble() + 0.03 < RATIO_16_9.toFloat()) {
//            L.d("删除大于16:9的比例")
//            targetPreviewSize.remove(CameraFrame.FRAME_16_9)
//            targetPictureSize.remove(CameraFrame.FRAME_16_9)
//            targetPreviewSize.remove(CameraFrame.FRAME_FULL)
//            targetPictureSize.remove(CameraFrame.FRAME_FULL)
//        }
        L.d("筛选完成consume:${System.currentTimeMillis() - start}, targetPreviewSize=$targetPreviewSize, targetPictureSize=$targetPictureSize")
    }

    private fun printSizeInfo(sizeList: List<Camera.Size>) {
        L.d("printSizeInfo length:${sizeList.size}")
        for (size in sizeList) {
            L.d("size:${size.width} x ${size.height} -- ${Rational(size.width, size.height)}")
        }
    }

    companion object {
        //获取openGL的边长限制
        val MAX_PHOTO_LENGTH = 4096

        /**
         * 小于0.03f认为比例相同
         */
        private fun isSameRatio(ratio1: Rational, ratio2: Rational): Boolean {
            return abs(ratio1.toDouble() - ratio2.toDouble()) < 0.03
        }

        private fun filterRatioList(sourceList: List<Camera.Size>, targetRatioList: List<Camera.Size>, screenRatio: Rational): List<Camera.Size> {
            val filteredSizesList = ArrayList<Camera.Size>()
            for (sourceSize in sourceList) {
                val sourceRatio = Rational(sourceSize.width, sourceSize.height)
                if (isSameRatio(sourceRatio, RATIO_4_3) || isSameRatio(sourceRatio, RATIO_16_9)) {
                    // 筛入16:9和4:3
                    filteredSizesList.add(sourceSize)
                } else if (sourceRatio > screenRatio || sourceRatio < RATIO_16_9) {
                    // 筛除大于屏幕比例和小于16:9的
                } else {
                    // 筛入和相片比例相同的
                    for (targetSize in targetRatioList) {
                        val targetRatio = Rational(targetSize.width, targetSize.height)
                        if (isSameRatio(sourceRatio, targetRatio)) {
                            filteredSizesList.add(sourceSize)
                        }
                    }
                }
            }
            return filteredSizesList
        }

        /**
         * 选择支持的CameraFrame目标比例的分辨率尺寸
         * @param sourceSizeList 原始支持的尺寸列表，始终认为Camera.Size中width大于height
         * @param targetMap 结果结果映射表
         * @param fullScreenRatio 全屏比例
         * @param targetLength 尺寸中短边的最长长度，用于限制筛选结果的尺寸大小
         * @param isCompareShortLength true-以短边为目标比较长度, false-以长边
         * @param isCelling 是否不能超过target长度
         */
        private fun selectTargetSize(sourceSizeList: List<Camera.Size>,
                                     targetMap: EnumMap<CameraFrame, Size>,
                                     fullScreenRatio: Rational,
                                     targetLength: Int,
                                     isCompareShortLength: Boolean = true,
                                     isCelling: Boolean = true) {
            for (sourceSize in sourceSizeList) {
                val sourceRatio = Rational(sourceSize.width, sourceSize.height)
                val sourceLength = if (isCompareShortLength) sourceSize.height else sourceSize.width
                when {
                    sourceRatio > RATIO_16_9 && sourceRatio <= fullScreenRatio -> {
                        // 全面屏，大于16:9的相机分辨率且小于等于屏幕比例
                        if (checkCelling(isCelling, targetLength, sourceLength) &&
                                compareLastSizeAndRatio(sourceSize, targetMap[CameraFrame.FRAME_FULL],
                                        fullScreenRatio, targetLength, isCompareShortLength)
                        ) {
                            targetMap[CameraFrame.FRAME_FULL] = Size(sourceSize.width, sourceSize.height)
                        }
                    }
                    isSameRatio(sourceRatio, RATIO_16_9) -> {
                        if (checkCelling(isCelling, targetLength, sourceLength) &&
                                compareLastSize(sourceSize, targetMap[CameraFrame.FRAME_16_9],
                                        targetLength, isCompareShortLength)
                        ) {
                            targetMap[CameraFrame.FRAME_16_9] = Size(sourceSize.width, sourceSize.height)
                        }
                    }
                    isSameRatio(sourceRatio, RATIO_4_3) -> {
                        if (checkCelling(isCelling, targetLength, sourceLength) &&
                                compareLastSize(sourceSize, targetMap[CameraFrame.FRAME_4_3],
                                        targetLength, isCompareShortLength)
                        ) {
                            targetMap[CameraFrame.FRAME_4_3] = Size(sourceSize.width, sourceSize.height)
                            targetMap[CameraFrame.FRAME_1_1] = Size(sourceSize.width, sourceSize.height)
                        }
                    }
                }
            }
        }

        private fun checkCelling(checkCelling: Boolean, targetLength: Int, sourceLength: Int) = if (checkCelling) {
            sourceLength <= targetLength
        } else {
            true
        }

        private fun compareLastSize(currentSize: Camera.Size, lastSize: Size?, targetLength: Int, isCompareShortLength: Boolean): Boolean {
            return if (lastSize == null) {
                true
            } else {
                val currentLength = if (isCompareShortLength) currentSize.height else currentSize.width
                val lastLength = if (isCompareShortLength) lastSize.height else lastSize.width
                abs(targetLength - currentLength) < abs(targetLength - lastLength)
            }
        }

        private fun compareLastSizeAndRatio(currentSize: Camera.Size, lastSize: Size?, targetRatio: Rational, targetLength: Int, isCompareShortLength: Boolean): Boolean {
            return if (lastSize == null) {
                true
            } else {
                val currentRatio = currentSize.width.toFloat() / currentSize.height
                val lastRatio = lastSize.width.toFloat() / lastSize.height
                if (currentRatio != lastRatio) {
                    // 选择更接近targetRatio的
                    abs(targetRatio.toFloat() - currentRatio) < abs(targetRatio.toFloat() - lastRatio)
                } else {
                    // targetRatio相等, 选择更接近targetLength的
                    val currentLength = if (isCompareShortLength) currentSize.height else currentSize.width
                    val lastLength = if (isCompareShortLength) lastSize.height else lastSize.width
                    abs(targetLength - currentLength) < abs(targetLength - lastLength)
                }
            }
        }

        private fun Int.closeToOf(target: Int, another: Int?): Boolean {
            return if (another == null) {
                true
            } else {
                abs(target - this) < abs(target - another)
            }
        }

    }

}