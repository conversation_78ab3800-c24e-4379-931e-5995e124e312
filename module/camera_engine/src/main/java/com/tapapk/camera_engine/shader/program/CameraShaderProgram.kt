package com.tapapk.camera_engine.shader.program

import android.content.Context
import android.opengl.GLES11Ext
import android.opengl.GLES20
import com.tapapk.camera_engine.shader.ShaderProgram
import online.yllh.smartinspect.camera.R

class CameraShaderProgram(context: Context?) :
        ShaderProgram(context, R.raw.vertex_camera, R.raw.fragment_camera) {

    // Uniform locations
    var uTextureSamplerLocation = -1
    var uMvpMatrixLocation = -1

    // Attribute locations
    var aPositionLocation = -1
    var aTextureCoordinateLocation = -1
    init {
        // Retrieve attribute locations for the shader program.
        aPositionLocation = GLES20.glGetAttribLocation(programId, "a_Position")
        aTextureCoordinateLocation = GLES20.glGetAttribLocation(programId, "a_TextureCoordinate")

        // Retrieve uniform locations for the shader program
        uTextureSamplerLocation = GLES20.glGetUniformLocation(programId, "u_TextureSampler")
        uMvpMatrixLocation = GLES20.glGetUniformLocation(programId, "u_MvpMatrix")

    }


    fun setUniform(mvpMatrix: FloatArray, textureId: Int) {
        GLES20.glUniformMatrix4fv(uMvpMatrixLocation, 1, false, mvpMatrix, 0)

        GLES20.glActiveTexture(GLES20.GL_TEXTURE0)
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId)
        GLES20.glUniform1i(uTextureSamplerLocation, 0)

    }
}
