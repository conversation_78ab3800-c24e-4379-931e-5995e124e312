#version 100
precision lowp float;
//国内部分手机跑shader必须设置精度
varying lowp vec2 vTextureCoord;

uniform sampler2D vTextureOrigin;
uniform sampler2D vTextureLut;
uniform sampler2D vTextureSticker; // 贴纸的纹理
uniform bool isFacingFront;
uniform bool enableLut;
uniform bool hidePort;
uniform lowp float lut_intensity;
uniform lowp float lut_position_offset;

uniform lowp vec2 singleStepOffset;
uniform lowp vec4 params;
uniform lowp float brightness;
uniform float texelWidthOffset;
uniform float texelHeightOffset;
uniform float lutCube;


vec4 lookup(in vec4 textureColor){
    mediump float blueColor = textureColor.b * (pow(lutCube,2.0)-1.0);
    mediump vec2 quad1;
    quad1.y = floor(floor(blueColor) / lutCube);
    quad1.x = floor(blueColor) - (quad1.y * lutCube);
    mediump vec2 quad2;
    quad2.y = floor(ceil(blueColor) / lutCube);
    quad2.x = ceil(blueColor) - (quad2.y * lutCube);
    lowp vec2 texPos1;
    texPos1.x = (quad1.x * 1.0/lutCube) + 0.5/pow(lutCube,3.0) + ((1.0/lutCube - 1.0/pow(lutCube,3.0)) * textureColor.r);
    texPos1.y = (quad1.y * 1.0/lutCube) + 0.5/pow(lutCube,3.0) + ((1.0/lutCube - 1.0/pow(lutCube,3.0)) * textureColor.g);
    lowp vec2 texPos2;
    texPos2.x = (quad2.x * 1.0/lutCube) + 0.5/pow(lutCube,3.0) + ((1.0/lutCube - 1.0/pow(lutCube,3.0)) * textureColor.r);
    texPos2.y = (quad2.y * 1.0/lutCube) + 0.5/pow(lutCube,3.0) + ((1.0/lutCube - 1.0/pow(lutCube,3.0)) * textureColor.g);
    lowp vec4 newColor1 = texture2D(vTextureLut, texPos1);
    lowp vec4 newColor2 = texture2D(vTextureLut, texPos2);
    lowp vec4 newColor = mix(newColor1, newColor2, fract(blueColor));
    return mix(textureColor, vec4(newColor.rgb, textureColor.w), lut_intensity);
}
vec2 rotateCoords(vec2 coord, vec2 center, float angle) {
    vec2 rotatedCoord;
    rotatedCoord.x = cos(angle) * (coord.x - center.x) - sin(angle) * (coord.y - center.y) + center.x;
    rotatedCoord.y = sin(angle) * (coord.x - center.x) + cos(angle) * (coord.y - center.y) + center.y;
    return rotatedCoord;
}

vec2 mirrorCoords(vec2 coord) {
    if(isFacingFront){
        return vec2(1.0 - coord.x, 1.0 - coord.y);
    }else{
        return vec2(1.0 - coord.x, coord.y);
    }
}

void main(){
    highp vec3 centralColor = texture2D(vTextureOrigin, vTextureCoord).rgb;
    vec4 lut_color;
    if(enableLut){
        lut_color = lookup(vec4(centralColor.rgb, 1.0));
    }else{
        lut_color = vec4(centralColor.rgb, 1.0);
    }
    if(vTextureCoord.y<0.0){
        lut_color = vec4(centralColor.rgb, 1.0);
    }
    //    vec4 makeupColor = vec4(makeup(lut_color.rgb),1.0);

    // 旋转贴纸纹理坐标
    vec2 rotatedCoord = rotateCoords(vTextureCoord, vec2(0.5, 0.5), radians(90.0));
    vec2 mirroredCoord = mirrorCoords(rotatedCoord);

    vec4 stickerColor = texture2D(vTextureSticker, mirroredCoord); // 获取旋转且镜像后的贴纸颜色
    gl_FragColor = mix(lut_color, stickerColor, stickerColor.a); // 将贴纸叠加到 lut_color 上
}
