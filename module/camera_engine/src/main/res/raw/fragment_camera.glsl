#version 100
#extension GL_OES_EGL_image_external:require
//precision highp float;

uniform samplerExternalOES u_TextureSampler;
varying highp vec2 vTextureCoord;


void main(){
    gl_FragColor = texture2D(u_TextureSampler, vTextureCoord);
}


//void main() {
//    vec4 color = texture2D(u_TextureSampler, vTextureCoord);
////        float gray = 0.299 * color.r + 0.587 * color.g + 0.114 * color.b;
////        vec4 newColor = vec4(gray, gray, gray, color.a);
////        gl_FragColor = mix(color, newColor, identity);
//    gl_FragColor = color;
//}