package com.tapapk.edit_engine.egl

import android.graphics.SurfaceTexture
import android.opengl.GLUtils
import android.os.HandlerThread
import javax.microedition.khronos.egl.*
import javax.microedition.khronos.opengles.GL


class GLThread(
    private var mSurfaceTexture: SurfaceTexture?,
    private val eglSharedContext: EGLContext?,
    private val width:Int = 0,
    private val height:Int = 0
) :
    HandlerThread("GLThread") {
    private var mEgl: EGL10? = null
    private var mEglDisplay: EGLDisplay = EGL10.EGL_NO_DISPLAY
    private var mEglContext: EGLContext = EGL10.EGL_NO_CONTEXT
    private var mEglSurface: EGLSurface = EGL10.EGL_NO_SURFACE
    private var mGL: GL? = null
    val configs: Array<EGLConfig?> = arrayOfNulls<EGLConfig>(1)
    val configAttribs = intArrayOf(
        EGL10.EGL_BUFFER_SIZE, 32,
        EGL10.EGL_ALPHA_SIZE, 8,
        EGL10.EGL_BLUE_SIZE, 8,
        EGL10.EGL_GREEN_SIZE, 8,
        EGL10.EGL_RED_SIZE, 8,
        EGL10.EGL_RENDERABLE_TYPE, EGL_OPENGL_ES2_BIT,
        EGL10.EGL_SURFACE_TYPE, EGL10.EGL_WINDOW_BIT,
        EGL10.EGL_NONE
    )

    fun initEGL(initEGLDone: (Boolean) -> Unit) {
        mEgl = EGLContext.getEGL() as EGL10
        mEglDisplay = mEgl!!.eglGetDisplay(EGL10.EGL_DEFAULT_DISPLAY)
        if (mEglDisplay === EGL10.EGL_NO_DISPLAY) {
            initEGLDone.invoke(false)
            throw RuntimeException(
                "eglGetdisplay failed : " +
                        GLUtils.getEGLErrorString(mEgl!!.eglGetError())
            )
        }
        val version = IntArray(2)
        if (!mEgl!!.eglInitialize(mEglDisplay, version)) {
            initEGLDone.invoke(false)
            throw RuntimeException(
                "eglInitialize failed : " +
                        GLUtils.getEGLErrorString(mEgl!!.eglGetError())
            )
        }
        val surfaceAttribs = intArrayOf(
            EGL10.EGL_WIDTH, width,
            EGL10.EGL_HEIGHT, height,
            EGL10.EGL_NONE
        )
        val numConfigs = IntArray(1)
        if (!mEgl!!.eglChooseConfig(mEglDisplay, configAttribs, configs, 1, numConfigs)) {
            initEGLDone.invoke(false)
            throw RuntimeException(
                "eglChooseConfig failed : " +
                        GLUtils.getEGLErrorString(mEgl!!.eglGetError())
            )
        }
        val contextAttribs = intArrayOf(
            EGL_CONTEXT_CLIENT_VERSION, 2,
            EGL10.EGL_NONE
        )
        if (eglSharedContext == null) {
            mEglContext =
                mEgl!!.eglCreateContext(
                    mEglDisplay,
                    configs[0],
                    EGL10.EGL_NO_CONTEXT,
                    contextAttribs
                )
        }else{
            mEglContext =
                mEgl!!.eglCreateContext(
                    mEglDisplay,
                    configs[0],
                    eglSharedContext,
                    contextAttribs
                )
        }
        mEglSurface =
            if (mSurfaceTexture != null) {
                //屏上渲染
                mEgl!!.eglCreateWindowSurface(mEglDisplay, configs[0], mSurfaceTexture, null)
            } else {
                //离屏渲染
                mEgl!!.eglCreatePbufferSurface(mEglDisplay, configs[0], surfaceAttribs)
            }
        if (mEglSurface === EGL10.EGL_NO_SURFACE || mEglContext === EGL10.EGL_NO_CONTEXT) {
            val error = mEgl!!.eglGetError()
            if (error == EGL10.EGL_BAD_NATIVE_WINDOW) {
                initEGLDone.invoke(false)
                throw RuntimeException("eglCreateWindowSurface returned  EGL_BAD_NATIVE_WINDOW. ")
            }
            initEGLDone.invoke(false)
            throw RuntimeException(
                "eglCreateWindowSurface failed : " +
                        GLUtils.getEGLErrorString(mEgl!!.eglGetError())
            )
        }
        if (!mEgl!!.eglMakeCurrent(mEglDisplay, mEglSurface, mEglSurface, mEglContext)) {
            initEGLDone.invoke(false)
            throw RuntimeException(
                "eglMakeCurrent failed : " +
                        GLUtils.getEGLErrorString(mEgl!!.eglGetError())
            )
        }
        mGL = mEglContext.getGL()
        initEGLDone.invoke(true)
    }

    fun destroyGL() {
        mEgl?.eglDestroyContext(mEglDisplay, mEglContext)
        mEgl?.eglDestroySurface(mEglDisplay, mEglSurface)
        mEglContext = EGL10.EGL_NO_CONTEXT
        mEglSurface = EGL10.EGL_NO_SURFACE
        quit()
    }

    fun swapBuffer() {
        mEgl?.eglWaitNative(EGL10.EGL_CORE_NATIVE_ENGINE, null)
        mEgl?.eglWaitGL()
        mEgl?.eglSwapBuffers(mEglDisplay, mEglSurface)
    }

    override fun run() {
        super.run()
//        while (mShouldRender != null && mShouldRender.get() !== false) {
//            mRenderer?.drawFrame()
//            mEgl!!.eglSwapBuffers(mEglDisplay, mEglSurface)
//            try {
//                sleep(5)
//            } catch (e: InterruptedException) {
//            }
//        }
//        destoryGL()
    }

    fun getCurrentContext():EGLContext{
        return mEglContext
    }

    companion object {
        private const val EGL_CONTEXT_CLIENT_VERSION = 0x3098
        private const val EGL_OPENGL_ES2_BIT = 4
    }
}
