package com.tapapk.edit_engine.core

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Matrix
import android.opengl.GLES20
import android.os.Handler
import android.util.Log
import com.tapapk.edit_engine.config.EffectsConfig
import com.tapapk.edit_engine.`interface`.EffectsListener
import com.tapapk.edit_engine.egl.GLThread
import com.tapapk.edit_engine.render.SnapshotRender
import com.tapapk.edit_engine.utils.EffectsUtils
import com.tapapk.edit_engine.utils.GLUtils
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * 这个类只在拍照和生成滤镜预览图用
 */
class EffectsCore {
    private var glHandler: Handler? = null
    var config: EffectsConfig? = null
    var listener: EffectsListener? = null
    var render: SnapshotRender? = null
    var glThread: GLThread? = null
    private var width: Int = 0
    private var height: Int = 0
    private var context: Context? = null
    fun createEffectsGL(context: Context?, width: Int, height: Int, created: () -> Unit) {
        this.width = width
        this.height = height
        this.context = context
        glThread = GLThread(null, null, width, height)
        glThread?.start()
        glHandler = Handler(glThread?.looper!!)
        glHandler?.post {
            glThread?.initEGL {
                if (it) {
                    created()
                }
            }
        }
    }

    fun process(bitmap: Bitmap) {
        glHandler?.post {
            var frameTexture = GLUtils.transformBitmapToTexture(bitmap)
            val modelMatrix = Matrix()
            modelMatrix.postTranslate(width / 2f, height / 2f)
            modelMatrix.postRotate(180f, width / 2f, height / 2f)
            modelMatrix.postScale(-1f, 1f, width / 2f, height / 2f)
            render = SnapshotRender(context!!, width, height)
            render?.setMatrix(modelMatrix)
            render?.calculateMatrix()
            render?.setFrameSize(width, height)
            render?.setFrameTexture(frameTexture)
            if (config?.lutEnable!!) {
                render?.enableLut(1)
                render?.setLut(config?.lut!!)
                render?.setLutCube(config?.lutCube?.toFloat()!!)
                render?.setLutIntensity(EffectsUtils.transLutIntensity(config?.lutLevel!!).toFloat())
            } else {
                render?.enableLut(0)
            }
            render?.setTexelOffset(EffectsUtils.transSoftSkin(config?.softSkin!!).toFloat())
            val beautyValue = EffectsUtils.transBeautyValue(config?.beauty!!)
            render?.setBeautyLevel(beautyValue.beautyLevel.toFloat())
            render?.setToneLevel(beautyValue.toneLevel.toFloat())
            render?.setBrightLevel(beautyValue.brightLevel.toFloat())
            render?.draw()
            glThread?.swapBuffer()
            val length = width * height * 4
            val pixelBuffer = ByteBuffer.allocateDirect(length)
            pixelBuffer.order(ByteOrder.LITTLE_ENDIAN)
            pixelBuffer.position(0)
            var x = 0
            var y = 0
//            when(config?.frameRatio){
//                CameraFrame.FRAME_4_3->{
//                    height = (width * (4f/3f)).toInt()
//                    x = 0
//                    y = 0
//                }
//                CameraFrame.FRAME_16_9->{
//                    height = (width * (16f/9f)).toInt()
//                    x = 0
//                    y = 0
//                }
//                CameraFrame.FRAME_1_1->{
//                    x = 0
//                    y = height/2 - width/2
//                    height = (width * (1f/1f)).toInt()
//                }
//            }
            GLES20.glReadPixels(
                0,
                0,
                width,
                height,
                GLES20.GL_RGBA,
                GLES20.GL_UNSIGNED_BYTE,
                pixelBuffer
            )
            Log.e("GL", "$width $height")
            val pictureBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            pictureBitmap.copyPixelsFromBuffer(pixelBuffer)
            if (frameTexture != 0){
                GLES20.glDeleteTextures(1, intArrayOf(frameTexture), 0)
                frameTexture = 0
            }
            listener?.processDone(pictureBitmap)
        }
    }

    fun release() {
        glHandler?.post {
            render?.destroy()
            glThread?.destroyGL()
            glThread?.quit()
        }
    }

}