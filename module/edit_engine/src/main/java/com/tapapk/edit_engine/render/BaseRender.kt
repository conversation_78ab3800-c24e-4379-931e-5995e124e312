package com.tapapk.edit_engine.render

import android.opengl.GLES20
import android.opengl.Matrix
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer

open class BaseRender {
    protected var left:Float = 0f
    protected var right:Float = 0f
    protected var bottom:Float = 0f
    protected var top:Float = 0f
    protected var frameWidth = 0f
    protected var frameHeight = 0f
    protected var portWidth = 0f
    protected var portHeight = 0f
    protected var viewWidth = 0f
    protected var viewHeight = 0f
    protected var ratio = 0f
    protected val mvpMatrix = getIdentity()
    protected var bitmapTexture = 0
    protected var modelMatrix = android.graphics.Matrix()
    private val viewMatrix = getIdentity()
    private val projectMatrix = getIdentity()
    protected lateinit var vertexBuffer:FloatBuffer
    protected lateinit var fragmentBuffer:FloatBuffer
    protected var xFlip = 0f
    protected var yFlip = 0f
    protected var orientationScale = 1f //旋转后的缩放，针对于原图纹理

    /**
     * 顶点坐标
     * (x,y,z)
     */
    private val POSITION_VERTEX = floatArrayOf(
        -1.0f, -1.0f,    //左上角坐标
        1.0f, -1.0f,     //右上角坐标
        -1.0f, 1.0f,     //左下角坐标
        1.0f, 1.0f    //右下角坐标
    )

    /**
     * 纹理坐标
     * (s,t)
     */
    private val FRAGMENT_VERTEX = floatArrayOf(
        0.0f, 1.0f,//左上角
        1.0f, 1.0f,//右上角
        0.0f, 0.0f,//左下角
        1.0f, 0.0f//右下角
    )
    protected open fun init(){
        //获取顶点和片元buffer
        vertexBuffer = getFloatBuffer(POSITION_VERTEX)
        fragmentBuffer = getFloatBuffer(FRAGMENT_VERTEX)
    }

    open fun portSizeChanged(portWidth: Float,  portHeight: Float){
        this.portWidth = portWidth
        this.portHeight = portHeight
    }
    open fun viewSizeChanged(viewWidth: Float,  viewHeight: Float){
        this.viewWidth = viewWidth
        this.viewHeight = viewHeight
    }
    open fun setMatrix(matrix: android.graphics.Matrix){
        modelMatrix = matrix
    }
    open fun calculatePosition(){
        ratio = viewWidth / viewHeight
        left = -1f
        right = 1f
        bottom = -1f
        top = 1f
        if (viewWidth / viewHeight >= frameWidth/frameHeight) {
            val frameRatio = viewHeight / frameHeight
            val frameGLWidth = normalizeWidth(frameWidth * frameRatio)
            val pLeft = normalizeTranslateX((viewWidth/2f - (frameWidth * frameRatio)/2f))
            val pRight = pLeft + frameGLWidth
            POSITION_VERTEX[0] = pLeft
            POSITION_VERTEX[1] = bottom
            POSITION_VERTEX[2] = pRight
            POSITION_VERTEX[3] = bottom
            POSITION_VERTEX[4] = pLeft
            POSITION_VERTEX[5] = top
            POSITION_VERTEX[6] = pRight
            POSITION_VERTEX[7] = top
        }else{
            val frameRatio = viewWidth / frameWidth
            val frameGLHeight = normalizeHeight(frameHeight * frameRatio)
            val pTop = -normalizeTranslateY(viewHeight/2f - (frameHeight * frameRatio)/2f)
            val pBottom = pTop + frameGLHeight
            POSITION_VERTEX[0] = left
            POSITION_VERTEX[1] = pTop
            POSITION_VERTEX[2] = right
            POSITION_VERTEX[3] = pTop
            POSITION_VERTEX[4] = left
            POSITION_VERTEX[5] = pBottom
            POSITION_VERTEX[6] = right
            POSITION_VERTEX[7] = pBottom
        }
        vertexBuffer = getFloatBuffer(POSITION_VERTEX)

        // 摄像机矩阵计算
        Matrix.setLookAtM(viewMatrix,
            0,
            0f, 0f, 1f,
            0f, 0f, 0f,
            0f, 1f, 0f
        )
        // 透视投影矩阵计算
        Matrix.orthoM(
            projectMatrix,
            0,
            left, right, bottom, top,
            1f,
            5f
        )
        // MVP矩阵计算
        Matrix.multiplyMM(mvpMatrix, 0, projectMatrix, 0, viewMatrix, 0)
    }
    protected open fun draw(){
//        GLES20.glViewport(PortUtils.centerX(viewWidth,viewWidth).toInt(), PortUtils.centerY(viewHeight,viewHeight).toInt(), viewWidth.toInt(), viewHeight.toInt())
        GLES20.glViewport(0, 0, viewWidth.toInt(), viewHeight.toInt())
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT or GLES20.GL_DEPTH_BUFFER_BIT)
//        GLES20.glClearColor(1.0f,1.0f,1.0f,1.0f)
//        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0)
//        GLES20.glEnableVertexAttribArray(GLES20.glGetAttribLocation(program, "vPosition"))
//        GLES20.glVertexAttribPointer(GLES20.glGetAttribLocation(program, "vPosition"), 2, GLES20.GL_FLOAT, false, 0, vertexBuffer)
//        GLES20.glEnableVertexAttribArray(GLES20.glGetAttribLocation(program, "vCoordinate"))
//        GLES20.glVertexAttribPointer(GLES20.glGetAttribLocation(program, "vCoordinate"), 2, GLES20.GL_FLOAT, false, 0, fragmentBuffer)
    }
    fun getIdentity(): FloatArray {
        return floatArrayOf(
            1f, 0f, 0f, 0f,
            0f, 1f, 0f, 0f,
            0f, 0f, 1f, 0f,
            0f, 0f, 0f, 1f
        )
    }
    open fun transform(sourceMatrix: FloatArray, destinationMatrix: FloatArray, matrix:android.graphics.Matrix) {
        //获取矩阵数组
        val v = FloatArray(9)
        matrix.getValues(v)
        //x,y轴归一化
        v[2] = normalizeTranslateX(v[2])
        v[5] = normalizeTranslateY(v[5])

        //3x3矩阵转4x4
        val openGLMatrix = floatArrayOf(
            v[0], v[1], 0f, 0f,
            v[3], v[4], 0f, v[2],
            0f, 0f, 1f, v[5],
            0f, 0f, 0f, 1f
        )
        val scaleGLWidth = v[0] * normalizeWidth(viewWidth)
        val scaleGLHeight = v[4] * normalizeHeight(viewHeight)
//        Log.e("GLX","${(scaleGLWidth - normalizeWidth(viewWidth))/2}")
//        Log.e("GLY", "${(scaleGLHeight - normalizeHeight(viewHeight))/2}")
//        Log.e("tX","${v[2]}")
//        Log.e("tY","${v[5]}")

        if (openGLMatrix[12] > Math.abs((scaleGLWidth - normalizeWidth(viewWidth))/2)){
            openGLMatrix[12] = Math.abs((scaleGLWidth - normalizeWidth(viewWidth))/2)
        }
        if (openGLMatrix[13] > Math.abs((scaleGLHeight - normalizeHeight(viewHeight))/2)){
            openGLMatrix[13] = Math.abs((scaleGLHeight - normalizeHeight(viewHeight))/2)
        }
        if (openGLMatrix[12] < (scaleGLWidth - normalizeWidth(viewWidth))/2){
            openGLMatrix[12] = (scaleGLWidth - normalizeWidth(viewWidth))/2
        }
        if (openGLMatrix[13] < (scaleGLHeight - normalizeHeight(viewHeight))/2){
            openGLMatrix[13] = (scaleGLHeight - normalizeHeight(viewHeight))/2
        }
        Matrix.scaleM(openGLMatrix,0,orientationScale,orientationScale,1f)
        Matrix.rotateM(openGLMatrix,0,xFlip,1f,0f,0f)
        Matrix.rotateM(openGLMatrix,0,yFlip,0f,1f,0f)
        Matrix.multiplyMM(destinationMatrix, 0, sourceMatrix, 0, openGLMatrix, 0)
    }
    private fun normalizeTranslateX(x:Float):Float{
        val translateX = if (x < viewWidth / 2f) {
            -1f + (x / (viewWidth / 2f))
        } else {
            (x - (viewWidth / 2f)) / (viewWidth / 2f)
        }

        return translateX
    }
    private fun normalizeTranslateY(y:Float):Float{
        val translateY = if (y < viewHeight / 2f) {
            1f - (y / (viewHeight / 2f))
        } else {
            -(y - (viewHeight / 2f)) / (viewHeight / 2f)
        }

        return translateY
    }
    private fun normalizeWidth(width: Float, isScalable: Boolean = true): Float {
        var openGLWidth = (width / viewWidth) * 2
        return openGLWidth
    }

    private fun normalizeHeight(height: Float, isScalable: Boolean = true): Float {
        var openGLHeight = (height / viewHeight) * 2
        return openGLHeight
    }
    /**
     * @param floatArray
     */
    private fun getFloatBuffer(floatArray: FloatArray): FloatBuffer {
        val vbb = ByteBuffer.allocateDirect(floatArray.size * 4)
        vbb.order(ByteOrder.nativeOrder())                //设置字节顺序
        val vertexBuf = vbb.asFloatBuffer()     //转换为Float型缓冲
        vertexBuf.put(floatArray)                 //向缓冲区中放入顶点坐标数据
        vertexBuf.position(0)        //设置缓冲区起始位置
        return vertexBuf
    }
    protected open fun destroy(){
        if(bitmapTexture != 0){
            GLES20.glDeleteTextures(1, intArrayOf(bitmapTexture), 0)
            bitmapTexture = 0
        }
    }
}