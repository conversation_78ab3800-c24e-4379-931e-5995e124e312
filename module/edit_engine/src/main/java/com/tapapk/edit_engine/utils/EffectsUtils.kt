package com.tapapk.edit_engine.utils

import android.util.Log

object EffectsUtils {
    val minStepOffset = 0f//-10f
    val maxStepOffset = 1.5f//10f
    val minToneValue = -5f
    val maxToneValue = 5f
    val minBeautyValue = 0f
    val maxBeautyValue = 1.0f
    val minBrightValue = 0f
    val maxBrightValue = 1f
    val minLutIntensity = 0f
    val maxLutIntensity = 1f
    data class BeautyValue(
        var toneLevel:Double = 0.0,
        var beautyLevel:Double = 0.0,
        var brightLevel:Double = 0.0
    )
    fun transBeautyValue(value:Int): BeautyValue {
        val beautyValue = range(value, minBeautyValue, maxBeautyValue)
        val toneValue = (0.5/ maxBeautyValue)*beautyValue
        val brightValue = ((0.1/ maxBeautyValue) * beautyValue) + 0.47
        return BeautyValue(toneValue,beautyValue,brightValue);
    }
    fun transSoftSkin(value:Int):Double{
        return range(value, minStepOffset, maxStepOffset)
    }
    fun transLutIntensity(value: Int):Double{
        return range(value, minLutIntensity, maxLutIntensity)
    }
    private fun range(percentage:Int, start:Float, end:Float):Double{
        Log.e("range","${(end - start) * percentage / 1000.0 + start}")
        return (end - start) * percentage / 1000.0 + start
    }
}