package com.tapapk.edit_engine.core

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Point
import android.graphics.SurfaceTexture
import android.opengl.GLES11Ext
import android.opengl.GLES20
import android.os.Handler
import android.os.Looper
import com.tapapk.edit_engine.utils.EffectsUtils
import com.tapapk.edit_engine.utils.GLUtils
import com.tapapk.edit_engine.`interface`.GLCreateListener
import com.tapapk.edit_engine.egl.GLThread
import com.tapapk.edit_engine.render.EffectsRender
import it.sephiroth.android.library.exif2.ExifInterface
import javax.microedition.khronos.egl.EGLContext

class EditEngineCore {
    private var context: Context? = null
    private var glThread: GLThread? = null
    private var glHandler: Handler? = null
    private var mainHandler: Handler? = null
    private var editTextureId = 0
    private var viewWidth = 0
    private var viewHeight = 0
    private var effectsRender:EffectsRender? = null
    fun create(
        context: Context,
        surface: SurfaceTexture,
        width: Int,
        height: Int,
        glCreateListener: GLCreateListener?
    ) {
        this.context = context
        createGLEnvironment(surface, null, width, height, glCreateListener)
    }

    private fun createGLEnvironment(
        surface: SurfaceTexture?,
        eglSharedContext: EGLContext?,
        width: Int,
        height: Int,
        glCreateListener: GLCreateListener?
    ) {
        glThread = GLThread(surface, eglSharedContext, width, height)
        glThread?.start()
        if (glThread == null) {
            glCreateListener?.error()
            return
        }
        viewWidth = width
        viewHeight = height
        glHandler = Handler(glThread!!.looper)
        mainHandler = Handler(Looper.getMainLooper())
        glHandler?.post {
            glThread?.initEGL {
                if (it) {
                    effectsRender = EffectsRender(context,viewWidth,viewHeight)
                    mainHandler?.post {
                        glCreateListener?.success()
                    }
                } else {
                    mainHandler?.post {
                        glCreateListener?.error()
                    }
                }
            }
        }
    }

    fun setMainTexture(byteArray: ByteArray){
        glHandler?.post {
            val bitmap = BitmapFactory.decodeByteArray(byteArray,0,byteArray.size)
            val textureID = GLUtils.transformBitmapToTexture(bitmap)
            editTextureId = textureID
            effectsRender?.setFrameSize(bitmap.width,bitmap.height)
            effectsRender?.orgTexture = editTextureId
            render()
        }
    }

    fun getOutTexture():Int{
        if(effectsRender == null){
            return 0
        }else{
            return effectsRender!!.outTexture
        }
    }

    fun setEnableLut(boolean: Boolean) {
        if (boolean) {
            effectsRender?.enableLut(1)
        } else {
            effectsRender?.enableLut(0)
        }
    }

    fun setSticker(bitmap: Bitmap){
        glHandler?.post {
            effectsRender?.setSticker(bitmap)
            render()
        }
    }
    fun setLut(context: Context?, resId: Int, cube: Float) {
        glHandler?.post {
            effectsRender?.setLut(context, resId)
            effectsRender?.setLutCube(cube)
        }
    }

    fun setLut(bitmap: Bitmap, cube: Float) {
        glHandler?.post {
            effectsRender?.setLut(bitmap)
            effectsRender?.setLutCube(cube)
        }
    }

    fun setLutIntensity(value: Int) {
        effectsRender?.setLutIntensity(EffectsUtils.transLutIntensity(value).toFloat())
    }


    fun outBitmap(width: Int,height: Int,callback:(bitmap:Bitmap)->Unit){
        glHandler?.post {
            effectsRender?.createBufferAndOutTexture(width,height)
            effectsRender?.drawFrameBuffer(width,height)
            val imageBitmap = GLUtils.texture2DToBitmap(getOutTexture(), Point(width,height))
            mainHandler?.post {
                callback(imageBitmap)
            }
        }
    }

    fun render(){
        glHandler?.post {
            effectsRender?.draw()
            glThread?.swapBuffer()
        }
    }

    fun release(){
        glHandler?.post {
            if(editTextureId != 0){
                GLES20.glDeleteTextures(1, intArrayOf(editTextureId), 0)
                editTextureId = 0
            }
            effectsRender?.destroy()
            glThread?.destroyGL()
            glThread = null
        }
    }
}