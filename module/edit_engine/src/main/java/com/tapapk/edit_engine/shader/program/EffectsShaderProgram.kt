package com.tapapk.edit_engine.shader.program

import android.content.Context
import android.opengl.GLES20
import com.tapapk.edit_engine.shader.ShaderProgram
import online.yllh.smartinspect.edit.R

class EffectsShaderProgram(context: Context?) :
    ShaderProgram(context, R.raw.vertex_edit, R.raw.fragment_edit_effects) {
    var vTextureOriginLocation = -1
    var uMvpMatrixLocation = -1

    // Attribute locations
    var aPositionLocation = -1
    var aTextureCoordinateLocation = -1
    var paramsLocation = -1
    var brightnessLocation = -1
    var singleStepOffsetLocation = -1
    var texelWidthLocation = -1
    var texelHeightLocation = -1
    var enableLutLocation = -1
    var hidePortLocation = -1
    var textureLutLocation = -1
    var lutIntensityLocation = -1
    var lutCubeLocation = -1
    var vTextureStickerLocation = -1

    init {
        // Retrieve attribute locations for the shader program.
        aPositionLocation = GLES20.glGetAttribLocation(programId, "a_Position")
        aTextureCoordinateLocation = GLES20.glGetAttribLocation(programId, "a_TextureCoordinate")

        // Retrieve uniform locations for the shader program
        vTextureOriginLocation = GLES20.glGetUniformLocation(programId, "vTextureOrigin")
        textureLutLocation = GLES20.glGetUniformLocation(programId, "vTextureLut")

        uMvpMatrixLocation = GLES20.glGetUniformLocation(programId, "u_MvpMatrix")

        paramsLocation = GLES20.glGetUniformLocation(programId, "params")
        brightnessLocation = GLES20.glGetUniformLocation(programId, "brightness")
        singleStepOffsetLocation = GLES20.glGetUniformLocation(programId, "singleStepOffset")
        texelWidthLocation = GLES20.glGetUniformLocation(programId, "texelWidthOffset")
        texelHeightLocation = GLES20.glGetUniformLocation(programId, "texelHeightOffset")
        enableLutLocation = GLES20.glGetUniformLocation(programId,"enableLut")
        hidePortLocation = GLES20.glGetUniformLocation(programId,"hidePort")
        lutIntensityLocation = GLES20.glGetUniformLocation(programId,"lut_intensity")
        lutCubeLocation = GLES20.glGetUniformLocation(programId,"lutCube")
        vTextureStickerLocation = GLES20.glGetUniformLocation(programId,"vTextureSticker")

    }

    fun setUniform(mvpMatrix: FloatArray, textureId: Int,lutTexture:Int, vTextureSticker:Int) {
        GLES20.glUniformMatrix4fv(uMvpMatrixLocation, 1, false, mvpMatrix, 0)

        GLES20.glActiveTexture(GLES20.GL_TEXTURE0)
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, textureId)
        GLES20.glUniform1i(vTextureOriginLocation, 0)

        GLES20.glActiveTexture(GLES20.GL_TEXTURE1)
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, lutTexture)
        GLES20.glUniform1i(textureLutLocation,1)

        GLES20.glActiveTexture(GLES20.GL_TEXTURE2)
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, vTextureSticker)
        GLES20.glUniform1i(vTextureStickerLocation,2)
    }
}
