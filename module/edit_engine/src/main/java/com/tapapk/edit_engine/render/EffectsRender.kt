package com.tapapk.edit_engine.render

import android.content.Context
import android.graphics.Bitmap
import android.opengl.GLES11Ext
import android.opengl.GLES20
import com.tapapk.edit_engine.utils.GLUtils
import com.tapapk.edit_engine.shader.program.EffectsShaderProgram
import java.nio.FloatBuffer

class EffectsRender(context: Context?, width:Int, height:Int): BaseRender() {
    var effectsProgram = EffectsShaderProgram(context)
    var textureMatrix = getIdentity()
    var vTextureSticker = 0
    var orgTexture = 0
    var lutTexture = 0
    var frameBuffer = 0
    var outTexture = 0

    private var toneLevel:Float = 0f
    private var beautyLevel:Float = 0f
    private var brightLevel:Float = 0f
    private var texelWidthOffset:Float = 0f
    private var texelHeightOffset:Float = 0f
    private var isEnableLut = 0
    private var isHidePort = 0
    private var lutIntensity = 0f
    private var lutCube = 4f
    init {
        texelWidthOffset=0f
        texelHeightOffset=0f
        toneLevel = 0.0f//-0.5f
        beautyLevel = 0.0f//1.2f
        brightLevel = 0.47f//0.47f
        viewWidth = width.toFloat()
        viewHeight = height.toFloat()
        portWidth = width.toFloat()
        portHeight = height.toFloat()
        init()
    }

    fun createBufferAndOutTexture(width: Int,height: Int){
        val frameBuffers = IntArray(1)
        GLES20.glGenFramebuffers(1, frameBuffers, 0)
        frameBuffer = frameBuffers[0]
        outTexture = GLUtils.createEmptyTexture2D(width,height)
    }

    public override fun draw() {
        super.draw()
        effectsProgram.useProgram()
        effectsProgram.setUniform(textureMatrix,orgTexture,lutTexture,vTextureSticker)
        setTexelSize(viewWidth,viewHeight)
        setParams(beautyLevel, toneLevel)
        setBrightLevel(brightLevel)
        setTexelOffset(texelWidthOffset)
        setLutIntensity(lutIntensity)
        enableLut(isEnableLut)
        hidePort(isHidePort)
        GLES20.glEnableVertexAttribArray(effectsProgram.aPositionLocation)
        GLES20.glVertexAttribPointer(effectsProgram.aPositionLocation, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer)
        GLES20.glEnableVertexAttribArray(effectsProgram.aTextureCoordinateLocation)
        GLES20.glVertexAttribPointer(effectsProgram.aTextureCoordinateLocation, 2, GLES20.GL_FLOAT, false, 0, fragmentBuffer)
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4)
        GLES20.glDisableVertexAttribArray(effectsProgram.aPositionLocation)
        GLES20.glDisableVertexAttribArray(effectsProgram.aTextureCoordinateLocation)
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, 0)
    }
    fun calculateMatrix(){
        transform(mvpMatrix,this.textureMatrix,modelMatrix)
    }

    fun drawFrameBuffer(width: Int ,height: Int){
        GLES20.glViewport(0, 0, width, height)
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT or GLES20.GL_DEPTH_BUFFER_BIT)
        GLES20.glClearColor(0.0f,0.0f,0.0f,1.0f)
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, frameBuffer)
        GLES20.glFramebufferTexture2D(GLES20.GL_FRAMEBUFFER, GLES20.GL_COLOR_ATTACHMENT0, GLES20.GL_TEXTURE_2D, outTexture, 0)
        effectsProgram.useProgram()
        effectsProgram.setUniform(textureMatrix,orgTexture,lutTexture,vTextureSticker)
        setTexelSize(viewWidth,viewHeight)
        setParams(beautyLevel, toneLevel)
        setBrightLevel(brightLevel)
        setTexelOffset(texelWidthOffset)
        setLutIntensity(lutIntensity)
        enableLut(isEnableLut)
        hidePort(isHidePort)
        GLES20.glEnableVertexAttribArray(effectsProgram.aPositionLocation)
        GLES20.glVertexAttribPointer(effectsProgram.aPositionLocation, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer)
        GLES20.glEnableVertexAttribArray(effectsProgram.aTextureCoordinateLocation)
        GLES20.glVertexAttribPointer(effectsProgram.aTextureCoordinateLocation, 2, GLES20.GL_FLOAT, false, 0, fragmentBuffer)
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4)
        GLES20.glDisableVertexAttribArray(effectsProgram.aPositionLocation)
        GLES20.glDisableVertexAttribArray(effectsProgram.aTextureCoordinateLocation)
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, 0)
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0)
    }

    fun setFrameSize(width: Int,height: Int){
        frameWidth = width.toFloat()
        frameHeight = height.toFloat()
        viewSizeChanged(viewWidth,viewHeight)
//        calculateMatrix()
    }
    fun setLut(context: Context?,resId:Int){
        if (lutTexture != 0) {
            GLES20.glDeleteTextures(1, intArrayOf(lutTexture), 0)
            lutTexture = 0
        }
        lutTexture = GLUtils.transformBitmapToTexture(context,resId)
    }
    fun setLut(bitmap: Bitmap){
        if (lutTexture != 0) {
            GLES20.glDeleteTextures(1, intArrayOf(lutTexture), 0)
            lutTexture = 0
        }
        lutTexture = GLUtils.transformBitmapToTexture(bitmap)
    }
    fun setLutCube(cube:Float){
        this.lutCube = cube
        setFloat(effectsProgram.lutCubeLocation, lutCube)
    }
    fun setSticker(bitmap: Bitmap){
        if (vTextureSticker != 0) {
            GLES20.glDeleteTextures(1, intArrayOf(vTextureSticker), 0)
            vTextureSticker = 0
        }
        vTextureSticker = GLUtils.transformBitmapToTexture(bitmap)
    }
    fun clear(){
        GLES20.glClearColor(0.0f,0.0f,0.0f,1.0f)
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT or GLES20.GL_DEPTH_BUFFER_BIT)
    }
    override fun viewSizeChanged(viewWidth: Float, viewHeight: Float) {
        super.viewSizeChanged(viewWidth, viewHeight)
        if (viewHeight == viewWidth){
            val tmp = frameWidth
            frameWidth = frameHeight
            frameHeight = tmp
        }
        calculatePosition()
    }

    fun setTexelOffset(texelOffset: Float) {
        texelHeightOffset = texelOffset
        texelWidthOffset = texelHeightOffset
        setFloat(effectsProgram.texelWidthLocation, texelOffset / viewWidth)
        setFloat(effectsProgram.texelHeightLocation, texelOffset / viewHeight)
    }

    fun setToneLevel(toneLeve: Float) {
        toneLevel = toneLeve
        setParams(beautyLevel, toneLevel)
    }

    fun setBeautyLevel(beautyLeve: Float) {
        beautyLevel = beautyLeve
        setParams(beautyLevel, toneLevel)
    }

    fun setBrightLevel(brightLevel: Float) {
        this.brightLevel = brightLevel
        setFloat(effectsProgram.brightnessLocation, 0.6f * (-0.5f + brightLevel))
    }

    fun setLutIntensity(lutIntensity:Float){
        this.lutIntensity = lutIntensity
        setFloat(effectsProgram.lutIntensityLocation, lutIntensity)
    }

    fun enableLut(intValue:Int){
        this.isEnableLut = intValue
        setInt(effectsProgram.enableLutLocation,isEnableLut)
    }

    fun hidePort(intValue: Int){
        this.isHidePort = intValue
        setInt(effectsProgram.hidePortLocation,isHidePort)
    }

    fun setParams(beauty: Float, tone: Float) {
        beautyLevel = beauty
        toneLevel = tone
        val vector = FloatArray(4)
        vector[0] = 1.0f - 0.6f * beauty
        vector[1] = 1.0f - 0.3f * beauty
        vector[2] = 0.1f + 0.3f * tone
        vector[3] = 0.1f + 0.3f * tone
        setFloatVec4(effectsProgram.paramsLocation, vector)
    }

    private fun setTexelSize(w: Float, h: Float) {
        setFloatVec2(effectsProgram.singleStepOffsetLocation, floatArrayOf(2.0f / w, 2.0f / h))
    }

    private fun setFloat(location: Int, floatValue: Float) {
        GLES20.glUniform1f(location, floatValue)
    }

    private fun setInt(location: Int,intValue:Int){
        GLES20.glUniform1i(location,intValue)
    }

    private fun setFloatVec2(location: Int, arrayValue: FloatArray?) {
        GLES20.glUniform2fv(location, 1, FloatBuffer.wrap(arrayValue))
    }

    private fun setFloatVec3(location: Int, arrayValue: FloatArray?) {
        GLES20.glUniform3fv(location, 1, FloatBuffer.wrap(arrayValue))
    }

    private fun setFloatVec4(location: Int, arrayValue: FloatArray?) {
        GLES20.glUniform4fv(location, 1, FloatBuffer.wrap(arrayValue))
    }

    public override fun destroy() {
        super.destroy()
        effectsProgram.deleteProgram()
        if (lutTexture != 0) {
            GLES20.glDeleteTextures(1, intArrayOf(lutTexture), 0)
            lutTexture = 0
        }
        if (orgTexture != 0){
            GLES20.glDeleteTextures(1, intArrayOf(orgTexture), 0)
            orgTexture = 0
        }
        if (outTexture != 0){
            GLES20.glDeleteTextures(1, intArrayOf(outTexture), 0)
            outTexture = 0
        }
        if (frameBuffer != 0){
            GLES20.glDeleteBuffers(1, intArrayOf(frameBuffer), 0)
            frameBuffer = 0
        }
    }
}