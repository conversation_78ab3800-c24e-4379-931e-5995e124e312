package com.tapapk.edit_engine.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Point
import android.opengl.GLES11Ext
import android.opengl.GLES20
import android.opengl.GLUtils
import android.util.Log
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.text.DecimalFormat


object GLUtils {
    fun createOESEmptyTexture2D(): Int {
        val textures = IntArray(1)
        GLES20.glGenTextures(1, textures, 0)
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textures[0])
        GLES20.glTexParameterf(
            GLES11Ext.GL_TEXTURE_EXTERNAL_OES,
            GLES20.GL_TEXTURE_MIN_FILTER,
            GLES20.GL_LINEAR.toFloat()
        )
        GLES20.glTexParameterf(
            GLES11Ext.GL_TEXTURE_EXTERNAL_OES,
            GLES20.GL_TEXTURE_MAG_FILTER,
            GLES20.GL_LINEAR.toFloat()
        )
        GLES20.glTexParameteri(
            GLES11Ext.GL_TEXTURE_EXTERNAL_OES,
            GLES20.GL_TEXTURE_WRAP_S,
            GLES20.GL_CLAMP_TO_EDGE
        )
        GLES20.glTexParameteri(
            GLES11Ext.GL_TEXTURE_EXTERNAL_OES,
            GLES20.GL_TEXTURE_WRAP_T,
            GLES20.GL_CLAMP_TO_EDGE
        )
        return textures[0]
    }
    fun createEmptyTexture2D(width: Int, height: Int): Int {
        var textureId = IntArray(1)
        GLES20.glGenTextures(1, textureId, 0)
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, textureId[0])
        GLES20.glTexParameterf(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_MIN_FILTER,
            GLES20.GL_LINEAR.toFloat()
        )
        GLES20.glTexParameterf(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_MAG_FILTER,
            GLES20.GL_LINEAR.toFloat()
        )
        GLES20.glTexParameterf(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_WRAP_S,
            GLES20.GL_CLAMP_TO_EDGE.toFloat()
        )
        GLES20.glTexParameterf(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_WRAP_T,
            GLES20.GL_CLAMP_TO_EDGE.toFloat()
        )
        GLES20.glTexImage2D(
            GLES20.GL_TEXTURE_2D,
            0,
            GLES20.GL_RGBA,
            width,
            height,
            0,
            GLES20.GL_RGBA,
            GLES20.GL_UNSIGNED_BYTE,
            null
        )
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, 0)
        return textureId[0]
    }
    fun transformRgbaBytesArrayToTexture(rgbaBytes: ByteArray, width: Int, height: Int): Int {
        val byteBuffer = ByteBuffer.wrap(rgbaBytes)
        byteBuffer.rewind()
        var textureId = createEmptyTexture2D(width, height)
        if (textureId <= 0) {
            return 0
        }
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, textureId)
        GLES20.glTexImage2D(
            GLES20.GL_TEXTURE_2D,
            0,
            GLES20.GL_RGBA,
            width,
            height,
            0,
            GLES20.GL_RGBA,
            GLES20.GL_UNSIGNED_BYTE,
            byteBuffer
        )
        return textureId
    }
    fun transformBitmapToTexture(bitmap: Bitmap): Int {
        val textureId = IntArray(1)
        GLES20.glGenTextures(1, textureId, 0)
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, textureId[0])
        GLES20.glTexParameterf(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_MIN_FILTER,
            GLES20.GL_NEAREST.toFloat()
        )
        GLES20.glTexParameterf(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_MAG_FILTER,
            GLES20.GL_LINEAR.toFloat()
        )
        GLES20.glTexParameterf(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_WRAP_S,
            GLES20.GL_CLAMP_TO_EDGE.toFloat()
        )
        GLES20.glTexParameterf(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_WRAP_T,
            GLES20.GL_CLAMP_TO_EDGE.toFloat()
        )
        //根据以上指定的参数，生成一个2D纹理
        GLUtils.texImage2D(GLES20.GL_TEXTURE_2D, 0, bitmap, 0)
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, 0)
        return textureId[0]
    }
    fun transformBitmapToTexture(context: Context?, resourceId: Int): Int {
        val options = BitmapFactory.Options()
        options.inScaled = false
        val bitmap = BitmapFactory.decodeResource(
            context?.getResources(), resourceId, options
        )
        val textureId = IntArray(1)
        GLES20.glGenTextures(1, textureId, 0)
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, textureId[0])
        GLES20.glTexParameterf(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_MIN_FILTER,
            GLES20.GL_NEAREST.toFloat()
        )
        GLES20.glTexParameterf(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_MAG_FILTER,
            GLES20.GL_LINEAR.toFloat()
        )
        GLES20.glTexParameterf(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_WRAP_S,
            GLES20.GL_CLAMP_TO_EDGE.toFloat()
        )
        GLES20.glTexParameterf(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_WRAP_T,
            GLES20.GL_CLAMP_TO_EDGE.toFloat()
        )
        //根据以上指定的参数，生成一个2D纹理
        GLUtils.texImage2D(GLES20.GL_TEXTURE_2D, 0, bitmap, 0)
        bitmap.recycle()
        //解绑当前纹理，防止其他地方以外改变该纹理
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, 0)
        return textureId[0]
    }
    fun texture2DToJpg(textureId: Int, size: Point): ByteArray {
        var frameBuffer = -1

        val curBuf = IntArray(1)
        GLES20.glGetIntegerv(GLES20.GL_FRAMEBUFFER_BINDING, curBuf, 0)

        if (!GLES20.glIsFramebuffer(frameBuffer)) {
            val values = IntArray(1)
            GLES20.glGenFramebuffers(1, values, 0)
            frameBuffer = values[0]    // expected > 0
        }
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, frameBuffer)

        GLES20.glFramebufferTexture2D(
            GLES20.GL_FRAMEBUFFER, GLES20.GL_COLOR_ATTACHMENT0,
            GLES20.GL_TEXTURE_2D, textureId, 0
        )

        val width = size.x
        val height = size.y

        val length = width * height * 4
        val pixelBuffer = ByteBuffer.allocateDirect(length)
        pixelBuffer.order(ByteOrder.LITTLE_ENDIAN)
        pixelBuffer.position(0)
        GLES20.glReadPixels(
            0,
            0,
            width,
            height,
            GLES20.GL_RGBA,
            GLES20.GL_UNSIGNED_BYTE,
            pixelBuffer
        )

        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        bitmap.copyPixelsFromBuffer(pixelBuffer)

        val byteArrayOutputStream = ByteArrayOutputStream(16 * 1024)
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream)
        val jpg = byteArrayOutputStream.toByteArray()

        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, curBuf[0])

        return jpg
    }
    fun texture2DToBitmap(textureId: Int, size: Point): Bitmap {
        var frameBuffer = -1

        val curBuf = IntArray(1)
        GLES20.glGetIntegerv(GLES20.GL_FRAMEBUFFER_BINDING, curBuf, 0)

        if (!GLES20.glIsFramebuffer(frameBuffer)) {
            val values = IntArray(1)
            GLES20.glGenFramebuffers(1, values, 0)
            frameBuffer = values[0]    // expected > 0
        }
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, frameBuffer)

        GLES20.glFramebufferTexture2D(
            GLES20.GL_FRAMEBUFFER, GLES20.GL_COLOR_ATTACHMENT0,
            GLES20.GL_TEXTURE_2D, textureId, 0
        )

        val width = size.x
        val height = size.y

        val length = width * height * 4
        val pixelBuffer = ByteBuffer.allocateDirect(length)
        pixelBuffer.order(ByteOrder.LITTLE_ENDIAN)
        pixelBuffer.position(0)
        GLES20.glReadPixels(
            0,
            0,
            width,
            height,
            GLES20.GL_RGBA,
            GLES20.GL_UNSIGNED_BYTE,
            pixelBuffer
        )

        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        bitmap.copyPixelsFromBuffer(pixelBuffer)

        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, curBuf[0])

        return bitmap
    }
    fun texture2DToRGBA(textureId: Int, size: Point): ByteArray {
        var frameBuffer = -1

        val curBuf = IntArray(1)
        GLES20.glGetIntegerv(GLES20.GL_FRAMEBUFFER_BINDING, curBuf, 0)

        if (!GLES20.glIsFramebuffer(frameBuffer)) {
            val values = IntArray(1)
            GLES20.glGenFramebuffers(1, values, 0)
            frameBuffer = values[0]    // expected > 0
        }
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, frameBuffer)

        GLES20.glFramebufferTexture2D(
            GLES20.GL_FRAMEBUFFER, GLES20.GL_COLOR_ATTACHMENT0,
            GLES20.GL_TEXTURE_2D, textureId, 0
        )

        val width = size.x
        val height = size.y

        val length = width * height * 4
        val pixelBuffer = ByteBuffer.allocateDirect(length)
        pixelBuffer.order(ByteOrder.LITTLE_ENDIAN)
        pixelBuffer.position(0)
        GLES20.glReadPixels(
            0,
            0,
            width,
            height,
            GLES20.GL_RGBA,
            GLES20.GL_UNSIGNED_BYTE,
            pixelBuffer
        )
        val pixelBytes = ByteArray(4*width*height)
        pixelBuffer.get(pixelBytes)

        return pixelBytes
    }
    fun readGLToRGBA(x: Int, y: Int, width: Int, height: Int): ByteArray {

        val length = width * height * 4
        val pixelBuffer = ByteBuffer.allocateDirect(length)
        pixelBuffer.order(ByteOrder.LITTLE_ENDIAN)
        pixelBuffer.position(0)
        GLES20.glReadPixels(
            x,
            y,
            width,
            height,
            GLES20.GL_RGBA,
            GLES20.GL_UNSIGNED_BYTE,
            pixelBuffer
        )
        val pixelBytes = ByteArray(4 * width * height)
        pixelBuffer.get(pixelBytes)
        return pixelBytes
    }
    fun readGLToBitmap(x: Int, y: Int, width: Int, height: Int): Bitmap {

        val length = width * height * 4
        val pixelBuffer = ByteBuffer.allocateDirect(length)
        pixelBuffer.order(ByteOrder.LITTLE_ENDIAN)
        pixelBuffer.position(0)
        GLES20.glReadPixels(
            x,
            y,
            width,
            height,
            GLES20.GL_RGBA,
            GLES20.GL_UNSIGNED_BYTE,
            pixelBuffer
        )
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        bitmap.copyPixelsFromBuffer(pixelBuffer)

        return bitmap
    }
    fun checkGlError(op: String) {
        var error: Int
        while (GLES20.glGetError().also { error = it } != GLES20.GL_NO_ERROR) {
            Log.e("OpenGL", "$op: glError $error")
            throw RuntimeException("$op: glError $error")
        }
    }
    fun matrixLog(matrix:FloatArray,name:String,line:Int){
        val log = StringBuffer()
        for (i in matrix.indices){
            if (i % line == 0) {
                log.append(" \n")
            }
            log.append("${roundByScale(matrix[i].toDouble(),4)}\t")
        }
        Log.e("matrixLog ${name}",log.toString())
    }

    private fun roundByScale(v: Double, scale: Int): String? {
        require(scale >= 0) { "The   scale   must   be   a   positive   integer   or   zero" }
        if (scale == 0) {
            return DecimalFormat("0").format(v)
        }
        var formatStr = "0."
        for (i in 0 until scale) {
            formatStr = formatStr + "0"
        }
        return DecimalFormat(formatStr).format(v)
    }
}