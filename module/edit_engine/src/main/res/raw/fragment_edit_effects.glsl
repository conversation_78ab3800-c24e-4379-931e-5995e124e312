#version 100
precision lowp float;
//国内部分手机跑shader必须设置精度
varying lowp vec2 vTextureCoord;

uniform sampler2D vTextureOrigin;
uniform sampler2D vTextureLut;
uniform sampler2D vTextureSticker; // 贴纸的纹理

uniform bool enableLut;
uniform bool hidePort;
uniform lowp float lut_intensity;
uniform lowp float lut_position_offset;

uniform lowp vec2 singleStepOffset;
uniform lowp vec4 params;
uniform lowp float brightness;
uniform float texelWidthOffset;
uniform float texelHeightOffset;
uniform float lutCube;

vec4 lookup(in vec4 textureColor){
    mediump float blueColor = textureColor.b * (pow(lutCube,2.0)-1.0);
    mediump vec2 quad1;
    quad1.y = floor(floor(blueColor) / lutCube);
    quad1.x = floor(blueColor) - (quad1.y * lutCube);
    mediump vec2 quad2;
    quad2.y = floor(ceil(blueColor) / lutCube);
    quad2.x = ceil(blueColor) - (quad2.y * lutCube);
    lowp vec2 texPos1;
    texPos1.x = (quad1.x * 1.0/lutCube) + 0.5/pow(lutCube,3.0) + ((1.0/lutCube - 1.0/pow(lutCube,3.0)) * textureColor.r);
    texPos1.y = (quad1.y * 1.0/lutCube) + 0.5/pow(lutCube,3.0) + ((1.0/lutCube - 1.0/pow(lutCube,3.0)) * textureColor.g);
    lowp vec2 texPos2;
    texPos2.x = (quad2.x * 1.0/lutCube) + 0.5/pow(lutCube,3.0) + ((1.0/lutCube - 1.0/pow(lutCube,3.0)) * textureColor.r);
    texPos2.y = (quad2.y * 1.0/lutCube) + 0.5/pow(lutCube,3.0) + ((1.0/lutCube - 1.0/pow(lutCube,3.0)) * textureColor.g);
    lowp vec4 newColor1 = texture2D(vTextureLut, texPos1);
    lowp vec4 newColor2 = texture2D(vTextureLut, texPos2);
    lowp vec4 newColor = mix(newColor1, newColor2, fract(blueColor));
    return mix(textureColor, vec4(newColor.rgb, textureColor.w), lut_intensity);
}

vec2 rotateCoords(vec2 coord, vec2 center, float angle) {
    vec2 rotatedCoord;
    rotatedCoord.x = cos(angle) * (coord.x - center.x) - sin(angle) * (coord.y - center.y) + center.x;
    rotatedCoord.y = sin(angle) * (coord.x - center.x) + cos(angle) * (coord.y - center.y) + center.y;
    return rotatedCoord;
}

vec2 mirrorCoords(vec2 coord) {
    return vec2(1.0 - coord.x, coord.y);
}

void main(){
    highp vec3 centralColor = texture2D(vTextureOrigin, vTextureCoord).rgb;
    vec4 lut_color;
    if(enableLut){
        lut_color = lookup(vec4(centralColor.rgb, 1.0));
    }else{
        lut_color = vec4(centralColor.rgb, 1.0);
    }
    if(vTextureCoord.y<0.0){
        lut_color = vec4(centralColor.rgb, 1.0);
    }
    //    vec4 makeupColor = vec4(makeup(lut_color.rgb),1.0);

    if(hidePort){
        gl_FragColor = vec4(0.0,0.0,0.0,1.0);
    }else{
        gl_FragColor = lut_color;
        // 旋转贴纸纹理坐标
        vec2 rotatedCoord = rotateCoords(vTextureCoord, vec2(0.5, 0.5), radians(0.0));

        vec4 stickerColor = texture2D(vTextureSticker, rotatedCoord); // 获取旋转且镜像后的贴纸颜色
        gl_FragColor = mix(lut_color, stickerColor, stickerColor.a); // 将贴纸叠加到 lut_color 上
    }
}


//const lowp vec3 W = vec3(0.299, 0.587, 0.114);
//const lowp mat3 saturateMatrix = mat3(
//1.1102, -0.0598, -0.061,
//-0.0774, 1.0826, -0.1186,
//-0.0228, -0.0228, 1.1772);
//lowp vec2 blurCoordinates[24];
//
//lowp float hardLight(lowp float color) {
//    if (color <= 0.5)
//    color = color * color * 2.0;
//    else
//    color = 1.0 - ((1.0 - color)*(1.0 - color) * 2.0);
//    return color;
//}
//
//vec3 makeup(in vec3 centralColor){
//    vec2 singleStepOffset=vec2(texelWidthOffset, texelHeightOffset);
//    blurCoordinates[0] = vTextureCoord.xy + singleStepOffset * vec2(0.0, -10.0);
//    blurCoordinates[1] = vTextureCoord.xy + singleStepOffset * vec2(0.0, 10.0);
//    blurCoordinates[2] = vTextureCoord.xy + singleStepOffset * vec2(-10.0, 0.0);
//    blurCoordinates[3] = vTextureCoord.xy + singleStepOffset * vec2(10.0, 0.0);
//    blurCoordinates[4] = vTextureCoord.xy + singleStepOffset * vec2(5.0, -8.0);
//    blurCoordinates[5] = vTextureCoord.xy + singleStepOffset * vec2(5.0, 8.0);
//    blurCoordinates[6] = vTextureCoord.xy + singleStepOffset * vec2(-5.0, 8.0);
//    blurCoordinates[7] = vTextureCoord.xy + singleStepOffset * vec2(-5.0, -8.0);
//    blurCoordinates[8] = vTextureCoord.xy + singleStepOffset * vec2(8.0, -5.0);
//    blurCoordinates[9] = vTextureCoord.xy + singleStepOffset * vec2(8.0, 5.0);
//    blurCoordinates[10] = vTextureCoord.xy + singleStepOffset * vec2(-8.0, 5.0);
//    blurCoordinates[11] = vTextureCoord.xy + singleStepOffset * vec2(-8.0, -5.0);
//    blurCoordinates[12] = vTextureCoord.xy + singleStepOffset * vec2(0.0, -6.0);
//    blurCoordinates[13] = vTextureCoord.xy + singleStepOffset * vec2(0.0, 6.0);
//    blurCoordinates[14] = vTextureCoord.xy + singleStepOffset * vec2(6.0, 0.0);
//    blurCoordinates[15] = vTextureCoord.xy + singleStepOffset * vec2(-6.0, 0.0);
//    blurCoordinates[16] = vTextureCoord.xy + singleStepOffset * vec2(-4.0, -4.0);
//    blurCoordinates[17] = vTextureCoord.xy + singleStepOffset * vec2(-4.0, 4.0);
//    blurCoordinates[18] = vTextureCoord.xy + singleStepOffset * vec2(4.0, -4.0);
//    blurCoordinates[19] = vTextureCoord.xy + singleStepOffset * vec2(4.0, 4.0);
//    blurCoordinates[20] = vTextureCoord.xy + singleStepOffset * vec2(-2.0, -2.0);
//    blurCoordinates[21] = vTextureCoord.xy + singleStepOffset * vec2(-2.0, 2.0);
//    blurCoordinates[22] = vTextureCoord.xy + singleStepOffset * vec2(2.0, -2.0);
//    blurCoordinates[23] = vTextureCoord.xy + singleStepOffset * vec2(2.0, 2.0);
//
//    lowp float sampleColor = centralColor.g * 22.0;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[0]).g;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[1]).g;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[2]).g;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[3]).g;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[4]).g;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[5]).g;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[6]).g;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[7]).g;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[8]).g;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[9]).g;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[10]).g;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[11]).g;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[12]).g * 2.0;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[13]).g * 2.0;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[14]).g * 2.0;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[15]).g * 2.0;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[16]).g * 2.0;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[17]).g * 2.0;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[18]).g * 2.0;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[19]).g * 2.0;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[20]).g * 3.0;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[21]).g * 3.0;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[22]).g * 3.0;
//    sampleColor += texture2D(vTextureOrigin, blurCoordinates[23]).g * 3.0;
//
//    sampleColor = sampleColor / 62.0;
//
//    lowp float highPass = centralColor.g - sampleColor + 0.5;
//
//    for (int i = 0; i < 5; i++) {
//        highPass = hardLight(highPass);
//    }
//    lowp float lumance = dot(centralColor, W);
//
//    lowp float alpha = pow(lumance, params.r);
//
//    lowp vec3 smoothColor = centralColor + (centralColor-vec3(highPass))*alpha*0.1;
//
//    smoothColor.r = clamp(pow(smoothColor.r, params.g), 0.0, 1.0);
//    smoothColor.g = clamp(pow(smoothColor.g, params.g), 0.0, 1.0);
//    smoothColor.b = clamp(pow(smoothColor.b, params.g), 0.0, 1.0);
//
//    lowp vec3 lvse = vec3(1.0)-(vec3(1.0)-smoothColor)*(vec3(1.0)-centralColor);
//    lowp vec3 bianliang = max(smoothColor, centralColor);
//    lowp vec3 rouguang = 2.0*centralColor*smoothColor + centralColor*centralColor - 2.0*centralColor*centralColor*smoothColor;
//
//    vec3 a_FragColor = vec3(mix(centralColor, lvse, alpha));
//    a_FragColor.rgb = mix(a_FragColor.rgb, bianliang, alpha);
//    a_FragColor.rgb = mix(a_FragColor.rgb, rouguang, params.b);
//
//    lowp vec3 satcolor = a_FragColor.rgb * saturateMatrix;
//    a_FragColor.rgb = mix(a_FragColor.rgb, satcolor, params.a);
//    a_FragColor.rgb = vec3(a_FragColor.rgb + vec3(brightness));
//    return smoothColor;
//}