pluginManagement {
    repositories {
        maven { url "https://jitpack.io" }
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven { url "https://jitpack.io" }
        google()
        mavenCentral()
    }
}
include ':module:camera_engine'
include ':module:edit_engine'
rootProject.name = "SmartInspect"
include ':app'

